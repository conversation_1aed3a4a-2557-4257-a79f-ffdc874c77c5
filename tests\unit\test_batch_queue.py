#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理队列管理测试用例

TDD-Red阶段：编写批量处理队列管理的完整测试用例
确保初始状态为FAIL，涵盖队列管理、并发控制、进度跟踪等功能
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import Future

# 将来要实现的批量处理队列组件
from voice_came.core.batch_queue import BatchQueue, ProcessingJob, JobStatus
from voice_came.core.file_processor import FileProcessor
from voice_came.exceptions import QueueError, ProcessingError


class TestBatchQueue:
    """批量处理队列测试类"""
    
    @pytest.fixture
    def batch_queue(self):
        """创建测试用的批量处理队列"""
        return BatchQueue(max_concurrent_jobs=3)
    
    @pytest.fixture
    def mock_processor(self):
        """模拟文件处理器"""
        processor = Mock(spec=FileProcessor)
        processor.process_file = AsyncMock()
        return processor
    
    @pytest.fixture
    def sample_jobs(self, tmp_path):
        """创建测试用的处理任务"""
        jobs = []
        for i in range(5):
            file_path = tmp_path / f"test_file_{i}.mp4"
            file_path.write_bytes(f"test data {i}".encode())
            
            job = ProcessingJob(
                id=f"job_{i}",
                file_path=file_path,
                job_type="speech_recognition",
                priority=1,
                metadata={"user_id": f"user_{i}"}
            )
            jobs.append(job)
        return jobs
    
    # === 队列添加和移除测试 ===
    
    def test_add_job_to_empty_queue(self, batch_queue, sample_jobs):
        """测试向空队列添加任务"""
        job = sample_jobs[0]
        
        job_id = batch_queue.add_job(job)
        
        # 验证任务被添加
        assert job_id == job.id
        assert batch_queue.get_queue_size() == 1
        assert batch_queue.get_job(job_id) == job
        assert job.status == JobStatus.PENDING
    
    def test_add_multiple_jobs_to_queue(self, batch_queue, sample_jobs):
        """测试添加多个任务到队列"""
        job_ids = []
        for job in sample_jobs:
            job_id = batch_queue.add_job(job)
            job_ids.append(job_id)
        
        # 验证所有任务被添加
        assert batch_queue.get_queue_size() == len(sample_jobs)
        assert batch_queue.get_pending_jobs_count() == len(sample_jobs)
        
        # 验证任务顺序
        queue_jobs = batch_queue.get_all_jobs()
        for i, job in enumerate(queue_jobs):
            assert job.id == sample_jobs[i].id
    
    def test_add_duplicate_job_raises_error(self, batch_queue, sample_jobs):
        """测试添加重复任务抛出错误"""
        job = sample_jobs[0]
        
        # 添加第一次
        batch_queue.add_job(job)
        
        # 添加第二次应该抛出错误
        with pytest.raises(QueueError, match="任务ID已存在"):
            batch_queue.add_job(job)
    
    def test_remove_job_from_queue(self, batch_queue, sample_jobs):
        """测试从队列移除任务"""
        job = sample_jobs[0]
        job_id = batch_queue.add_job(job)
        
        # 移除任务
        removed_job = batch_queue.remove_job(job_id)
        
        # 验证任务被移除
        assert removed_job == job
        assert batch_queue.get_queue_size() == 0
        assert batch_queue.get_job(job_id) is None
    
    def test_remove_nonexistent_job_raises_error(self, batch_queue):
        """测试移除不存在的任务抛出错误"""
        with pytest.raises(QueueError, match="任务不存在"):
            batch_queue.remove_job("nonexistent_job_id")
    
    def test_remove_running_job_cancels_it(self, batch_queue, sample_jobs, mock_processor):
        """测试移除正在运行的任务会取消它"""
        batch_queue.set_processor(mock_processor)
        job = sample_jobs[0]
        job_id = batch_queue.add_job(job)
        
        # 开始处理任务
        batch_queue.start_processing()
        
        # 等待任务开始运行
        time.sleep(0.1)
        
        # 移除正在运行的任务
        removed_job = batch_queue.remove_job(job_id)
        
        # 验证任务被移除和取消
        assert removed_job.status == JobStatus.CANCELLED
        assert batch_queue.get_running_jobs_count() == 0
    
    # === 并发控制测试 ===
    
    def test_concurrent_jobs_limit_respected(self, batch_queue, sample_jobs, mock_processor):
        """测试并发任务数量限制被遵守"""
        batch_queue.set_processor(mock_processor)
        
        # 添加5个任务，但最大并发数为3
        for job in sample_jobs:
            batch_queue.add_job(job)
        
        # 开始处理
        batch_queue.start_processing()
        
        # 短暂等待，确保并发限制生效
        time.sleep(0.1)
        
        # 验证并发数量限制
        assert batch_queue.get_running_jobs_count() <= 3
        assert batch_queue.get_pending_jobs_count() >= 2
    
    def test_adjust_concurrent_jobs_limit(self, batch_queue, sample_jobs, mock_processor):
        """测试动态调整并发任务数量限制"""
        batch_queue.set_processor(mock_processor)
        
        # 添加任务并开始处理
        for job in sample_jobs:
            batch_queue.add_job(job)
        batch_queue.start_processing()
        
        # 调整并发限制
        batch_queue.set_max_concurrent_jobs(2)
        
        # 验证新的并发限制生效
        time.sleep(0.1)
        assert batch_queue.get_max_concurrent_jobs() == 2
        assert batch_queue.get_running_jobs_count() <= 2
    
    def test_concurrent_job_completion_starts_next_job(self, batch_queue, sample_jobs):
        """测试并发任务完成后启动下一个任务"""
        # 创建快速完成的处理器
        fast_processor = Mock(spec=FileProcessor)
        fast_processor.process_file = AsyncMock(return_value={"status": "success"})
        batch_queue.set_processor(fast_processor)
        
        # 添加任务
        for job in sample_jobs:
            batch_queue.add_job(job)
        
        # 开始处理
        batch_queue.start_processing()
        
        # 等待所有任务完成
        batch_queue.wait_for_completion(timeout=5.0)
        
        # 验证所有任务都被处理
        assert batch_queue.get_completed_jobs_count() == len(sample_jobs)
        assert batch_queue.get_pending_jobs_count() == 0
        assert batch_queue.get_running_jobs_count() == 0
    
    # === 队列状态管理测试 ===
    
    def test_job_status_transitions(self, batch_queue, sample_jobs, mock_processor):
        """测试任务状态转换"""
        batch_queue.set_processor(mock_processor)
        job = sample_jobs[0]
        job_id = batch_queue.add_job(job)
        
        # 初始状态
        assert job.status == JobStatus.PENDING
        
        # 开始处理
        batch_queue.start_processing()
        time.sleep(0.1)
        
        # 运行状态
        job = batch_queue.get_job(job_id)
        assert job.status == JobStatus.RUNNING
        
        # 完成处理
        mock_processor.process_file.return_value = {"status": "success"}
        batch_queue.wait_for_completion(timeout=1.0)
        
        # 完成状态
        job = batch_queue.get_job(job_id)
        assert job.status == JobStatus.COMPLETED
    
    def test_job_failure_handling(self, batch_queue, sample_jobs):
        """测试任务失败处理"""
        # 创建会失败的处理器
        failing_processor = Mock(spec=FileProcessor)
        failing_processor.process_file = AsyncMock(side_effect=ProcessingError("处理失败"))
        batch_queue.set_processor(failing_processor)
        
        job = sample_jobs[0]
        job_id = batch_queue.add_job(job)
        
        # 开始处理
        batch_queue.start_processing()
        batch_queue.wait_for_completion(timeout=1.0)
        
        # 验证任务失败状态
        job = batch_queue.get_job(job_id)
        assert job.status == JobStatus.FAILED
        assert "处理失败" in job.error_message
        assert batch_queue.get_failed_jobs_count() == 1
    
    def test_queue_pause_and_resume(self, batch_queue, sample_jobs, mock_processor):
        """测试队列暂停和恢复"""
        batch_queue.set_processor(mock_processor)
        
        # 添加任务
        for job in sample_jobs:
            batch_queue.add_job(job)
        
        # 开始处理
        batch_queue.start_processing()
        time.sleep(0.1)
        
        # 暂停队列
        batch_queue.pause_processing()
        
        # 验证暂停状态
        assert batch_queue.is_paused() is True
        initial_running_count = batch_queue.get_running_jobs_count()
        
        # 等待一段时间，确保没有新任务开始
        time.sleep(0.2)
        assert batch_queue.get_running_jobs_count() == initial_running_count
        
        # 恢复处理
        batch_queue.resume_processing()
        
        # 验证恢复状态
        assert batch_queue.is_paused() is False
    
    def test_queue_stop_cancels_all_jobs(self, batch_queue, sample_jobs, mock_processor):
        """测试停止队列取消所有任务"""
        batch_queue.set_processor(mock_processor)
        
        # 添加任务并开始处理
        for job in sample_jobs:
            batch_queue.add_job(job)
        batch_queue.start_processing()
        time.sleep(0.1)
        
        # 停止队列
        batch_queue.stop_processing()
        
        # 验证所有任务被取消
        for job in batch_queue.get_all_jobs():
            assert job.status in [JobStatus.CANCELLED, JobStatus.COMPLETED]
        
        assert batch_queue.is_running() is False
        assert batch_queue.get_running_jobs_count() == 0
    
    # === 进度跟踪测试 ===
    
    def test_job_progress_tracking(self, batch_queue, sample_jobs):
        """测试任务进度跟踪"""
        # 创建支持进度报告的处理器
        progress_processor = Mock(spec=FileProcessor)
        
        async def mock_process_with_progress(file_path, progress_callback=None):
            if progress_callback:
                await progress_callback(0.0, "开始处理")
                await asyncio.sleep(0.1)
                await progress_callback(0.5, "处理中")
                await asyncio.sleep(0.1)
                await progress_callback(1.0, "处理完成")
            return {"status": "success"}
        
        progress_processor.process_file = mock_process_with_progress
        batch_queue.set_processor(progress_processor)
        
        job = sample_jobs[0]
        job_id = batch_queue.add_job(job)
        
        # 设置进度回调
        progress_updates = []
        def progress_callback(progress, message):
            progress_updates.append((progress, message))
        
        batch_queue.set_progress_callback(progress_callback)
        
        # 开始处理
        batch_queue.start_processing()
        batch_queue.wait_for_completion(timeout=2.0)
        
        # 验证进度更新
        assert len(progress_updates) >= 3
        assert progress_updates[0][0] == 0.0
        assert progress_updates[-1][0] == 1.0
    
    def test_overall_queue_progress(self, batch_queue, sample_jobs):
        """测试整体队列进度"""
        # 创建快速处理器
        fast_processor = Mock(spec=FileProcessor)
        fast_processor.process_file = AsyncMock(return_value={"status": "success"})
        batch_queue.set_processor(fast_processor)
        
        # 添加任务
        for job in sample_jobs:
            batch_queue.add_job(job)
        
        total_jobs = len(sample_jobs)
        
        # 开始处理
        batch_queue.start_processing()
        
        # 检查不同阶段的进度
        initial_progress = batch_queue.get_overall_progress()
        assert initial_progress["total_jobs"] == total_jobs
        assert initial_progress["completed_jobs"] == 0
        assert initial_progress["progress_percentage"] == 0.0
        
        # 等待部分完成
        time.sleep(0.2)
        mid_progress = batch_queue.get_overall_progress()
        assert mid_progress["progress_percentage"] > 0.0
        
        # 等待全部完成
        batch_queue.wait_for_completion(timeout=2.0)
        final_progress = batch_queue.get_overall_progress()
        assert final_progress["completed_jobs"] == total_jobs
        assert final_progress["progress_percentage"] == 100.0
    
    # === 队列持久化测试 ===
    
    def test_save_queue_state(self, batch_queue, sample_jobs, tmp_path):
        """测试保存队列状态"""
        # 添加任务
        for job in sample_jobs:
            batch_queue.add_job(job)
        
        # 保存状态
        state_file = tmp_path / "queue_state.json"
        batch_queue.save_state(state_file)
        
        # 验证状态文件存在
        assert state_file.exists()
        
        # 验证状态内容
        saved_state = batch_queue.load_state_from_file(state_file)
        assert len(saved_state["jobs"]) == len(sample_jobs)
        assert saved_state["max_concurrent_jobs"] == 3
    
    def test_load_queue_state(self, tmp_path):
        """测试加载队列状态"""
        # 创建状态文件
        state_file = tmp_path / "queue_state.json"
        
        # 创建第一个队列并保存状态
        queue1 = BatchQueue(max_concurrent_jobs=2)
        
        # 添加任务到第一个队列
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        
        job = ProcessingJob(
            id="test_job",
            file_path=test_file,
            job_type="speech_recognition",
            priority=1
        )
        queue1.add_job(job)
        queue1.save_state(state_file)
        
        # 创建第二个队列并加载状态
        queue2 = BatchQueue()
        queue2.load_state(state_file)
        
        # 验证状态被正确加载
        assert queue2.get_queue_size() == 1
        assert queue2.get_max_concurrent_jobs() == 2
        
        loaded_job = queue2.get_job("test_job")
        assert loaded_job is not None
        assert loaded_job.file_path == test_file
        assert loaded_job.job_type == "speech_recognition"
    
    def test_queue_state_persistence_across_restarts(self, tmp_path):
        """测试队列状态在重启后持久化"""
        state_file = tmp_path / "persistent_queue.json"
        
        # 模拟第一次运行
        queue1 = BatchQueue(auto_save=True, state_file=state_file)
        
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        
        job = ProcessingJob(
            id="persistent_job",
            file_path=test_file,
            job_type="translation",
            priority=2,
            metadata={"user": "test_user"}
        )
        
        queue1.add_job(job)
        
        # 模拟程序关闭
        queue1.shutdown()
        
        # 模拟程序重启
        queue2 = BatchQueue(auto_save=True, state_file=state_file)
        
        # 验证状态自动恢复
        assert queue2.get_queue_size() == 1
        restored_job = queue2.get_job("persistent_job")
        assert restored_job.job_type == "translation"
        assert restored_job.priority == 2
        assert restored_job.metadata["user"] == "test_user"
    
    # === 优先级和调度测试 ===
    
    def test_job_priority_scheduling(self, batch_queue, tmp_path):
        """测试任务优先级调度"""
        # 创建不同优先级的任务
        jobs = []
        priorities = [1, 3, 2, 1, 2]  # 优先级：1=高，2=中，3=低
        
        for i, priority in enumerate(priorities):
            file_path = tmp_path / f"test_{i}.mp4"
            file_path.write_bytes(f"test data {i}".encode())
            
            job = ProcessingJob(
                id=f"job_{i}",
                file_path=file_path,
                job_type="speech_recognition",
                priority=priority
            )
            jobs.append(job)
        
        # 随机顺序添加任务
        for job in jobs:
            batch_queue.add_job(job)
        
        # 获取调度顺序
        scheduled_jobs = batch_queue.get_scheduled_jobs()
        
        # 验证高优先级任务排在前面
        priorities_order = [job.priority for job in scheduled_jobs]
        assert sorted(priorities_order) == priorities_order
    
    def test_fifo_scheduling_within_same_priority(self, batch_queue, tmp_path):
        """测试相同优先级内的FIFO调度"""
        # 创建相同优先级的任务
        jobs = []
        for i in range(5):
            file_path = tmp_path / f"test_{i}.mp4"
            file_path.write_bytes(f"test data {i}".encode())
            
            job = ProcessingJob(
                id=f"job_{i}",
                file_path=file_path,
                job_type="speech_recognition",
                priority=1,  # 所有任务相同优先级
                created_at=time.time() + i  # 不同创建时间
            )
            jobs.append(job)
        
        # 添加任务
        for job in jobs:
            batch_queue.add_job(job)
        
        # 获取调度顺序
        scheduled_jobs = batch_queue.get_scheduled_jobs()
        
        # 验证FIFO顺序
        for i in range(len(scheduled_jobs)):
            assert scheduled_jobs[i].id == f"job_{i}"
    
    # === 错误处理和重试测试 ===
    
    def test_job_retry_on_failure(self, batch_queue, sample_jobs):
        """测试任务失败后重试"""
        # 创建会失败然后成功的处理器
        retry_processor = Mock(spec=FileProcessor)
        call_count = 0
        
        async def mock_process_with_retry(file_path, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ProcessingError("临时错误")
            return {"status": "success"}
        
        retry_processor.process_file = mock_process_with_retry
        batch_queue.set_processor(retry_processor)
        
        job = sample_jobs[0]
        job.max_retries = 2
        job_id = batch_queue.add_job(job)
        
        # 开始处理
        batch_queue.start_processing()
        batch_queue.wait_for_completion(timeout=3.0)
        
        # 验证重试成功
        job = batch_queue.get_job(job_id)
        assert job.status == JobStatus.COMPLETED
        assert job.retry_count == 1
        assert call_count == 2
    
    def test_job_max_retries_exceeded(self, batch_queue, sample_jobs):
        """测试任务重试次数超限"""
        # 创建始终失败的处理器
        failing_processor = Mock(spec=FileProcessor)
        failing_processor.process_file = AsyncMock(side_effect=ProcessingError("持续错误"))
        batch_queue.set_processor(failing_processor)
        
        job = sample_jobs[0]
        job.max_retries = 2
        job_id = batch_queue.add_job(job)
        
        # 开始处理
        batch_queue.start_processing()
        batch_queue.wait_for_completion(timeout=3.0)
        
        # 验证最终失败
        job = batch_queue.get_job(job_id)
        assert job.status == JobStatus.FAILED
        assert job.retry_count == 2
        assert "持续错误" in job.error_message
    
    # === 性能和内存管理测试 ===
    
    @pytest.mark.slow
    def test_large_queue_performance(self, tmp_path):
        """测试大型队列性能"""
        batch_queue = BatchQueue(max_concurrent_jobs=5)
        
        # 创建大量任务
        job_count = 100
        jobs = []
        
        for i in range(job_count):
            file_path = tmp_path / f"test_{i}.mp4"
            file_path.write_bytes(b"test data")
            
            job = ProcessingJob(
                id=f"job_{i}",
                file_path=file_path,
                job_type="speech_recognition",
                priority=1
            )
            jobs.append(job)
        
        # 测试添加性能
        start_time = time.time()
        for job in jobs:
            batch_queue.add_job(job)
        add_time = time.time() - start_time
        
        # 验证添加性能合理 (应该很快)
        assert add_time < 1.0  # 少于1秒
        assert batch_queue.get_queue_size() == job_count
        
        # 测试查询性能
        start_time = time.time()
        for i in range(0, job_count, 10):
            job = batch_queue.get_job(f"job_{i}")
            assert job is not None
        query_time = time.time() - start_time
        
        # 验证查询性能合理
        assert query_time < 0.1  # 少于100ms
    
    def test_memory_cleanup_after_completion(self, batch_queue, sample_jobs):
        """测试完成后的内存清理"""
        # 创建快速处理器
        fast_processor = Mock(spec=FileProcessor)
        fast_processor.process_file = AsyncMock(return_value={"status": "success"})
        batch_queue.set_processor(fast_processor)
        
        # 添加任务
        for job in sample_jobs:
            batch_queue.add_job(job)
        
        initial_memory = batch_queue.get_memory_usage()
        
        # 处理所有任务
        batch_queue.start_processing()
        batch_queue.wait_for_completion(timeout=2.0)
        
        # 清理完成的任务
        batch_queue.cleanup_completed_jobs()
        
        final_memory = batch_queue.get_memory_usage()
        
        # 验证内存被释放
        assert final_memory < initial_memory
        assert batch_queue.get_completed_jobs_count() == 0
        assert batch_queue.get_queue_size() == 0