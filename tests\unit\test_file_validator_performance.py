#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 2.3: 文件验证功能性能测试和边界测试

重构优化阶段新增测试：
- 性能基准测试
- 并发验证测试
- 边界条件测试
- 内存使用测试
- 错误恢复测试
"""

import pytest
import time
import threading
import tempfile
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock, patch
import gc
import psutil
import os

from voice_came.core.file_validator import FileValidator, ValidationResult, FileValidationError


class TestFileValidatorPerformance:
    """文件验证器性能测试类"""
    
    @pytest.fixture
    def performance_validator(self):
        """性能测试用的验证器"""
        config = {
            'max_file_size_gb': 4.0,
            'check_file_header': False,  # 禁用文件头检查以避免测试失败
            'max_workers': 4,
            'enable_performance_tracking': True
        }
        return FileValidator(config=config)
    
    @pytest.fixture
    def large_file_batch(self, tmp_path):
        """创建大批量文件用于性能测试"""
        files = []
        formats = ['mp4', 'avi', 'mov', 'wav', 'mp3'] * 20  # 100个文件
        
        for i, fmt in enumerate(formats):
            file_path = tmp_path / f"test_{i}.{fmt}"
            # 创建不同大小的文件
            size = (i % 10 + 1) * 1024  # 1KB到10KB
            file_path.write_bytes(b"fake data" * size)
            files.append(file_path)
        
        return files
    
    @pytest.mark.performance
    def test_single_file_validation_speed(self, performance_validator, tmp_path):
        """测试单文件验证速度"""
        test_file = tmp_path / "speed_test.mp4"
        test_file.write_bytes(b"fake mp4 data" * 1024)  # 约13KB
        
        start_time = time.time()
        result = performance_validator.validate_file(test_file)
        elapsed_time = time.time() - start_time
        
        # 性能断言
        assert result.is_valid is True
        assert elapsed_time < 0.1  # 验证时间应小于100ms
    
    @pytest.mark.performance
    def test_batch_validation_speed(self, performance_validator, large_file_batch):
        """测试批量验证速度"""
        start_time = time.time()
        results = performance_validator.batch_validate(large_file_batch)
        elapsed_time = time.time() - start_time
        
        # 性能断言
        assert len(results) == len(large_file_batch)
        assert all(r.is_valid for r in results)
        assert elapsed_time < 5.0  # 100个文件验证应在5秒内完成
    
    @pytest.mark.performance
    def test_concurrent_validation_safety(self, performance_validator, large_file_batch):
        """测试并发验证的线程安全性"""
        results = []
        exceptions = []
        
        def validate_subset(files_subset):
            try:
                subset_results = performance_validator.batch_validate(files_subset)
                results.extend(subset_results)
            except Exception as e:
                exceptions.append(e)
        
        # 将文件分为4个子集，并发验证
        batch_size = len(large_file_batch) // 4
        batches = [
            large_file_batch[i:i + batch_size] 
            for i in range(0, len(large_file_batch), batch_size)
        ]
        
        # 启动并发验证
        threads = []
        for batch in batches:
            thread = threading.Thread(target=validate_subset, args=(batch,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(exceptions) == 0, f"并发验证出现异常: {exceptions}"
        assert len(results) == len(large_file_batch)
    
    @pytest.mark.performance
    def test_memory_usage_under_load(self, performance_validator, tmp_path):
        """测试大负载下的内存使用情况"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # 创建大量文件
        large_files = []
        for i in range(500):  # 500个文件
            file_path = tmp_path / f"memory_test_{i}.mp4"
            file_path.write_bytes(b"test data" * 100)  # 约900B每个
            large_files.append(file_path)
        
        # 批量验证
        results = performance_validator.batch_validate(large_files)
        
        peak_memory = process.memory_info().rss
        memory_increase = peak_memory - initial_memory
        
        # 内存增长应该合理（小于50MB）
        assert memory_increase < 50 * 1024 * 1024
        assert len(results) == 500
        
        # 强制垃圾回收
        del results
        del large_files
        gc.collect()
        
        final_memory = process.memory_info().rss
        memory_cleanup = peak_memory - final_memory
        
        # 验证内存能够有效回收
        assert memory_cleanup > 0
    
    @pytest.mark.performance
    def test_validation_with_progress_tracking(self, performance_validator, large_file_batch):
        """测试带进度跟踪的验证性能"""
        progress_calls = []
        
        def progress_callback(current, total, filename):
            progress_calls.append((current, total, filename))
        
        start_time = time.time()
        results = performance_validator.batch_validate(
            large_file_batch[:50], 
            progress_callback=progress_callback
        )
        elapsed_time = time.time() - start_time
        
        # 验证进度跟踪
        assert len(progress_calls) == 50
        assert progress_calls[-1][0] == 50  # 最后一次调用应该是100%
        assert all(call[1] == 50 for call in progress_calls)  # 总数应该一致
        
        # 性能应该合理（带进度跟踪的开销应该很小）
        assert elapsed_time < 1.0
        assert len(results) == 50


class TestFileValidatorBoundaryConditions:
    """文件验证器边界条件测试类"""
    
    @pytest.fixture
    def boundary_validator(self):
        """边界测试用的验证器"""
        return FileValidator({
            'max_file_size_gb': 0.001,  # 1MB限制用于测试
            'check_file_header': False  # 禁用文件头检查
        })
    
    @pytest.mark.boundary
    def test_exact_size_boundary(self, boundary_validator, tmp_path):
        """测试精确文件大小边界"""
        # 创建正好1MB的文件
        exact_size_file = tmp_path / "exact_1mb.mp4"
        exact_size_file.write_bytes(b"x" * (1024 * 1024))  # 正好1MB
        
        result = boundary_validator.validate_file(exact_size_file)
        assert result.is_valid is True  # 正好等于限制应该通过
        
        # 创建超过1字节的文件
        over_size_file = tmp_path / "over_1mb.mp4"
        over_size_file.write_bytes(b"x" * (1024 * 1024 + 1))  # 超过1字节
        
        result = boundary_validator.validate_file(over_size_file)
        assert result.is_valid is False
        assert "文件大小超过限制" in result.errors[0]
    
    @pytest.mark.boundary
    def test_zero_size_file(self, boundary_validator, tmp_path):
        """测试零字节文件"""
        zero_file = tmp_path / "zero.mp4"
        zero_file.write_bytes(b"")
        
        result = boundary_validator.validate_file(zero_file)
        assert result.is_valid is False
        assert "文件为空" in result.errors
    
    @pytest.mark.boundary
    def test_single_byte_file(self, boundary_validator, tmp_path):
        """测试单字节文件"""
        tiny_file = tmp_path / "tiny.mp4"
        tiny_file.write_bytes(b"x")
        
        result = boundary_validator.validate_file(tiny_file)
        # 单字节文件应该通过大小检查，但可能在文件头检查失败
        assert result.file_size == 1
    
    @pytest.mark.boundary
    def test_empty_batch_validation(self, boundary_validator):
        """测试空批量验证"""
        results = boundary_validator.batch_validate([])
        assert results == []
    
    @pytest.mark.boundary
    def test_single_file_batch(self, boundary_validator, tmp_path):
        """测试单文件批量验证"""
        single_file = tmp_path / "single.mp4"
        single_file.write_bytes(b"test data")
        
        results = boundary_validator.batch_validate([single_file])
        assert len(results) == 1
        assert results[0].is_valid is True
    
    @pytest.mark.boundary
    def test_very_long_filename(self, boundary_validator, tmp_path):
        """测试非常长的文件名"""
        # 创建长文件名（接近系统限制）
        long_name = "a" * 200 + ".mp4"
        long_file = tmp_path / long_name
        
        try:
            long_file.write_bytes(b"test data")
            result = boundary_validator.validate_file(long_file)
            # 如果文件创建成功，验证应该正常进行
            assert hasattr(result, 'is_valid')
        except OSError:
            # 如果系统不支持这么长的文件名，跳过测试
            pytest.skip("系统不支持长文件名")
    
    @pytest.mark.boundary
    def test_unicode_filename(self, boundary_validator, tmp_path):
        """测试Unicode文件名"""
        unicode_file = tmp_path / "测试文件_🎵.mp4"
        unicode_file.write_bytes(b"test data")
        
        result = boundary_validator.validate_file(unicode_file)
        assert result.is_valid is True
        assert "测试文件_🎵.mp4" in str(result.file_path)
    
    @pytest.mark.boundary
    def test_special_characters_in_path(self, boundary_validator, tmp_path):
        """测试路径中的特殊字符"""
        special_chars = ['&', '%', '$', '#', '!', '(', ')', '[', ']']
        
        for char in special_chars:
            if char not in ['/', '\\', ':', '*', '?', '"', '<', '>', '|']:  # 避免非法字符
                try:
                    special_file = tmp_path / f"test{char}file.mp4"
                    special_file.write_bytes(b"test data")
                    
                    result = boundary_validator.validate_file(special_file)
                    assert hasattr(result, 'is_valid')
                except (OSError, ValueError):
                    # 某些特殊字符可能不被系统支持
                    continue


class TestFileValidatorErrorRecovery:
    """文件验证器错误恢复测试类"""
    
    @pytest.fixture
    def error_recovery_validator(self):
        """错误恢复测试用验证器"""
        return FileValidator({
            'max_file_size_gb': 4.0,
            'check_file_header': True,
            'max_workers': 2
        })
    
    @pytest.mark.error_recovery
    def test_permission_denied_recovery(self, error_recovery_validator, tmp_path):
        """测试权限拒绝错误的恢复"""
        test_file = tmp_path / "permission_test.mp4"
        test_file.write_bytes(b"test data")
        
        # 模拟权限错误
        with patch('pathlib.Path.stat') as mock_stat:
            mock_stat.side_effect = PermissionError("Access denied")
            
            result = error_recovery_validator.validate_file(test_file)
            assert result.is_valid is False
            assert "无法读取文件信息" in result.errors[0]
    
    @pytest.mark.error_recovery
    def test_file_not_found_during_validation(self, error_recovery_validator, tmp_path):
        """测试验证过程中文件被删除的情况"""
        test_file = tmp_path / "disappearing_file.mp4"
        test_file.write_bytes(b"test data")
        
        # 模拟文件在验证过程中消失
        with patch('pathlib.Path.exists') as mock_exists:
            mock_exists.return_value = False
            
            result = error_recovery_validator.validate_file(test_file)
            assert result.is_valid is False
            assert "文件不存在" in result.errors
    
    @pytest.mark.error_recovery
    def test_corrupted_file_header_recovery(self, error_recovery_validator, tmp_path):
        """测试文件头读取错误的恢复"""
        test_file = tmp_path / "corrupted_header.mp4"
        test_file.write_bytes(b"test data")
        
        # 模拟文件头读取错误
        with patch('builtins.open') as mock_open:
            mock_open.side_effect = IOError("Disk error")
            
            result = error_recovery_validator.validate_file(test_file)
            # 文件头检查失败应该返回失败结果
            assert result.is_valid is False
    
    @pytest.mark.error_recovery
    def test_batch_validation_partial_failure(self, error_recovery_validator, tmp_path):
        """测试批量验证中部分文件失败的恢复"""
        # 创建一些正常文件和一些问题文件
        good_files = []
        
        for i in range(3):
            good_file = tmp_path / f"good_{i}.mp4"
            good_file.write_bytes(b"good data")
            good_files.append(good_file)
        
        # 添加不存在的文件
        bad_files = [tmp_path / "nonexistent.mp4"]
        
        all_files = good_files + bad_files
        
        results = error_recovery_validator.batch_validate(all_files)
        
        # 验证结果
        assert len(results) == 4
        good_results = results[:3]
        bad_results = results[3:]
        
        assert all(r.is_valid for r in good_results)
        assert not any(r.is_valid for r in bad_results)
    
    @pytest.mark.error_recovery
    def test_out_of_memory_simulation(self, error_recovery_validator, tmp_path):
        """测试内存不足情况的处理"""
        test_file = tmp_path / "memory_test.mp4"
        test_file.write_bytes(b"test data")
        
        # 模拟内存错误
        with patch('voice_came.core.file_validator.FileValidator._validate_single_file') as mock_validate:
            mock_validate.side_effect = MemoryError("Out of memory")
            
            result = error_recovery_validator.validate_file(test_file)
            assert result.is_valid is False
            assert "验证过程中发生未预期错误" in result.errors[0]


class TestFileValidatorConfigurationEdgeCases:
    """验证器配置边界情况测试"""
    
    @pytest.mark.configuration
    def test_extreme_small_file_size_limit(self):
        """测试极小的文件大小限制"""
        validator = FileValidator({'max_file_size_gb': 0.0000001})  # 约100字节
        assert validator.MAX_FILE_SIZE < 1000
    
    @pytest.mark.configuration
    def test_extreme_large_file_size_limit(self):
        """测试极大的文件大小限制"""
        validator = FileValidator({'max_file_size_gb': 1000})  # 1TB
        assert validator.MAX_FILE_SIZE > 1000 * 1024 * 1024 * 1024
    
    @pytest.mark.configuration
    def test_zero_workers_configuration(self):
        """测试零工作线程配置"""
        validator = FileValidator({'max_workers': 0})
        # 应该回退到默认值
        assert validator.max_workers > 0
    
    @pytest.mark.configuration
    def test_negative_workers_configuration(self):
        """测试负数工作线程配置"""
        validator = FileValidator({'max_workers': -1})
        # 应该回退到默认值
        assert validator.max_workers > 0
    
    @pytest.mark.configuration
    def test_empty_supported_formats(self):
        """测试空的支持格式列表"""
        validator = FileValidator({'supported_formats': []})
        assert len(validator.SUPPORTED_FORMATS) == 0
    
    @pytest.mark.configuration
    def test_invalid_configuration_type(self):
        """测试无效的配置类型"""
        # 传入字符串而不是字典
        validator = FileValidator("invalid_config")
        # 应该使用默认配置
        assert hasattr(validator, 'MAX_FILE_SIZE') 