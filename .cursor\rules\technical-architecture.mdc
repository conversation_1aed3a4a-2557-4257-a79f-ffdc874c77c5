---
description: 
globs: 
alwaysApply: false
---
# Voice-came Technical Architecture

## System Overview
Voice-came is a sleep content globalization tool that:
- Extracts 15-60 minutes of effective speech from 3-12 hour videos
- Translates content into 5 core languages (Chinese, English, Spanish, French, German)
- Uses WhisperX for speech processing and local translation models (Gemma3-12B, Qwen3)
- Achieves 95% time and cost savings compared to manual processing

## Core Architecture Components

### Speech Processing Pipeline
```
Video Input → Audio Extraction → WhisperX Processing → Speech Segmentation → Translation → Output
```

### Key Modules

#### `voice_came/core/whisperx_engine.py`
WhisperX integration for speech recognition with:
- 70x real-time speed processing
- Word-level timestamps with wav2vec2 alignment
- Voice Activity Detection (VAD) for noise reduction
- Batch processing optimization

#### `voice_came/core/batch_processor.py`
Handles batch video processing:
- Queue management for 3-5 concurrent videos
- Progress tracking and error recovery
- Processing state management
- Resource optimization

#### `voice_came/engines/translation/`
Local translation engines:
- **Gemma Translator**: `gemma_translator.py` - Gemma3-12B-Q4 model
- **Qwen Translator**: `qwen_translator.py` - Qwen3 model
- API fallback to existing VoiceTransl services

#### `voice_came/translation/` 
Translation module with layered architecture:
- **Data Models**: `models.py` - Translation request/response models, terminology definitions
- **Business Logic**: `business.py` - Core translation workflows, terminology management
- **System Integration**: `integration.py` - VoiceTransl adapter, external system interfaces

### WhisperX Integration Details

#### Configuration (`voice_came/config/whisperx_config.yaml`)
```yaml
whisperx:
  model: "large-v2"
  device: "cuda"
  compute_type: "float16"
  batch_size: 16
  vad_filter: true
  word_timestamps: true
  supported_languages: ["auto", "zh", "en", "es", "fr", "de"]
```

#### Processing Pipeline
1. **Audio Preprocessing**: Extract audio from video files
2. **VAD Processing**: Detect speech segments, filter silence/music
3. **Batch Transcription**: Process multiple segments efficiently
4. **Alignment**: Generate word-level timestamps
5. **Output**: Generate SRT subtitles with precise timing

### Translation Integration Architecture

#### Interface Contract Design
```python
# Standardized translation interface
from voice_came.translation.models import TranslationRequest, TranslationResponse

class TranslationInterface:
    """Standard interface for all translation engines"""
    
    def translate(self, request: TranslationRequest) -> TranslationResponse:
        """Execute translation with standardized input/output"""
        pass
    
    def is_available(self) -> bool:
        """Check if translation engine is ready"""
        pass
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported language pairs"""
        pass
```

#### VoiceTransl Integration Strategy
```python
# voice_came/translation/integration.py
class VoiceTranslAdapter:
    """Adapter for integrating with existing VoiceTransl system"""
    
    def execute_translation(self, text: str, source_lang: str, target_lang: str) -> dict:
        """Execute translation through VoiceTransl system"""
        # Leverage existing VoiceTransl translation pipeline
        # Maintain compatibility with current workflows
        # Provide fallback mechanism for local models
        pass
        
    def get_terminology_database(self) -> dict:
        """Access VoiceTransl's terminology management"""
        pass
```

### Translation Workflow

#### Enhanced Processing Strategy
```python
# Translation engine priority and fallback chain
class TranslationManager:
    def __init__(self):
        self.engines = [
            LocalGemmaEngine(),      # Primary: Local Gemma3-12B
            LocalQwenEngine(),       # Secondary: Local Qwen3
            VoiceTranslAdapter(),    # Fallback: Existing VoiceTransl system
            OnlineAPIEngine()        # Emergency: Online APIs
        ]
    
    def translate_with_fallback(self, request: TranslationRequest) -> TranslationResponse:
        """Execute translation with automatic fallback"""
        for engine in self.engines:
            if engine.is_available():
                try:
                    return engine.translate(request)
                except Exception as e:
                    logger.warning(f"Engine {engine.__class__.__name__} failed: {e}")
                    continue
        
        raise TranslationError("All translation engines failed")
```

#### Terminology Management Integration
- **VoiceTransl Compatibility**: Leverage existing terminology databases
- **100-word Core Dictionary**: Sleep-specific terminology with automatic replacement
- **Bidirectional Sync**: Keep terminology consistent between systems
- **Conflict Resolution**: Handle terminology conflicts with priority rules

### Interface Contract Validation
```python
# Contract testing for translation engines
def test_translation_interface_contract(engine: TranslationInterface):
    """Validate translation engine follows interface contract"""
    request = TranslationRequest(
        text="Hello world",
        source_language="en",
        target_language="zh"
    )
    
    response = engine.translate(request)
    
    # Validate contract compliance
    assert isinstance(response, TranslationResponse)
    assert response.translated_text is not None
    assert response.confidence_score >= 0.0
    assert response.success is True
```

## Performance Requirements

### Speed Targets
- 3-hour video processing: ≤ 30 minutes
- Real-time factor: 3-5x faster than audio length
- Concurrent processing: 3-5 videos simultaneously

### Resource Requirements
- **GPU Memory**: < 8GB for large-v2 model
- **System Memory**: 16GB recommended
- **Storage**: Temporary space for video processing

### Quality Metrics
- **Speech Recognition Accuracy**: > 90% for clear audio
- **Translation Quality**: 90% terminology accuracy
- **Processing Success Rate**: > 95% for supported formats

## File Processing Workflow

### Input Stage
- Support formats: MP4, AVI, MOV, MP3, WAV, M4A, FLAC
- File validation and format checking
- Batch import with drag-and-drop interface

### Processing Stage
```python
# Main processing pipeline
def process_video_batch(video_paths: List[str]) -> Dict[str, dict]:
    results = {}
    for video_path in video_paths:
        # 1. Audio extraction
        audio_path = extract_audio(video_path)
        
        # 2. WhisperX processing
        transcription = whisperx_engine.transcribe(audio_path)
        
        # 3. Translation
        translations = translation_manager.translate_batch(
            transcription, target_languages=["en", "es", "fr", "de"]
        )
        
        # 4. Terminology processing
        final_translations = terminology_processor.apply_terms(translations)
        
        results[video_path] = {
            "transcription": transcription,
            "translations": final_translations,
            "metadata": extract_metadata(video_path)
        }
    
    return results
```

### Output Stage
- **File Organization**: Auto-create folders by video title
- **Format Support**: SRT, TXT, JSON export
- **Multi-language**: Original + translated subtitle files
- **Batch Export**: Process multiple results simultaneously

## Integration with VoiceTransl

### Architecture Compatibility
- Extends existing VoiceTransl framework
- Maintains API compatibility
- Preserves configuration system
- Adds WhisperX as additional engine option

### Migration Strategy
- Gradual integration without breaking existing functionality
- Feature flags for WhisperX vs traditional Whisper
- Fallback mechanisms for error recovery

## Error Handling and Recovery

### Robust Error Management
```python
# Error handling hierarchy
try:
    result = whisperx_processing(audio_file)
except WhisperXError:
    # Fallback to faster-whisper
    result = faster_whisper_processing(audio_file)
except TranslationError:
    # Fallback to online API
    result = online_api_translation(text)
except Exception as e:
    # Log and graceful degradation
    logger.error(f"Processing failed: {e}")
    return fallback_result
```

### Recovery Mechanisms
- **Checkpoint/Resume**: Save processing state for large batches
- **Partial Results**: Return completed translations even if some fail
- **Resource Management**: Automatic cleanup of temporary files
- **Progress Persistence**: Maintain progress across application restarts

## Monitoring and Optimization

### Performance Monitoring
- Real-time processing speed tracking
- Memory usage monitoring
- GPU utilization metrics
- Error rate statistics

### Optimization Features
- **Dynamic Batch Sizing**: Adjust based on available resources
- **Model Quantization**: Int8/Float16 precision options
- **Memory Management**: Efficient large file handling
- **Concurrent Processing**: Multi-video parallel processing
