---
description:
globs:
alwaysApply: false
---
# Voice-came Coding Standards

## Code Quality Standards
- **Cyclomatic Complexity**: ≤ 10
- **Function Length**: ≤ 50 lines
- **Class Length**: ≤ 500 lines
- **Nesting Depth**: ≤ 4 levels
- **Test Coverage**: ≥ 80%

## Python Code Standards (PEP 8)

### File Header Template
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice-came [Module Name]

Brief description of module functionality.
Detailed description of key features and usage.

Author: Voice-came Team
Created: 2025-01-XX
Version: 1.0.0
"""

import os
import sys
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path

# Third-party imports
import torch
import whisperx

# Local imports
from voice_came.core.base_engine import BaseEngine
from voice_came.utils.audio_utils import AudioProcessor
```

### Function Documentation Template
```python
def process_batch_videos(
    video_paths: List[str],
    output_dir: str,
    whisperx_model: str = "large-v2",
    target_languages: List[str] = None,
    max_concurrent: int = 3
) -> Dict[str, dict]:
    """
    Process multiple video files for speech recognition and translation
    
    Args:
        video_paths: List of video file paths
        output_dir: Output directory path
        whisperx_model: WhisperX model name
        target_languages: Target translation languages list
        max_concurrent: Maximum concurrent processing count
    
    Returns:
        Dict[str, dict]: Processing results dictionary
    
    Raises:
        ValueError: When input parameters are invalid
        FileNotFoundError: When video files don't exist
        ProcessingError: When processing fails
    
    Example:
        >>> results = process_batch_videos(
        ...     video_paths=["/path/to/video1.mp4"],
        ...     output_dir="/path/to/output",
        ...     target_languages=["en", "zh", "ja"]
        ... )
    """
```

### Error Handling Pattern
```python
def process_audio_file(audio_path: str) -> dict:
    """Process audio file with proper error handling"""
    try:
        # Input validation
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        # Main processing
        result = self._do_process(audio_path)
        return result
        
    except FileNotFoundError:
        logger.error(f"File not found: {audio_path}")
        raise
    except Exception as e:
        logger.error(f"Audio processing failed: {audio_path}, error: {str(e)}")
        raise ProcessingError(f"Audio processing failed: {str(e)}") from e
```

### Logging Standards
```python
import logging
from voice_came.utils.logger import get_logger

logger = get_logger(__name__)

# INFO: Key operations
logger.info(f"Starting video processing: {video_path}")

# DEBUG: Debug information
logger.debug(f"Using model: {model_name}, device: {device}")

# WARNING: Warning messages
logger.warning(f"Large video file: {file_size}MB, may take longer")

# ERROR: Error messages
logger.error(f"Processing failed: {video_path}, error: {str(e)}", exc_info=True)
```

### Performance Monitoring
```python
@monitor_performance
def process_large_video(video_path: str) -> dict:
    """Process large video file with performance monitoring"""
    # Processing logic...
    pass
```

## Code Formatting Tools
- **black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking

## Git Commit Standards
```
<type>(<scope>): <subject>

<body>

<footer>

# Types:
feat:     New feature
fix:      Bug fix
docs:     Documentation
style:    Code formatting
refactor: Code refactoring
test:     Testing
chore:    Build/tools
```

## Quality Gates
- All tests must pass
- Code coverage ≥ 80%
- No linting errors
- Type hints required for public APIs
- Docstrings required for all public functions/classes
