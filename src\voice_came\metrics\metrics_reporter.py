"""
TDD度量报告生成器

生成各种格式的TDD度量报告
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from jinja2 import Template
import matplotlib.pyplot as plt
import pandas as pd


class MetricsReporter:
    """TDD度量报告生成器"""
    
    def __init__(self, metrics_dir: str = "metrics"):
        self.metrics_dir = Path(metrics_dir)
        self.metrics_dir.mkdir(exist_ok=True)
        
        self.reports_dir = self.metrics_dir / "reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
    
    def generate_daily_report(self, metrics_data: Dict[str, Any], date: str = None) -> Dict[str, Any]:
        """生成日报"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        
        try:
            report = {
                'report_type': 'daily',
                'date': date,
                'summary': self._generate_summary(metrics_data),
                'coverage_analysis': self._analyze_coverage_trends(metrics_data),
                'quality_analysis': self._analyze_quality_trends(metrics_data),
                'performance_analysis': self._analyze_performance_trends(metrics_data),
                'compliance_analysis': self._analyze_compliance_trends(metrics_data),
                'alerts': self._generate_alerts(metrics_data),
                'recommendations': self._generate_daily_recommendations(metrics_data)
            }
            
            # 保存日报
            report_file = self.reports_dir / f"daily_report_{date}.json"
            self._save_report(report, report_file)
            
            # 生成HTML报告
            self._generate_html_report(report, f"daily_report_{date}.html")
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成日报时出错: {e}")
            return {'error': '无法生成日报'}
    
    def generate_weekly_report(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """生成周报"""
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
        
        try:
            # 收集一周的数据
            weekly_data = self._collect_weekly_data(start_date, end_date)
            
            report = {
                'report_type': 'weekly',
                'start_date': start_date,
                'end_date': end_date,
                'summary': self._generate_weekly_summary(weekly_data),
                'trends': self._analyze_weekly_trends(weekly_data),
                'achievements': self._identify_achievements(weekly_data),
                'issues': self._identify_issues(weekly_data),
                'goals': self._set_weekly_goals(weekly_data),
                'recommendations': self._generate_weekly_recommendations(weekly_data)
            }
            
            # 保存周报
            report_file = self.reports_dir / f"weekly_report_{start_date}_to_{end_date}.json"
            self._save_report(report, report_file)
            
            # 生成图表
            self._generate_weekly_charts(weekly_data, start_date, end_date)
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成周报时出错: {e}")
            return {'error': '无法生成周报'}
    
    def generate_dashboard_data(self) -> Dict[str, Any]:
        """生成实时仪表盘数据"""
        try:
            # 从各个度量文件加载最新数据
            latest_metrics = self._load_latest_metrics()
            
            dashboard = {
                'timestamp': datetime.now().isoformat(),
                'overview': self._generate_overview(latest_metrics),
                'coverage_widget': self._generate_coverage_widget(latest_metrics),
                'quality_widget': self._generate_quality_widget(latest_metrics),
                'performance_widget': self._generate_performance_widget(latest_metrics),
                'compliance_widget': self._generate_compliance_widget(latest_metrics),
                'recent_activities': self._get_recent_activities(),
                'alerts': self._get_active_alerts(latest_metrics),
                'quick_stats': self._generate_quick_stats(latest_metrics)
            }
            
            # 保存仪表盘数据
            dashboard_file = self.metrics_dir / "dashboard_data.json"
            self._save_report(dashboard, dashboard_file)
            
            return dashboard
            
        except Exception as e:
            self.logger.error(f"生成仪表盘数据时出错: {e}")
            return {'error': '无法生成仪表盘数据'}
    
    def _generate_summary(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成度量摘要"""
        current_metrics = metrics_data.get('current_metrics', {})
        
        return {
            'test_coverage': {
                'current': current_metrics.get('test_coverage', 0),
                'target': 0.90,
                'status': 'good' if current_metrics.get('test_coverage', 0) >= 0.90 else 'needs_improvement'
            },
            'code_quality': {
                'score': current_metrics.get('code_quality_score', 0),
                'rating': self._rate_quality_score(current_metrics.get('code_quality_score', 0))
            },
            'tdd_compliance': {
                'rate': current_metrics.get('compliance_rate', 0),
                'status': 'compliant' if current_metrics.get('compliance_rate', 0) >= 0.80 else 'non_compliant'
            },
            'performance': {
                'score': current_metrics.get('performance_score', 0),
                'rating': self._rate_performance_score(current_metrics.get('performance_score', 0))
            }
        }
    
    def _analyze_coverage_trends(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析覆盖率趋势"""
        trend_data = metrics_data.get('trend_analysis', {})
        
        return {
            'trend_direction': 'improving' if trend_data.get('trends', {}).get('coverage_change', 0) > 0 else 'declining',
            'change_amount': trend_data.get('trends', {}).get('coverage_change', 0),
            'current_status': trend_data.get('current_status', {}),
            'recommendations': ['维持良好的测试覆盖率', '继续完善边界测试用例']
        }
    
    def _analyze_quality_trends(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析质量趋势"""
        trend_data = metrics_data.get('trend_analysis', {})
        
        return {
            'trend_direction': 'improving' if trend_data.get('trends', {}).get('quality_change', 0) > 0 else 'stable',
            'change_amount': trend_data.get('trends', {}).get('quality_change', 0),
            'focus_areas': ['代码复杂度', '安全性检查', '风格规范']
        }
    
    def _analyze_performance_trends(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析性能趋势"""
        current_metrics = metrics_data.get('current_metrics', {})
        
        return {
            'current_score': current_metrics.get('performance_score', 0),
            'optimization_areas': ['测试执行速度', '内存使用优化', 'CI/CD管道效率']
        }
    
    def _analyze_compliance_trends(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析合规性趋势"""
        current_metrics = metrics_data.get('current_metrics', {})
        
        return {
            'compliance_rate': current_metrics.get('compliance_rate', 0),
            'key_issues': ['测试先行实践', '代码审查流程', '文档完整性']
        }
    
    def _generate_alerts(self, metrics_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成警报"""
        alerts = metrics_data.get('alerts', [])
        
        # 添加趋势警报
        trend_data = metrics_data.get('trend_analysis', {})
        trends = trend_data.get('trends', {})
        
        if trends.get('coverage_change', 0) < -0.05:
            alerts.append({
                'level': 'warning',
                'type': 'coverage_decline',
                'message': '测试覆盖率呈下降趋势',
                'action': '检查新增代码的测试覆盖情况'
            })
        
        return alerts
    
    def _generate_daily_recommendations(self, metrics_data: Dict[str, Any]) -> List[str]:
        """生成日常建议"""
        recommendations = metrics_data.get('recommendations', [])
        
        # 基于当前状态添加建议
        current_metrics = metrics_data.get('current_metrics', {})
        
        if current_metrics.get('test_coverage', 0) < 0.95:
            recommendations.append('建议为核心模块增加更多测试用例')
        
        if current_metrics.get('rework_rate', 0) > 0.1:
            recommendations.append('建议加强代码审查，减少返工')
        
        return recommendations
    
    def _collect_weekly_data(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """收集一周的数据"""
        weekly_data = []
        
        # 这里应该从历史文件中读取数据
        # 简化实现，返回模拟数据
        for i in range(7):
            date = datetime.strptime(start_date, "%Y-%m-%d") + timedelta(days=i)
            if date <= datetime.strptime(end_date, "%Y-%m-%d"):
                weekly_data.append({
                    'date': date.strftime("%Y-%m-%d"),
                    'test_coverage': 0.89 + (i * 0.01),
                    'code_quality_score': 85 + i,
                    'compliance_rate': 0.80 + (i * 0.02),
                    'performance_score': 88 + i
                })
        
        return weekly_data
    
    def _generate_weekly_summary(self, weekly_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成周度摘要"""
        if not weekly_data:
            return {}
        
        # 计算平均值
        avg_coverage = sum(d['test_coverage'] for d in weekly_data) / len(weekly_data)
        avg_quality = sum(d['code_quality_score'] for d in weekly_data) / len(weekly_data)
        avg_compliance = sum(d['compliance_rate'] for d in weekly_data) / len(weekly_data)
        avg_performance = sum(d['performance_score'] for d in weekly_data) / len(weekly_data)
        
        return {
            'days_tracked': len(weekly_data),
            'average_coverage': avg_coverage,
            'average_quality': avg_quality,
            'average_compliance': avg_compliance,
            'average_performance': avg_performance,
            'overall_trend': 'improving' if weekly_data[-1]['test_coverage'] > weekly_data[0]['test_coverage'] else 'stable'
        }
    
    def _analyze_weekly_trends(self, weekly_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析周度趋势"""
        if len(weekly_data) < 2:
            return {}
        
        first_day = weekly_data[0]
        last_day = weekly_data[-1]
        
        return {
            'coverage_trend': last_day['test_coverage'] - first_day['test_coverage'],
            'quality_trend': last_day['code_quality_score'] - first_day['code_quality_score'],
            'compliance_trend': last_day['compliance_rate'] - first_day['compliance_rate'],
            'performance_trend': last_day['performance_score'] - first_day['performance_score']
        }
    
    def _rate_quality_score(self, score: float) -> str:
        """评级质量分数"""
        if score >= 90:
            return 'excellent'
        elif score >= 80:
            return 'good'
        elif score >= 70:
            return 'average'
        else:
            return 'poor'
    
    def _rate_performance_score(self, score: float) -> str:
        """评级性能分数"""
        if score >= 90:
            return 'excellent'
        elif score >= 80:
            return 'good'
        elif score >= 70:
            return 'average'
        else:
            return 'poor'
    
    def _load_latest_metrics(self) -> Dict[str, Any]:
        """加载最新的度量数据"""
        try:
            metrics_file = self.metrics_dir / "tdd_metrics.json"
            if metrics_file.exists():
                with open(metrics_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                metrics_list = data.get('metrics', [])
                if metrics_list:
                    return metrics_list[-1]  # 返回最新的度量数据
            
            return {}
            
        except Exception as e:
            self.logger.error(f"加载最新度量数据时出错: {e}")
            return {}
    
    def _generate_overview(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """生成概览数据"""
        return {
            'health_score': self._calculate_health_score(metrics),
            'key_metrics': {
                'coverage': metrics.get('test_coverage', 0),
                'quality': metrics.get('code_quality_score', 0),
                'compliance': metrics.get('compliance_rate', 0),
                'performance': metrics.get('performance_score', 0)
            },
            'status': self._determine_overall_status(metrics)
        }
    
    def _calculate_health_score(self, metrics: Dict[str, Any]) -> float:
        """计算整体健康评分"""
        weights = {
            'coverage': 0.3,
            'quality': 0.25,
            'compliance': 0.25,
            'performance': 0.2
        }
        
        coverage_score = metrics.get('test_coverage', 0) * 100
        quality_score = metrics.get('code_quality_score', 0)
        compliance_score = metrics.get('compliance_rate', 0) * 100
        performance_score = metrics.get('performance_score', 0)
        
        health_score = (
            coverage_score * weights['coverage'] +
            quality_score * weights['quality'] +
            compliance_score * weights['compliance'] +
            performance_score * weights['performance']
        )
        
        return health_score
    
    def _determine_overall_status(self, metrics: Dict[str, Any]) -> str:
        """确定整体状态"""
        health_score = self._calculate_health_score(metrics)
        
        if health_score >= 90:
            return 'excellent'
        elif health_score >= 80:
            return 'good'
        elif health_score >= 70:
            return 'warning'
        else:
            return 'critical'
    
    def _save_report(self, report: Dict[str, Any], file_path: Path) -> None:
        """保存报告到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"报告已保存到 {file_path}")
            
        except Exception as e:
            self.logger.error(f"保存报告时出错: {e}")
    
    def _generate_html_report(self, report: Dict[str, Any], filename: str) -> None:
        """生成HTML格式报告"""
        try:
            # 简化的HTML模板
            html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>TDD度量报告 - {{ date }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .metric { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; }
        .alert { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>TDD度量报告</h1>
        <p>日期: {{ date }}</p>
    </div>
    
    <h2>摘要</h2>
    <div class="metric">
        <strong>测试覆盖率:</strong> {{ "%.1f%%" | format(summary.test_coverage.current * 100) }}
    </div>
    <div class="metric">
        <strong>代码质量评分:</strong> {{ summary.code_quality.score }}
    </div>
    
    {% if alerts %}
    <h2>警报</h2>
    {% for alert in alerts %}
    <div class="alert">
        <strong>{{ alert.level.upper() }}:</strong> {{ alert.message }}
    </div>
    {% endfor %}
    {% endif %}
    
    <h2>建议</h2>
    <ul>
    {% for rec in recommendations %}
        <li>{{ rec }}</li>
    {% endfor %}
    </ul>
</body>
</html>
            """
            
            template = Template(html_template)
            html_content = template.render(**report)
            
            html_file = self.reports_dir / filename
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            self.logger.info(f"HTML报告已生成: {html_file}")
            
        except Exception as e:
            self.logger.error(f"生成HTML报告时出错: {e}")
    
    # 简化其他方法的实现
    def _generate_coverage_widget(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        return {'current': metrics.get('test_coverage', 0), 'target': 0.90}
    
    def _generate_quality_widget(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        return {'score': metrics.get('code_quality_score', 0)}
    
    def _generate_performance_widget(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        return {'score': metrics.get('performance_score', 0)}
    
    def _generate_compliance_widget(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        return {'rate': metrics.get('compliance_rate', 0)}
    
    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        return [{'type': 'test_run', 'timestamp': datetime.now().isoformat(), 'status': 'success'}]
    
    def _get_active_alerts(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        return []
    
    def _generate_quick_stats(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        return {
            'tests_run_today': 50,
            'code_commits': 3,
            'coverage_change': '+1.2%',
            'quality_trend': 'improving'
        }
    
    def _identify_achievements(self, weekly_data: List[Dict[str, Any]]) -> List[str]:
        return ['达到90%测试覆盖率目标', '代码质量评分持续提升']
    
    def _identify_issues(self, weekly_data: List[Dict[str, Any]]) -> List[str]:
        return []
    
    def _set_weekly_goals(self, weekly_data: List[Dict[str, Any]]) -> List[str]:
        return ['维持95%+测试覆盖率', '提升TDD合规性到90%']
    
    def _generate_weekly_recommendations(self, weekly_data: List[Dict[str, Any]]) -> List[str]:
        return ['继续保持良好的TDD实践', '关注代码质量趋势']
    
    def _generate_weekly_charts(self, weekly_data: List[Dict[str, Any]], start_date: str, end_date: str) -> None:
        """生成周度图表（简化实现）"""
        pass 