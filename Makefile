# Voice-came TDD开发 Makefile
# 提供便捷的开发和测试命令

.PHONY: help install test test-unit test-integration test-fast test-slow coverage format check lint type-check security clean setup pre-commit tdd-cycle

# 默认目标
help:
	@echo "Voice-came TDD开发命令："
	@echo ""
	@echo "  安装和设置："
	@echo "    install      - 安装依赖包"
	@echo "    setup        - 完整环境设置（安装依赖+pre-commit）"
	@echo ""
	@echo "  测试命令："
	@echo "    test         - 运行所有测试"
	@echo "    test-unit    - 运行单元测试"
	@echo "    test-integration - 运行集成测试"
	@echo "    test-fast    - 运行快速测试（排除慢测试）"
	@echo "    test-slow    - 只运行慢测试"
	@echo "    coverage     - 生成覆盖率报告"
	@echo ""
	@echo "  代码质量："
	@echo "    format       - 格式化代码"
	@echo "    check        - 完整代码质量检查"
	@echo "    lint         - 代码风格检查"
	@echo "    type-check   - 类型检查"
	@echo "    security     - 安全检查"
	@echo ""
	@echo "  TDD流程："
	@echo "    tdd-cycle    - 完整TDD循环（测试+检查+格式化）"
	@echo "    pre-commit   - 运行pre-commit检查"
	@echo ""
	@echo "  清理："
	@echo "    clean        - 清理临时文件"

# 安装依赖
install:
	pip install -r requirements.txt
	pip install -e .[dev]

# 完整环境设置
setup: install
	pre-commit install
	@echo "✅ 开发环境设置完成！"

# 测试命令
test:
	pytest --cov=src/voice_came --cov-report=term-missing --cov-report=html

test-unit:
	pytest -m unit --cov=src/voice_came --cov-report=term-missing

test-integration:
	pytest -m integration --cov=src/voice_came --cov-report=term-missing

test-fast:
	pytest -m "not slow" --cov=src/voice_came --cov-report=term-missing

test-slow:
	pytest -m slow -v

# 覆盖率报告
coverage:
	coverage run -m pytest
	coverage report
	coverage html
	@echo "📊 覆盖率报告生成在 htmlcov/ 目录"

# 代码格式化
format:
	black src/ tests/
	isort src/ tests/
	@echo "✨ 代码格式化完成"

# 代码检查
check: lint type-check security
	@echo "✅ 所有代码质量检查通过"

lint:
	flake8 src/ tests/

type-check:
	mypy src/voice_came --ignore-missing-imports

security:
	bandit -r src/ -f json -o bandit-report.json || true
	@echo "🔒 安全检查完成，报告保存到 bandit-report.json"

# Pre-commit检查
pre-commit:
	pre-commit run --all-files

# TDD循环
tdd-cycle:
	@echo "🔄 开始TDD循环..."
	$(MAKE) test-fast
	$(MAKE) check
	$(MAKE) format
	@echo "✅ TDD循环完成！"

# 清理临时文件
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf temp_test/
	rm -rf output_test/
	rm -rf models_test/
	rm -rf logs_test/
	@echo "🧹 清理完成"

# 快速开发循环（保存文件后运行）
watch:
	@echo "👀 监视文件变化，自动运行测试..."
	pytest-watch --runner "make test-fast" 