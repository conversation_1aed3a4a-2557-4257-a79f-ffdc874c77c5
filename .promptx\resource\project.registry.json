{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-17T14:25:56.068Z", "updatedAt": "2025-06-17T14:25:56.077Z", "resourceCount": 18}, "resources": [{"id": "test-engineer", "source": "project", "protocol": "role", "name": "Test Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/test-engineer/test-engineer.role.md", "metadata": {"createdAt": "2025-06-17T14:25:56.069Z", "updatedAt": "2025-06-17T14:25:56.069Z", "scannedAt": "2025-06-17T14:25:56.069Z"}}, {"id": "test-engineer", "source": "project", "protocol": "thought", "name": "Test Engineer 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/test-engineer/thought/test-engineer.thought.md", "metadata": {"createdAt": "2025-06-17T14:25:56.069Z", "updatedAt": "2025-06-17T14:25:56.069Z", "scannedAt": "2025-06-17T14:25:56.069Z"}}, {"id": "test-engineer", "source": "project", "protocol": "execution", "name": "Test Engineer 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/test-engineer/execution/test-engineer.execution.md", "metadata": {"createdAt": "2025-06-17T14:25:56.069Z", "updatedAt": "2025-06-17T14:25:56.069Z", "scannedAt": "2025-06-17T14:25:56.069Z"}}, {"id": "software-testing", "source": "project", "protocol": "knowledge", "name": "Software Testing 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/test-engineer/knowledge/software-testing.knowledge.md", "metadata": {"createdAt": "2025-06-17T14:25:56.071Z", "updatedAt": "2025-06-17T14:25:56.071Z", "scannedAt": "2025-06-17T14:25:56.071Z"}}, {"id": "voice-came-fullstack-dev", "source": "project", "protocol": "role", "name": "Voice Came Fullstack Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/voice-came-fullstack-dev/voice-came-fullstack-dev.role.md", "metadata": {"createdAt": "2025-06-17T14:25:56.071Z", "updatedAt": "2025-06-17T14:25:56.071Z", "scannedAt": "2025-06-17T14:25:56.071Z"}}, {"id": "voice-came-fullstack-thinking", "source": "project", "protocol": "thought", "name": "Voice Came Fullstack Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/voice-came-fullstack-dev/thought/voice-came-fullstack-thinking.thought.md", "metadata": {"createdAt": "2025-06-17T14:25:56.072Z", "updatedAt": "2025-06-17T14:25:56.072Z", "scannedAt": "2025-06-17T14:25:56.072Z"}}, {"id": "code-quality-standards", "source": "project", "protocol": "execution", "name": "Code Quality Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/voice-came-fullstack-dev/execution/code-quality-standards.execution.md", "metadata": {"createdAt": "2025-06-17T14:25:56.072Z", "updatedAt": "2025-06-17T14:25:56.072Z", "scannedAt": "2025-06-17T14:25:56.072Z"}}, {"id": "voice-came-development-workflow", "source": "project", "protocol": "execution", "name": "Voice Came Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/voice-came-fullstack-dev/execution/voice-came-development-workflow.execution.md", "metadata": {"createdAt": "2025-06-17T14:25:56.072Z", "updatedAt": "2025-06-17T14:25:56.072Z", "scannedAt": "2025-06-17T14:25:56.072Z"}}, {"id": "translation-engines", "source": "project", "protocol": "knowledge", "name": "Translation Engines 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/voice-came-fullstack-dev/knowledge/translation-engines.knowledge.md", "metadata": {"createdAt": "2025-06-17T14:25:56.073Z", "updatedAt": "2025-06-17T14:25:56.073Z", "scannedAt": "2025-06-17T14:25:56.073Z"}}, {"id": "voice-came-tech-stack", "source": "project", "protocol": "knowledge", "name": "Voice Came Tech Stack 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/voice-came-fullstack-dev/knowledge/voice-came-tech-stack.knowledge.md", "metadata": {"createdAt": "2025-06-17T14:25:56.073Z", "updatedAt": "2025-06-17T14:25:56.073Z", "scannedAt": "2025-06-17T14:25:56.073Z"}}, {"id": "whisperx-integration", "source": "project", "protocol": "knowledge", "name": "Whisperx Integration 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/voice-came-fullstack-dev/knowledge/whisperx-integration.knowledge.md", "metadata": {"createdAt": "2025-06-17T14:25:56.073Z", "updatedAt": "2025-06-17T14:25:56.073Z", "scannedAt": "2025-06-17T14:25:56.073Z"}}, {"id": "voice-came-fullstack-developer", "source": "project", "protocol": "role", "name": "Voice Came Fullstack Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/voice-came-fullstack-developer/voice-came-fullstack-developer.role.md", "metadata": {"createdAt": "2025-06-17T14:25:56.074Z", "updatedAt": "2025-06-17T14:25:56.074Z", "scannedAt": "2025-06-17T14:25:56.074Z"}}, {"id": "voice-came-pm", "source": "project", "protocol": "role", "name": "Voice Came Pm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/voice-came-pm/voice-came-pm.role.md", "metadata": {"createdAt": "2025-06-17T14:25:56.075Z", "updatedAt": "2025-06-17T14:25:56.075Z", "scannedAt": "2025-06-17T14:25:56.075Z"}}, {"id": "voice-came-product-thinking", "source": "project", "protocol": "thought", "name": "Voice Came Product Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/voice-came-pm/thought/voice-came-product-thinking.thought.md", "metadata": {"createdAt": "2025-06-17T14:25:56.076Z", "updatedAt": "2025-06-17T14:25:56.076Z", "scannedAt": "2025-06-17T14:25:56.076Z"}}, {"id": "voice-came-product-management", "source": "project", "protocol": "execution", "name": "Voice Came Product Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/voice-came-pm/execution/voice-came-product-management.execution.md", "metadata": {"createdAt": "2025-06-17T14:25:56.076Z", "updatedAt": "2025-06-17T14:25:56.076Z", "scannedAt": "2025-06-17T14:25:56.076Z"}}, {"id": "asmr-domain-expertise", "source": "project", "protocol": "knowledge", "name": "Asmr Domain Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/voice-came-pm/knowledge/asmr-domain-expertise.knowledge.md", "metadata": {"createdAt": "2025-06-17T14:25:56.076Z", "updatedAt": "2025-06-17T14:25:56.076Z", "scannedAt": "2025-06-17T14:25:56.076Z"}}, {"id": "product-management", "source": "project", "protocol": "knowledge", "name": "Product Management 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/voice-came-pm/knowledge/product-management.knowledge.md", "metadata": {"createdAt": "2025-06-17T14:25:56.076Z", "updatedAt": "2025-06-17T14:25:56.076Z", "scannedAt": "2025-06-17T14:25:56.076Z"}}, {"id": "voice-ai-technology", "source": "project", "protocol": "knowledge", "name": "Voice Ai Technology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/voice-came-pm/knowledge/voice-ai-technology.knowledge.md", "metadata": {"createdAt": "2025-06-17T14:25:56.076Z", "updatedAt": "2025-06-17T14:25:56.076Z", "scannedAt": "2025-06-17T14:25:56.076Z"}}], "stats": {"totalResources": 18, "byProtocol": {"role": 4, "thought": 3, "execution": 4, "knowledge": 7}, "bySource": {"project": 18}}}