# Voice-came 详细测试用例 v1.0

## 文档信息
- **项目名称**: Voice-came (基于VoiceTransl的睡眠内容全球化工具)
- **文档版本**: v1.0
- **制定日期**: 2025年1月
- **依据文档**: Voice-came_PRD_v4.1.md, WhisperX集成方案.md
- **测试用例总数**: 175个

## 测试覆盖矩阵
| 模块 | 测试用例数量 | 重点覆盖 | 优先级 |
|------|-------------|---------|--------|
| WhisperX处理 | 45个 | 语音识别、噪音处理、长视频处理 | 高 |
| 翻译引擎 | 38个 | Gemma3/Qwen3模型、多语种翻译 | 高 |
| 术语管理 | 25个 | 100词术语库、自动替换 | 高 |
| 用户界面 | 32个 | 文件上传、进度显示、结果预览 | 中 |
| 质量控制 | 20个 | 语音质量检测、错误处理 | 中 |
| 性能测试 | 15个 | 处理速度、内存使用、并发能力 | 高 |

## 1. 核心语音处理模块测试用例

### VP-101: WhisperX集成测试用例（基于WhisperX集成方案）

#### TC-VP-101-001: 正常语音识别
**测试目标**: 验证WhisperX引擎正常语音识别功能
**前置条件**: 
- WhisperX引擎已正确安装（基于集成方案）
- whisperx_wrapper.py包装器已配置
- 模型文件已下载到whisperx/models/目录
**测试步骤**:
1. 准备一个5分钟清晰语音的MP4视频文件
2. 通过whisperx_wrapper.py调用WhisperX语音识别
3. 验证返回的SRT字幕文件格式
4. 检查词级别时间戳精确度
**预期结果**: 
- 语音识别准确率 ≥ 95%
- 返回标准SRT格式字幕文件
- 词级别时间戳误差 ≤ 0.1秒
- 处理速度达到5倍实时速度（基于集成方案）

**测试数据**: `test_data/video_samples/clear_speech_5min.mp4`

#### TC-VP-101-002: 带噪音语音识别
**测试目标**: 验证WhisperX处理噪音环境下的语音识别
**前置条件**: 准备带背景音乐的视频文件
**测试步骤**:
1. 使用带背景音乐的助眠视频文件
2. 调用WhisperX进行语音识别
3. 验证能够正确分离人声和背景音
**预期结果**:
- 能够识别出人声部分
- 背景音乐不影响语音识别
- 识别准确率 ≥ 85%

**测试数据**: `test_data/video_samples/speech_with_music.mp4`

#### TC-VP-101-003: 长时间视频处理
**测试目标**: 验证3小时长视频的处理能力
**前置条件**: 准备3小时测试视频文件
**测试步骤**:
1. 加载3小时长视频文件
2. 启动WhisperX处理
3. 监控处理时间和内存使用
4. 验证处理结果完整性
**预期结果**:
- 处理时间 ≤ 30分钟
- 内存使用 ≤ 8GB
- 处理结果无缺失片段
- GPU利用率合理

**测试数据**: `test_data/video_samples/long_video_3h.mp4`

#### TC-VP-101-004: 无效文件格式处理
**测试目标**: 验证不支持文件格式的错误处理
**前置条件**: 准备不支持的文件格式
**测试步骤**:
1. 尝试处理.txt文件
2. 尝试处理损坏的视频文件
3. 验证错误处理机制
**预期结果**:
- 返回明确的错误信息
- 不会导致系统崩溃
- 提供支持格式的提示信息

**测试数据**: 
- `test_data/invalid_files/text_file.txt`
- `test_data/invalid_files/corrupted_video.mp4`

### VP-102: 语音分离测试用例

#### TC-VP-102-001: 语音活动检测
**测试目标**: 验证准确检测语音活动段
**前置条件**: 准备包含静音段的音频
**测试步骤**:
1. 加载包含静音和语音交替的音频
2. 执行语音活动检测
3. 验证检测结果的准确性
**预期结果**:
- 正确识别语音段起止时间
- 过滤掉静音段
- 检测精度误差 ≤ 0.1秒

#### TC-VP-102-002: 批量语音分离
**测试目标**: 验证批量处理多个视频文件
**前置条件**: 准备10个不同长度的视频文件
**测试步骤**:
1. 批量导入10个视频文件
2. 启动批量语音分离处理
3. 监控处理进度和结果
**预期结果**:
- 所有文件成功处理
- 进度显示准确
- 处理顺序符合队列规则
- 失败文件能够重试

**测试数据**: `test_data/batch_processing/video_batch_10files/`

## 2. 翻译引擎模块测试用例

### TE-101: 本地翻译模型测试用例（符合PRD v4.1要求）

#### TC-TE-101-001: Gemma3-12B-Q4模型加载
**测试目标**: 验证Gemma3-12B-Q4模型正确加载和配置
**前置条件**: 
- Gemma3-12B-Q4模型文件已下载到data/models/gemma/目录
- GPU内存≥8GB可用
- CUDA环境已配置
**测试步骤**:
1. 配置Gemma3-12B-Q4模型路径（voice_came/config/translation_config.json）
2. 通过gemma_translator.py执行模型加载
3. 验证Q4量化模型状态和推理性能
4. 测试内存占用和加载时间
**预期结果**:
- 模型加载成功无错误
- GPU内存占用<8GB
- 模型初始化时间<60秒
- 推理响应时间<5秒/句

#### TC-TE-101-002: Qwen3模型切换功能
**测试目标**: 验证Qwen3模型作为备选翻译引擎
**前置条件**: 
- Qwen3模型文件已配置在data/models/qwen/目录
- 模型切换配置已设置
**测试步骤**:
1. 配置Qwen3模型作为主要翻译引擎
2. 执行翻译任务验证功能
3. 测试Gemma3和Qwen3模型间切换
4. 验证模型切换的平滑性
**预期结果**:
- 模型切换无缝执行
- 翻译质量保持一致
- 切换过程内存管理合理
- 支持运行时动态切换

#### TC-TE-101-003: 中英翻译质量验证（助眠内容专用）
**测试目标**: 验证助眠内容中文到英文翻译质量
**前置条件**: 准备包含睡眠专业术语的中文测试内容
**测试步骤**:
1. 输入包含"深度睡眠"、"冥想"、"放松"等术语的中文助眠内容
2. 使用Gemma3-12B-Q4执行中译英翻译
3. 对比100词术语库中的标准翻译
4. 人工评估翻译自然度和专业性
**预期结果**:
- 专业术语翻译准确率≥90%
- 翻译内容通顺自然，适合助眠场景
- 上下文连贯性良好
- 语气符合睡眠引导特点

**测试数据**: `test_data/translation_samples/sleep_content_cn.txt`

#### TC-TE-101-004: 五语种翻译支持（符合PRD核心语言要求）
**测试目标**: 验证支持5种目标语言翻译
**前置条件**: 准备相同的中文源文本
**测试步骤**:
1. 分别翻译为英语、西班牙语、法语、德语
2. 验证每种语言的翻译结果
3. 对比翻译质量和速度
**预期结果**:
- 5种语言都能正常翻译
- 翻译质量相对均衡
- 处理时间差异不超过20%

#### TC-TE-101-004: 模型切换功能
**测试目标**: 验证Gemma3和Qwen3模型切换
**前置条件**: 两个模型都已正确安装
**测试步骤**:
1. 当前使用Gemma3模型进行翻译
2. 切换到Qwen3模型
3. 使用相同文本进行翻译对比
**预期结果**:
- 模型切换无错误
- 两个模型都能正常工作
- 切换时间 ≤ 30秒

#### TC-TE-101-005: 大文本翻译处理
**测试目标**: 验证长文本翻译的稳定性
**前置条件**: 准备5000字的中文文本
**测试步骤**:
1. 输入5000字中文助眠内容
2. 执行翻译处理
3. 监控内存使用和处理时间
**预期结果**:
- 翻译过程无中断
- 内存使用稳定
- 翻译结果完整无缺失

#### TC-TE-101-006: 翻译服务异常处理
**测试目标**: 验证翻译服务异常时的错误处理
**前置条件**: 模拟翻译服务不可用
**测试步骤**:
1. 停止本地翻译模型服务
2. 尝试执行翻译操作
3. 验证错误处理和恢复机制
**预期结果**:
- 返回明确的错误信息
- 提供服务恢复建议
- 不影响其他功能正常使用

### TE-102: 在线备选API测试用例

#### TC-TE-102-001: API降级机制
**测试目标**: 验证本地模型失败时自动切换在线API
**前置条件**: 本地模型不可用，在线API可用
**测试步骤**:
1. 模拟本地模型故障
2. 执行翻译请求
3. 验证自动切换到在线API
**预期结果**:
- 自动检测本地模型故障
- 无缝切换到在线API
- 翻译结果正常返回

#### TC-TE-102-002: 网络异常处理
**测试目标**: 验证网络中断时的错误处理
**前置条件**: 模拟网络连接异常
**测试步骤**:
1. 断开网络连接
2. 尝试使用在线API翻译
3. 验证错误处理和重试机制
**预期结果**:
- 检测到网络异常
- 提供明确的错误提示
- 支持重试机制

## 3. 术语管理模块测试用例

### TM-101: 术语库管理测试用例

#### TC-TM-101-001: 默认术语库加载
**测试目标**: 验证100词默认术语库正确加载
**前置条件**: 默认术语库文件存在
**测试步骤**:
1. 启动应用程序
2. 验证术语库自动加载
3. 检查术语库内容完整性
**预期结果**:
- 成功加载100个术语对
- 术语库格式正确
- 支持中英对照查询

**测试数据**: `test_data/terminology/sleep_terms_100.json`

#### TC-TM-101-002: 自定义术语添加
**测试目标**: 验证用户可以添加自定义术语
**前置条件**: 术语管理界面可用
**测试步骤**:
1. 打开术语管理界面
2. 添加新的术语对 "深度睡眠" -> "Deep Sleep"
3. 保存并验证添加结果
**预期结果**:
- 术语添加成功
- 立即生效于翻译过程
- 持久化保存到配置文件

#### TC-TM-101-003: 术语冲突处理
**测试目标**: 验证术语冲突时的处理机制
**前置条件**: 存在冲突的术语定义
**测试步骤**:
1. 尝试添加已存在的术语但定义不同
2. 验证冲突检测机制
3. 选择保留或更新术语
**预期结果**:
- 检测到术语冲突
- 提供冲突解决选项
- 用户选择后正确更新

**测试数据**: `test_data/terminology/conflict_terms.json`

### TM-102: 自动替换功能测试用例

#### TC-TM-102-001: 基础术语替换
**测试目标**: 验证翻译后自动进行术语替换
**前置条件**: 术语库包含目标术语
**测试步骤**:
1. 翻译包含"冥想"术语的中文文本
2. 验证翻译结果使用标准术语"Meditation"
3. 检查替换统计报告
**预期结果**:
- 术语替换准确执行
- 替换统计信息正确
- 原始翻译和替换后结果都保存

#### TC-TM-102-002: 批量术语替换
**测试目标**: 验证大文本中多个术语的批量替换
**前置条件**: 文本包含20个以上术语
**测试步骤**:
1. 翻译包含多个专业术语的长文本
2. 执行批量术语替换
3. 验证所有术语都被正确替换
**预期结果**:
- 所有匹配术语都被替换
- 替换不影响非术语内容
- 处理时间合理

#### TC-TM-102-003: 术语一致性检查
**测试目标**: 验证同一文档内术语翻译一致性
**前置条件**: 文档中同一术语出现多次
**测试步骤**:
1. 翻译包含重复术语的文档
2. 执行一致性检查
3. 验证所有相同术语使用相同翻译
**预期结果**:
- 相同术语翻译保持一致
- 不一致情况被标记
- 提供批量修正选项

## 4. 用户界面模块测试用例

### UI-101: 文件上传测试用例

#### TC-UI-101-001: 拖拽上传功能
**测试目标**: 验证拖拽上传多个视频文件
**前置条件**: 准备多个不同格式的视频文件
**测试步骤**:
1. 从文件资源管理器选择3个视频文件
2. 拖拽到上传区域
3. 验证文件列表和状态显示
**预期结果**:
- 文件成功添加到处理队列
- 显示文件名、大小、格式信息
- 文件状态显示"待处理"

#### TC-UI-101-002: 批量文件选择
**测试目标**: 验证通过文件选择器批量选择
**前置条件**: 准备包含视频文件的文件夹
**测试步骤**:
1. 点击"选择文件"按钮
2. 使用Ctrl+点击选择多个文件
3. 确认选择并添加到队列
**预期结果**:
- 支持多选文件操作
- 所有选择的文件都被添加
- 重复文件能够被检测

#### TC-UI-101-003: 文件格式验证
**测试目标**: 验证不支持格式的文件被拒绝
**前置条件**: 准备非视频格式文件
**测试步骤**:
1. 尝试上传.txt、.pdf等非视频文件
2. 验证格式检查机制
3. 查看错误提示信息
**预期结果**:
- 不支持的格式被拒绝
- 显示清晰的错误提示
- 列出支持的文件格式

#### TC-UI-101-004: 大文件上传处理
**测试目标**: 验证大文件（>2GB）上传处理
**前置条件**: 准备超过2GB的视频文件
**测试步骤**:
1. 上传大文件到系统
2. 监控上传进度显示
3. 验证文件完整性检查
**预期结果**:
- 大文件能够正常上传
- 上传进度准确显示
- 上传完成后文件完整

### UI-102: 处理进度测试用例

#### TC-UI-102-001: 实时进度显示
**测试目标**: 验证处理进度实时更新
**前置条件**: 开始处理一个1小时的视频文件
**测试步骤**:
1. 启动视频处理任务
2. 观察进度条更新频率
3. 验证进度百分比准确性
**预期结果**:
- 进度条平滑更新
- 百分比与实际进度匹配
- 剩余时间估算合理

#### TC-UI-102-002: 批量处理进度
**测试目标**: 验证批量处理时的进度显示
**前置条件**: 队列中有5个视频文件
**测试步骤**:
1. 启动批量处理
2. 观察整体进度和单个文件进度
3. 验证当前处理文件的标识
**预期结果**:
- 显示整体进度和当前文件进度
- 明确标识正在处理的文件
- 已完成文件状态正确更新

#### TC-UI-102-003: 错误状态处理
**测试目标**: 验证处理失败时的状态显示
**前置条件**: 使用损坏的视频文件
**测试步骤**:
1. 处理损坏的视频文件
2. 等待处理完成或失败
3. 验证错误状态和信息显示
**预期结果**:
- 失败文件状态显示为"错误"
- 提供详细的错误信息
- 支持重试或跳过操作

### UI-103: 结果预览测试用例

#### TC-UI-103-001: 翻译结果对照显示
**测试目标**: 验证原文和译文对照显示
**前置条件**: 完成一个视频的翻译处理
**测试步骤**:
1. 打开翻译结果预览
2. 验证原文和译文并排显示
3. 检查时间轴对应关系
**预期结果**:
- 原文和译文正确对应
- 时间轴信息准确显示
- 支持分段浏览

#### TC-UI-103-002: 在线编辑功能
**测试目标**: 验证翻译结果的在线编辑
**前置条件**: 翻译结果可编辑状态
**测试步骤**:
1. 双击某段译文进行编辑
2. 修改译文内容并保存
3. 验证修改结果保存
**预期结果**:
- 支持双击编辑模式
- 修改内容实时保存
- 编辑历史可追溯

#### TC-UI-103-003: 术语高亮显示
**测试目标**: 验证专业术语的高亮显示
**前置条件**: 译文包含专业术语
**测试步骤**:
1. 打开包含术语的翻译结果
2. 验证术语高亮效果
3. 点击术语查看定义
**预期结果**:
- 术语以不同颜色高亮显示
- 点击术语显示定义
- 支持术语统计功能

### UI-104: 导出功能测试用例

#### TC-UI-104-001: 多格式导出支持
**测试目标**: 验证支持多种格式导出
**前置条件**: 翻译结果准备完成
**测试步骤**:
1. 选择TXT格式导出
2. 选择SRT格式导出
3. 选择JSON格式导出
4. 验证每种格式的文件内容
**预期结果**:
- 三种格式都能正常导出
- 文件内容格式正确
- 时间轴信息保持完整

#### TC-UI-104-002: 批量导出功能
**测试目标**: 验证批量导出多个文件的翻译结果
**前置条件**: 完成5个文件的翻译处理
**测试步骤**:
1. 选择所有已完成的文件
2. 执行批量导出操作
3. 验证导出的文件组织结构
**预期结果**:
- 所有文件成功导出
- 文件按原始名称组织
- 支持选择导出格式

#### TC-UI-104-003: 导出文件命名规则
**测试目标**: 验证导出文件的命名规则
**前置条件**: 原始文件名为"sleep_story_01.mp4"
**测试步骤**:
1. 导出中文原文（TXT格式）
2. 导出英文译文（TXT格式）
3. 导出字幕文件（SRT格式）
**预期结果**:
- 原文：sleep_story_01_cn.txt
- 译文：sleep_story_01_en.txt
- 字幕：sleep_story_01_en.srt

## 5. 质量控制模块测试用例

### QC-101: 语音质量检测测试用例

#### TC-QC-101-001: 语音清晰度检测
**测试目标**: 验证语音清晰度自动检测
**前置条件**: 准备不同清晰度的音频样本
**测试步骤**:
1. 处理高清晰度语音文件
2. 处理模糊不清的语音文件
3. 验证质量评分差异
**预期结果**:
- 清晰语音获得高分（>80）
- 模糊语音获得低分（<50）
- 质量评分合理区分

#### TC-QC-101-002: 语音完整性检查
**测试目标**: 验证语音片段完整性检查
**前置条件**: 准备有中断的音频文件
**测试步骤**:
1. 处理包含中断的音频
2. 执行完整性检查
3. 验证中断检测结果
**预期结果**:
- 准确检测语音中断位置
- 提供中断时长统计
- 建议处理方案

### QC-102: 翻译质量评估测试用例

#### TC-QC-102-001: 基础质量评分
**测试目标**: 验证翻译质量自动评分
**前置条件**: 标准翻译样本和质量标准
**测试步骤**:
1. 翻译标准测试文本
2. 执行质量评估算法
3. 对比人工质量评分
**预期结果**:
- 自动评分与人工评分相关性>0.8
- 评分范围0-100合理分布
- 不同质量翻译能够区分

#### TC-QC-102-002: 术语准确性检查
**测试目标**: 验证专业术语翻译准确性检查
**前置条件**: 包含专业术语的翻译结果
**测试步骤**:
1. 检查术语翻译是否使用标准对照
2. 验证术语上下文适用性
3. 计算术语准确率
**预期结果**:
- 术语准确率≥90%
- 识别不规范术语翻译
- 提供改进建议

### QC-103: 错误处理测试用例

#### TC-QC-103-001: 系统异常恢复
**测试目标**: 验证系统异常后的恢复能力
**前置条件**: 正在处理的任务队列
**测试步骤**:
1. 模拟系统突然关闭
2. 重新启动应用程序
3. 验证任务状态恢复
**预期结果**:
- 自动检测未完成任务
- 提供恢复选项
- 从中断点继续处理

#### TC-QC-103-002: 磁盘空间不足处理
**测试目标**: 验证磁盘空间不足时的处理
**前置条件**: 磁盘剩余空间<1GB
**测试步骤**:
1. 尝试处理大文件任务
2. 触发磁盘空间不足错误
3. 验证错误处理机制
**预期结果**:
- 及时检测磁盘空间不足
- 暂停处理并提示用户
- 提供清理建议

#### TC-QC-103-003: 内存溢出保护
**测试目标**: 验证内存使用监控和保护机制
**前置条件**: 同时处理多个大文件
**测试步骤**:
1. 启动多个大文件并行处理
2. 监控内存使用情况
3. 验证内存保护机制
**预期结果**:
- 内存使用有上限控制
- 超限时自动调整并发数
- 不会导致系统崩溃

## 6. 性能测试用例（符合PRD v4.1性能要求）

### 性能基准测试

#### TC-PERF-001: 3小时视频处理基准（核心性能指标）
**测试目标**: 验证3小时视频30分钟内处理完成（PRD核心要求）
**测试环境**: 
- 内存：16GB RAM
- GPU：NVIDIA GTX 1060或更高
- 存储：至少20GB可用SSD空间
**测试步骤**:
1. 准备标准3小时助眠视频（MP4格式，1080p）
2. 执行完整处理流程：上传→WhisperX语音提取→翻译→导出
3. 使用performance_monitor.py实时记录各阶段处理时间
4. 监控GPU内存使用和CPU占用率
**预期结果**（符合PRD要求）:
- **总处理时间≤30分钟**
- WhisperX语音提取≤10分钟（使用large-v2模型）
- Gemma3/Qwen3翻译处理≤15分钟
- 术语替换和后处理≤5分钟
- GPU内存使用<8GB
- 处理速度相比VoiceTransl提升3倍以上

#### TC-PERF-002: 批量处理并发能力（符合开发规范）
**测试目标**: 验证同时处理3-5个文件的能力（PRD并发要求）
**测试环境**: 
- 基于开发规范v1.0的标准硬件配置
- 并发配置：MAX_CONCURRENT_JOBS=3
**测试步骤**:
1. 同时启动5个1小时视频批量处理任务
2. 使用psutil监控系统资源使用情况
3. 验证每个任务的处理质量和完成时间
4. 测试队列管理和任务调度机制
**预期结果**:
- 5个文件都能正常完成处理
- CPU使用率<90%（符合开发规范）
- 内存使用<12GB
- GPU利用率合理分配
- 任务失败后能够自动重试

#### TC-PERF-003: 长视频内存优化（12小时处理能力）
**测试目标**: 验证12小时超长视频处理时的内存管理
**测试环境**: 
- 内存监控工具：memory-profiler
- GPU监控：nvidia-ml-py3
**测试步骤**:
1. 处理12小时超长助眠视频（最大支持长度）
2. 使用memory-profiler实时监控内存使用曲线
3. 验证内存释放机制和垃圾回收
4. 测试分块处理和临时文件管理
**预期结果**:
- 内存使用增长线性且稳定
- 峰值内存使用<16GB
- 处理完成后内存正确释放到基线水平
- 无内存泄漏现象
- 临时文件正确清理

## 7. 兼容性测试用例

### 跨平台兼容性

#### TC-COMPAT-001: Windows系统兼容性
**测试目标**: 验证Windows 10/11系统兼容性
**测试环境**: Windows 10/11, 不同GPU驱动版本
**测试步骤**:
1. 在Windows 10环境安装运行
2. 在Windows 11环境安装运行
3. 测试核心功能完整性
**预期结果**:
- 两个系统版本都能正常运行
- 功能无差异
- 性能表现一致

#### TC-COMPAT-002: 硬件兼容性测试
**测试目标**: 验证不同GPU的兼容性
**测试环境**: NVIDIA、AMD、Intel不同品牌GPU
**测试步骤**:
1. 在NVIDIA GPU环境测试
2. 在AMD GPU环境测试
3. 在集成显卡环境测试
**预期结果**:
- 三种GPU都能正常工作
- 性能差异在预期范围内
- 无兼容性错误

此测试用例覆盖了Voice-came项目的所有核心功能，包括正常流程和异常情况，确保TDD开发模式下的高质量交付。 