#!/usr/bin/env python3
"""
Voice-came 快速环境设置脚本

这个脚本会根据操作系统自动选择合适的安装方式
"""

import os
import sys
import platform
import subprocess

def main():
    """主函数"""
    print("Voice-came 语音翻译系统 - 快速设置")
    print("=" * 50)
    print()
    
    system = platform.system()
    print(f"检测到操作系统：{system}")
    
    if system == "Windows":
        print("启动Windows设置脚本...")
        try:
            subprocess.run(["scripts\\setup_windows.bat"], shell=True, check=True)
        except subprocess.CalledProcessError:
            print("Windows设置脚本执行失败")
            print("请手动运行：scripts\\setup_windows.bat")
    
    elif system in ["Linux", "Darwin"]:  # Darwin是macOS
        print("启动Unix设置脚本...")
        try:
            # 先设置脚本权限
            os.chmod("scripts/setup_unix.sh", 0o755)
            subprocess.run(["bash", "scripts/setup_unix.sh"], check=True)
        except subprocess.CalledProcessError:
            print("Unix设置脚本执行失败")
            print("请手动运行：bash scripts/setup_unix.sh")
    
    else:
        print(f"不支持的操作系统：{system}")
        print("请手动安装依赖")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1) 