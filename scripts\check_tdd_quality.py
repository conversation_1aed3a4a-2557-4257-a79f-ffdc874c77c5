#!/usr/bin/env python3
"""
Voice-came TDD重构质量自动化检查脚本
检查TDD重构是否符合预期标准
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Tuple

class TDDQualityChecker:
    def __init__(self, tasks_dir: str = ".taskmaster/tasks"):
        self.tasks_dir = Path(tasks_dir)
        self.tasks_json = self.tasks_dir / "tasks.json"
        self.task_files = list(self.tasks_dir.glob("task_*.txt"))
        
    def load_tasks_json(self) -> Dict:
        """加载tasks.json文件"""
        with open(self.tasks_json, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def check_tdd_keywords(self) -> Tuple[int, List[str]]:
        """检查TDD关键词覆盖情况"""
        tdd_keywords = [
            "TDD模式", "测试驱动", "Red-Green-Refactor",
            "测试设计", "最小实现", "重构优化",
            "先写测试", "测试先行", "测试保护"
        ]
        
        issues = []
        total_files = len(self.task_files)
        files_with_tdd = 0
        
        for task_file in self.task_files:
            with open(task_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            has_tdd_keywords = any(keyword in content for keyword in tdd_keywords)
            if has_tdd_keywords:
                files_with_tdd += 1
            else:
                issues.append(f"{task_file.name} 缺少TDD关键词")
        
        coverage_rate = (files_with_tdd / total_files) * 100 if total_files > 0 else 0
        return coverage_rate, issues
    
    def check_dependencies(self) -> Tuple[int, List[str]]:
        """检查依赖关系正确性"""
        tasks_data = self.load_tasks_json()
        tasks = tasks_data.get('master', {}).get('tasks', [])
        
        issues = []
        correct_deps = 0
        total_tasks = len(tasks)
        
        for task in tasks:
            task_id = task.get('id')
            deps = task.get('dependencies', [])
            
            # Task 001和013不需要依赖013，其他都需要
            if task_id not in [1, 13]:
                if 13 in deps:
                    correct_deps += 1
                else:
                    issues.append(f"Task {task_id} 缺少对Task 013的依赖")
            else:
                correct_deps += 1  # Task 001和013自动算作正确
        
        dependency_rate = (correct_deps / total_tasks) * 100 if total_tasks > 0 else 0
        return dependency_rate, issues
    
    def check_red_green_refactor_cycle(self) -> Tuple[int, List[str]]:
        """检查Red-Green-Refactor循环完整性"""
        issues = []
        files_with_cycle = 0
        
        for task_file in self.task_files:
            with open(task_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更灵活的关键词匹配
            red_keywords = ["Red阶段", "测试设计", "先写测试", "测试用例设计", "失败测试", "先编写测试", "编写测试用例", "先编写.*测试", "基准测试", "先写基准测试"]
            green_keywords = ["Green阶段", "最小实现", "通过测试", "基础实现", "简单实现", "最小可用", "最小可用功能", "实现最小"]
            refactor_keywords = ["Refactor阶段", "重构优化", "代码重构", "优化代码", "重构改进", "在测试保护下重构"]
            
            has_red = any(keyword in content for keyword in red_keywords)
            has_green = any(keyword in content for keyword in green_keywords)
            has_refactor = any(keyword in content for keyword in refactor_keywords)
            
            # 对于基础设施任务(task_001, task_013)，不强制要求完整的TDD循环
            if task_file.name in ["task_001.txt", "task_013.txt"]:
                # 基础设施任务只要有TDD相关内容即可
                if "TDD" in content or "测试驱动" in content:
                    files_with_cycle += 1
                else:
                    issues.append(f"{task_file.name} 缺少TDD相关内容")
            else:
                # 功能任务需要完整的TDD循环
                if has_red and has_green and has_refactor:
                    files_with_cycle += 1
                else:
                    missing = []
                    if not has_red: missing.append("测试设计阶段")
                    if not has_green: missing.append("最小实现阶段")
                    if not has_refactor: missing.append("重构优化阶段")
                    issues.append(f"{task_file.name} 缺少: {', '.join(missing)}")
        
        cycle_rate = (files_with_cycle / len(self.task_files)) * 100 if self.task_files else 0
        return cycle_rate, issues
    
    def check_test_coverage_requirements(self) -> Tuple[int, List[str]]:
        """检查测试覆盖率要求"""
        issues = []
        files_with_coverage = 0
        
        coverage_patterns = [
            r"90%\+.*覆盖率",
            r"覆盖率.*90%",
            r"测试覆盖率.*90",
            r"95%\+.*覆盖率"
        ]
        
        for task_file in self.task_files:
            with open(task_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            has_coverage_req = any(re.search(pattern, content) for pattern in coverage_patterns)
            if has_coverage_req:
                files_with_coverage += 1
            else:
                issues.append(f"{task_file.name} 缺少明确的测试覆盖率要求")
        
        coverage_req_rate = (files_with_coverage / len(self.task_files)) * 100 if self.task_files else 0
        return coverage_req_rate, issues
    
    def check_task_title_standards(self) -> Tuple[int, List[str]]:
        """检查任务标题规范性"""
        issues = []
        compliant_titles = 0
        
        tdd_title_patterns = [
            "TDD模式", "TDD增强", "TDD质量保障", "TDD流程监控"
        ]
        
        for task_file in self.task_files:
            with open(task_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            title_line = None
            for line in lines:
                if line.startswith("# Title:"):
                    title_line = line
                    break
            
            if title_line:
                has_tdd_identifier = any(pattern in title_line for pattern in tdd_title_patterns)
                if has_tdd_identifier:
                    compliant_titles += 1
                else:
                    issues.append(f"{task_file.name} 标题缺少TDD标识")
            else:
                issues.append(f"{task_file.name} 缺少标题行")
        
        title_rate = (compliant_titles / len(self.task_files)) * 100 if self.task_files else 0
        return title_rate, issues
    
    def calculate_overall_score(self, scores: Dict[str, float]) -> float:
        """计算总体评分"""
        weights = {
            'tdd_principles': 0.40,  # TDD原则符合性
            'quality_standards': 0.25,  # 质量度量标准
            'task_structure': 0.20,  # 任务结构完整性
            'project_adaptation': 0.15  # 项目适配性
        }
        
        # TDD原则符合性 = (Red-Green-Refactor + 测试覆盖率要求) / 2
        tdd_principles = (scores['cycle_completeness'] + scores['coverage_requirements']) / 2
        
        # 质量度量标准 = (依赖关系 + TDD关键词) / 2
        quality_standards = (scores['dependencies'] + scores['tdd_keywords']) / 2
        
        # 任务结构完整性 = 标题规范性
        task_structure = scores['title_standards']
        
        # 项目适配性 = 95 (基于业务需求保持完整)
        project_adaptation = 95
        
        overall_score = (
            tdd_principles * weights['tdd_principles'] +
            quality_standards * weights['quality_standards'] +
            task_structure * weights['task_structure'] +
            project_adaptation * weights['project_adaptation']
        )
        
        return overall_score
    
    def run_full_check(self) -> Dict:
        """运行完整的TDD质量检查"""
        print("🔍 开始TDD重构质量检查...")
        print("=" * 50)
        
        # 1. 检查TDD关键词覆盖
        tdd_keywords_rate, tdd_issues = self.check_tdd_keywords()
        print(f"✅ TDD关键词覆盖率: {tdd_keywords_rate:.1f}%")
        
        # 2. 检查依赖关系
        deps_rate, deps_issues = self.check_dependencies()
        print(f"✅ 依赖关系正确率: {deps_rate:.1f}%")
        
        # 3. 检查Red-Green-Refactor循环
        cycle_rate, cycle_issues = self.check_red_green_refactor_cycle()
        print(f"✅ TDD循环完整率: {cycle_rate:.1f}%")
        
        # 4. 检查测试覆盖率要求
        coverage_rate, coverage_issues = self.check_test_coverage_requirements()
        print(f"✅ 覆盖率要求完整率: {coverage_rate:.1f}%")
        
        # 5. 检查任务标题规范
        title_rate, title_issues = self.check_task_title_standards()
        print(f"✅ 任务标题规范率: {title_rate:.1f}%")
        
        # 计算总体评分
        scores = {
            'tdd_keywords': tdd_keywords_rate,
            'dependencies': deps_rate,
            'cycle_completeness': cycle_rate,
            'coverage_requirements': coverage_rate,
            'title_standards': title_rate
        }
        
        overall_score = self.calculate_overall_score(scores)
        
        print("=" * 50)
        print(f"📊 总体评分: {overall_score:.2f}/100")
        
        # 评级判断
        if overall_score >= 90:
            grade = "优秀 ✅"
            recommendation = "可以立即开始TDD开发"
        elif overall_score >= 80:
            grade = "良好 ⚠️"
            recommendation = "需要小幅调整后开始"
        elif overall_score >= 70:
            grade = "合格 ⚠️"
            recommendation = "需要重要改进"
        else:
            grade = "不合格 ❌"
            recommendation = "需要重新设计"
        
        print(f"🎯 评级: {grade}")
        print(f"💡 建议: {recommendation}")
        
        # 汇总所有问题
        all_issues = tdd_issues + deps_issues + cycle_issues + coverage_issues + title_issues
        
        if all_issues:
            print("\n⚠️ 发现的问题:")
            for i, issue in enumerate(all_issues, 1):
                print(f"  {i}. {issue}")
        else:
            print("\n🎉 没有发现问题，TDD重构质量优秀！")
        
        return {
            'overall_score': overall_score,
            'grade': grade,
            'recommendation': recommendation,
            'scores': scores,
            'issues': all_issues,
            'details': {
                'tdd_keywords': {'rate': tdd_keywords_rate, 'issues': tdd_issues},
                'dependencies': {'rate': deps_rate, 'issues': deps_issues},
                'cycle_completeness': {'rate': cycle_rate, 'issues': cycle_issues},
                'coverage_requirements': {'rate': coverage_rate, 'issues': coverage_issues},
                'title_standards': {'rate': title_rate, 'issues': title_issues}
            }
        }

def main():
    """主函数"""
    checker = TDDQualityChecker()
    
    if not checker.tasks_dir.exists():
        print("❌ 错误: .taskmaster/tasks 目录不存在")
        return
    
    if not checker.tasks_json.exists():
        print("❌ 错误: tasks.json 文件不存在")
        return
    
    result = checker.run_full_check()
    
    # 保存检查结果
    output_file = Path(".taskmaster/tdd_quality_report.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: {output_file}")
    
    return result['overall_score'] >= 90

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 