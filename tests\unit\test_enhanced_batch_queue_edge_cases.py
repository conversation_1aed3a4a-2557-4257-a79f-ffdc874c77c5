#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
enhanced_batch_queue.py 边界情况测试

专门测试复杂执行路径、异常处理和边界情况
目标覆盖难以到达的代码分支
"""

import pytest
import time
import json
import threading
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from voice_came.core.enhanced_batch_queue import (
    QueueJob, JobState, JobPriority,
    EnhancedBatchQueue, ErrorRecoveryManager
)


class TestComplexExecutionPaths:
    """复杂执行路径测试"""
    
    @pytest.fixture
    def queue_with_monitoring(self, tmp_path):
        """创建启用监控的队列"""
        state_file = tmp_path / "test_queue.json" 
        return EnhancedBatchQueue(
            max_concurrent=2,
            state_file=state_file,
            enable_monitoring=True,
            cleanup_interval=1
        )
    
    def test_job_retry_delay_logic(self, queue_with_monitoring, tmp_path):
        """测试重试延迟逻辑"""
        test_file = tmp_path / "retry_test.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob("retry_job", test_file, retry_delay=0.1)
        job.status = JobState.RETRYING
        job.completed_at = time.time() - 0.2  # 延迟已过
        
        queue_with_monitoring.add_job(job)
        queue_with_monitoring.start()
        time.sleep(0.3)
        
        assert job.status in [JobState.RUNNING, JobState.COMPLETED]
        queue_with_monitoring.stop()
    
    def test_progress_callback_exception_handling(self, queue_with_monitoring, tmp_path):
        """测试进度回调异常处理"""
        test_file = tmp_path / "callback_test.mp4"
        test_file.write_bytes(b"test data")
        
        error_callback = Mock(side_effect=RuntimeError("Callback error"))
        queue_with_monitoring.set_progress_callback(error_callback)
        
        job = QueueJob("callback_job", test_file)
        queue_with_monitoring.add_job(job)
        
        queue_with_monitoring.start()
        time.sleep(0.6)
        
        # 任务应该完成（异常被捕获）
        assert job.status == JobState.COMPLETED
        queue_with_monitoring.stop()
    
    def test_error_recovery_with_retry(self, queue_with_monitoring, tmp_path):
        """测试错误恢复重试逻辑"""
        test_file = tmp_path / "error_test.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob("error_job", test_file, max_retries=1)
        
        # Mock错误恢复返回True
        with patch.object(queue_with_monitoring._error_recovery, 'handle_job_error', return_value=True):
            queue_with_monitoring.add_job(job)
            
            # 模拟处理中出错
            original_process = queue_with_monitoring._process_single_job
            call_count = 0
            
            def mock_process(job_param):
                nonlocal call_count
                call_count += 1
                if call_count == 1:
                    raise RuntimeError("First error")
                return original_process(job_param)
            
            with patch.object(queue_with_monitoring, '_process_single_job', side_effect=mock_process):
                queue_with_monitoring.start()
                time.sleep(0.5)
                
                # 应该被重试
                assert job.retry_count > 0 or job.status == JobState.COMPLETED
                queue_with_monitoring.stop()


class TestStateFileOperations:
    """状态文件操作测试"""
    
    def test_save_state_without_file(self):
        """测试无状态文件时保存"""
        queue = EnhancedBatchQueue(state_file=None)
        queue.save_state()  # 应该安全返回
        assert queue.state_file is None
    
    def test_save_state_creates_directory(self, tmp_path):
        """测试保存时创建目录"""
        nested_path = tmp_path / "deep" / "path"
        state_file = nested_path / "queue.json"
        
        queue = EnhancedBatchQueue(state_file=state_file)
        
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("test_job", test_file)
        queue.add_job(job)
        
        queue.save_state()
        
        assert nested_path.exists()
        assert state_file.exists()
    
    def test_load_corrupted_state_file(self, tmp_path):
        """测试加载损坏的状态文件"""
        state_file = tmp_path / "corrupted.json"
        
        with open(state_file, 'w') as f:
            f.write("{ invalid json")
        
        queue = EnhancedBatchQueue(state_file=state_file)
        queue._load_state_from_file()
        
        # 应该能处理损坏文件
        assert queue.size() == 0


class TestErrorRecoveryEdgeCases:
    """错误恢复边界情况"""
    
    def test_unknown_error_type(self):
        """测试未知错误类型"""
        recovery = ErrorRecoveryManager()
        
        class CustomError(Exception):
            pass
        
        job = QueueJob("test", Path("/tmp/test.mp4"))
        error = CustomError("Custom error")
        
        result = recovery.handle_job_error(job, error)
        
        assert result is True  # 默认重试
        assert "Custom error" in job.error_message
    
    def test_error_statistics(self):
        """测试错误统计"""
        recovery = ErrorRecoveryManager()
        
        job = QueueJob("test", Path("/tmp/test.mp4"))
        recovery.handle_job_error(job, FileNotFoundError("Not found"))
        recovery.handle_job_error(job, MemoryError("No memory"))
        
        stats = recovery.get_error_statistics()
        
        assert stats['total_errors'] == 2
        assert 'FileNotFoundError' in stats['error_types']
        assert 'MemoryError' in stats['error_types']


class TestQueueDestructor:
    """析构函数测试"""
    
    def test_destructor_stops_queue(self):
        """测试析构函数停止队列"""
        queue = EnhancedBatchQueue()
        queue.start()
        
        queue.__del__()
        
        assert queue._running is False
    
    def test_destructor_exception_handling(self):
        """测试析构函数异常处理"""
        queue = EnhancedBatchQueue()
        
        with patch.object(queue, 'stop', side_effect=RuntimeError("Stop error")):
            # 不应该抛出异常
            queue.__del__()


class TestMemoryMonitoring:
    """内存监控测试"""
    
    def test_memory_usage_with_psutil(self):
        """测试使用psutil的内存监控"""
        queue = EnhancedBatchQueue()
        
        with patch('voice_came.core.enhanced_batch_queue.psutil.virtual_memory') as mock_memory:
            mock_memory.return_value.percent = 85.5
            
            usage = queue._get_current_memory_usage()
            assert usage == 85.5
    
    def test_memory_usage_without_psutil(self):
        """测试psutil不可用时的处理"""
        queue = EnhancedBatchQueue()
        
        with patch('voice_came.core.enhanced_batch_queue.psutil.virtual_memory', 
                  side_effect=ImportError("psutil unavailable")):
            
            usage = queue._get_current_memory_usage()
            assert usage == 0.0


class TestCleanupOperations:
    """清理操作测试"""
    
    def test_cleanup_old_completed_jobs(self, tmp_path):
        """测试清理旧的已完成任务"""
        queue = EnhancedBatchQueue()
        
        # 添加已完成任务
        for i in range(3):
            test_file = tmp_path / f"cleanup_{i}.mp4"
            test_file.write_bytes(b"test data")
            job = QueueJob(f"cleanup_job_{i}", test_file)
            job.status = JobState.COMPLETED
            job.completed_at = time.time() - 3600  # 1小时前
            queue.add_job(job)
        
        initial_size = queue.size()
        queue._cleanup_completed_jobs()
        
        # 清理后可能减少任务数
        assert queue.size() <= initial_size
    
    def test_schedule_cleanup_timer(self):
        """测试清理定时器"""
        queue = EnhancedBatchQueue(cleanup_interval=0.1)
        
        queue.start()
        time.sleep(0.15)  # 等待定时器触发
        queue.stop()
        
        # 验证定时器正常工作（无异常）


class TestJobStateTransitions:
    """任务状态转换测试"""
    
    def test_job_estimated_time_zero_progress(self, tmp_path):
        """测试零进度的估计时间"""
        test_file = tmp_path / "zero_progress.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob("zero_job", test_file, estimated_duration=30.0)
        job.started_at = time.time()
        job.progress = 0.0
        
        remaining = job.get_estimated_remaining_time()
        assert remaining == 30.0
    
    def test_job_estimated_time_negative_protection(self, tmp_path):
        """测试估计时间的负数保护"""
        test_file = tmp_path / "negative_test.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob("negative_job", test_file, estimated_duration=10.0)
        job.started_at = time.time() - 30  # 30秒前开始
        job.progress = 10.0  # 只有10%进度
        
        remaining = job.get_estimated_remaining_time()
        assert remaining >= 0  # 不应该是负数
    
    def test_job_retry_count_increment(self, tmp_path):
        """测试重试计数递增"""
        test_file = tmp_path / "retry_count.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob("retry_job", test_file)
        initial_count = job.retry_count
        
        job.reset_for_retry()
        assert job.retry_count == initial_count + 1
        
        job.reset_for_retry()
        assert job.retry_count == initial_count + 2


class TestQueueManagerEdgeCases:
    """QueueManager边界情况"""
    
    def test_create_queue_invalid_name(self):
        """测试无效队列名称"""
        from voice_came.core.enhanced_batch_queue import QueueManager
        
        manager = QueueManager()
        
        with pytest.raises(ValueError):
            manager.create_queue("")
    
    def test_remove_nonexistent_queue(self):
        """测试移除不存在的队列"""
        from voice_came.core.enhanced_batch_queue import QueueManager
        
        manager = QueueManager()
        
        with pytest.raises(KeyError):
            manager.remove_queue("nonexistent")
    
    def test_global_metrics_empty(self):
        """测试空管理器的全局指标"""
        from voice_came.core.enhanced_batch_queue import QueueManager
        
        manager = QueueManager()
        metrics = manager.get_global_metrics()
        
        assert metrics['total_queues'] == 0
        assert metrics['total_jobs'] == 0


class TestMonitoringOperations:
    """监控操作测试"""
    
    def test_monitoring_disabled(self):
        """测试禁用监控"""
        queue = EnhancedBatchQueue(enable_monitoring=False)
        queue.start()
        
        assert queue._performance_monitor is None
        queue.stop()
    
    def test_monitoring_enabled(self):
        """测试启用监控"""
        queue = EnhancedBatchQueue(enable_monitoring=True)
        queue.start()
        
        assert queue._performance_monitor is not None
        queue.stop() 