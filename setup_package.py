"""
Voice-came 语音翻译系统包安装脚本
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="voice-came",
    version="0.1.0",
    author="Voice-came Team",
    author_email="<EMAIL>",
    description="基于WhisperX和本地LLM的智能语音翻译系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/voice-came/voice-came",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Sound/Audio :: Speech",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.8",
    install_requires=[
        # 基础依赖将在后续子任务中添加
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.4.0",
            "isort>=5.12.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "voice-came=voice_came.main:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
) 