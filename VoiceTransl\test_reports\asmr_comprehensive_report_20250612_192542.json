{"test_session": {"start_time": 1749727542.746235, "config": {"test_suites": {"basic_validation": {"description": "基础ASMR验证测试", "enabled": true, "test_files": ["test_data/asmr_whisper_sample.wav", "test_data/asmr_tapping_sample.wav", "test_data/normal_speech_sample.wav"], "reference_texts": ["轻柔的耳语声音", "轻敲声音效果", "正常语音内容"]}, "performance_benchmark": {"description": "性能基准测试", "enabled": true, "test_files": ["test_data/benchmark_short.wav", "test_data/benchmark_medium.wav", "test_data/benchmark_long.wav"], "max_processing_time": {"short": 30, "medium": 120, "long": 300}}, "engine_comparison": {"description": "引擎对比测试", "enabled": true, "test_files": ["test_data/comparison_sample.wav"], "engines_to_test": ["whisper_cpp", "faster_whisper"]}, "asmr_detection_accuracy": {"description": "ASMR检测精度测试", "enabled": true, "positive_samples": ["test_data/asmr_positive_1.wav", "test_data/asmr_positive_2.wav"], "negative_samples": ["test_data/normal_speech_1.wav", "test_data/music_sample.wav"]}}, "test_data_generation": {"auto_generate": true, "synthetic_samples": {"asmr_whisper": {"duration": 30, "volume_range": [0.01, 0.05], "frequency_range": [100, 2000]}, "normal_speech": {"duration": 30, "volume_range": [0.1, 0.8], "frequency_range": [200, 8000]}}}, "reporting": {"output_dir": "test_reports", "formats": ["json", "html", "csv"], "include_audio_analysis": true, "generate_charts": true}}, "end_time": 1749727542.7522345, "total_duration": 0.005999565124511719}, "test_results": {"basic_validation": {}, "performance_benchmark": {"test_type": "performance_benchmark", "total_tests": 0, "passed_tests": 0, "pass_rate": 0, "average_speed_ratio": 0, "results": []}, "engine_comparison": {}, "asmr_detection_accuracy": {}}, "overall_summary": {"total_test_suites": 4, "successful_suites": 4, "total_individual_tests": 0, "passed_individual_tests": 0, "overall_pass_rate": 0}}