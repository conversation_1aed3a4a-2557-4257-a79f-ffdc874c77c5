#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI模块初始化文件测试
"""

import sys
import os
import pytest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

import voice_came.ui as ui_module


class TestUIModuleImports:
    """测试UI模块的导入功能"""
    
    def test_module_has_all_exports(self):
        """测试模块包含所有预期的导出"""
        expected_exports = [
            "UploadWidget",
            "FileDropArea",
        ]
        
        # 检查__all__列表
        assert hasattr(ui_module, '__all__')
        assert ui_module.__all__ == expected_exports
        
        # 检查每个导出是否真正可用
        for export_name in expected_exports:
            assert hasattr(ui_module, export_name)
            exported_item = getattr(ui_module, export_name)
            assert exported_item is not None
    
    def test_version_exists(self):
        """测试版本号存在"""
        assert hasattr(ui_module, '__version__')
        assert ui_module.__version__ == '0.1.0'
        assert isinstance(ui_module.__version__, str)
    
    def test_author_exists(self):
        """测试作者信息存在"""
        assert hasattr(ui_module, '__author__')
        assert ui_module.__author__ == 'Voice-came Team'
        assert isinstance(ui_module.__author__, str)
    
    def test_module_docstring(self):
        """测试模块文档字符串"""
        assert ui_module.__doc__ is not None
        assert "UI 模块" in ui_module.__doc__
        assert "Web界面" in ui_module.__doc__
        assert "文件上传" in ui_module.__doc__
    
    def test_individual_imports(self):
        """测试各个组件可以单独导入"""
        from voice_came.ui import UploadWidget
        from voice_came.ui import FileDropArea
        
        # 检查导入的对象不为空
        assert UploadWidget is not None
        assert FileDropArea is not None
    
    def test_class_types(self):
        """测试导入的类型"""
        # UploadWidget 应该是一个类
        assert isinstance(ui_module.UploadWidget, type)
        
        # FileDropArea 应该是一个类
        assert isinstance(ui_module.FileDropArea, type)
    
    def test_star_import(self):
        """测试星号导入功能"""
        # 创建一个新的命名空间来测试星号导入
        namespace = {}
        exec("from voice_came.ui import *", namespace)
        
        # 检查所有预期的导出都被导入
        expected_exports = [
            "UploadWidget",
            "FileDropArea",
        ]
        
        for export_name in expected_exports:
            assert export_name in namespace
            assert namespace[export_name] is not None
    
    def test_module_attributes(self):
        """测试模块属性"""
        # 检查模块名
        assert ui_module.__name__ == 'voice_came.ui'
        
        # 检查包路径
        assert hasattr(ui_module, '__path__')
        
        # 检查文件路径
        assert hasattr(ui_module, '__file__')
        assert ui_module.__file__.endswith('__init__.py')


class TestUIModuleClasses:
    """测试UI模块中各个类的基本属性"""
    
    def test_upload_widget_properties(self):
        """测试UploadWidget类属性"""
        # UploadWidget是一个类
        assert isinstance(ui_module.UploadWidget, type)
        
        # 检查类名
        assert ui_module.UploadWidget.__name__ == 'UploadWidget'
    
    def test_file_drop_area_properties(self):
        """测试FileDropArea类属性"""
        # FileDropArea是一个类
        assert isinstance(ui_module.FileDropArea, type)
        
        # 检查类名
        assert ui_module.FileDropArea.__name__ == 'FileDropArea'


class TestUIModuleEdgeCases:
    """测试UI模块的边界情况"""
    
    def test_module_reload(self):
        """测试模块重新加载"""
        import importlib
        
        # 重新加载模块
        reloaded_module = importlib.reload(ui_module)
        
        # 检查重新加载后的模块
        assert reloaded_module is not None
        assert hasattr(reloaded_module, '__all__')
        assert hasattr(reloaded_module, '__version__')
        assert hasattr(reloaded_module, '__author__')
    
    def test_imports_consistency(self):
        """测试导入的一致性"""
        for export_name in ui_module.__all__:
            # 每个导出都应该存在于模块中
            assert hasattr(ui_module, export_name)
            
            # 获取的对象应该不为空
            exported_item = getattr(ui_module, export_name)
            assert exported_item is not None
    
    def test_no_extra_public_exports(self):
        """测试没有额外的公共导出"""
        # 获取所有公共属性（不以下划线开头的）
        public_attrs = [attr for attr in dir(ui_module) 
                       if not attr.startswith('_')]
        
        # 过滤掉已知的模块属性
        known_attrs = set(ui_module.__all__ + ['__version__', '__author__'])
        
        # 检查是否有未在__all__中声明的公共属性
        extra_attrs = set(public_attrs) - known_attrs
        
        # 允许一些常见的模块属性和模块文件名
        allowed_extra = {'upload_widget', 'file_drop_area'}
        
        unexpected_attrs = extra_attrs - allowed_extra
        assert len(unexpected_attrs) == 0, f"发现未预期的公共属性: {unexpected_attrs}"
    
    def test_module_metadata(self):
        """测试模块元数据"""
        # 版本号应该是字符串且格式合理
        version = ui_module.__version__
        assert isinstance(version, str)
        assert len(version.split('.')) >= 2  # 至少有主版本号和次版本号
        
        # 作者信息应该是字符串
        author = ui_module.__author__
        assert isinstance(author, str)
        assert len(author) > 0
        
        # 文档字符串应该存在且有意义
        doc = ui_module.__doc__
        assert isinstance(doc, str)
        assert len(doc) > 10  # 应该有实质内容
    
    def test_module_structure(self):
        """测试模块结构完整性"""
        # 检查模块的基本结构
        assert hasattr(ui_module, '__name__')
        assert hasattr(ui_module, '__file__')
        assert hasattr(ui_module, '__package__')
        
        # 包名应该正确
        assert ui_module.__package__ == 'voice_came.ui'
    
    def test_class_inheritance(self):
        """测试类继承关系"""
        # 所有的UI类都应该是object的子类
        for class_name in ui_module.__all__:
            cls = getattr(ui_module, class_name)
            assert issubclass(cls, object)
    
    def test_class_names_consistency(self):
        """测试类名与__all__的一致性"""
        for class_name in ui_module.__all__:
            # 类名应该存在于模块中
            assert hasattr(ui_module, class_name)
            
            # 获取的对象应该是一个类
            cls = getattr(ui_module, class_name)
            assert isinstance(cls, type)
            
            # 类的名称应该与__all__中的名称一致
            assert cls.__name__ == class_name


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 