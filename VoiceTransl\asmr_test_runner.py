#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASMR算法验证测试运行器

作为Voice-came技术负责人，提供完整的ASMR算法验证测试套件
包含:
1. 自动化测试流程
2. 性能基准测试
3. 回归测试
4. 压力测试
5. 详细的测试报告
"""

import os
import sys
import json
import time
import argparse
from pathlib import Path
from typing import List, Dict
from asmr_validator import ASMRValidator, ASMRValidationResult

class ASMRTestRunner:
    """ASMR算法验证测试运行器"""
    
    def __init__(self, test_config_path: str = "asmr_test_config.json"):
        self.test_config_path = test_config_path
        self.test_config = self._load_test_config()
        self.validator = ASMRValidator()
        
    def _load_test_config(self) -> Dict:
        """加载测试配置"""
        default_config = {
            "test_suites": {
                "basic_validation": {
                    "description": "基础ASMR验证测试",
                    "enabled": True,
                    "test_files": [
                        # 示例测试文件路径
                        "test_data/asmr_whisper_sample.wav",
                        "test_data/asmr_tapping_sample.wav",
                        "test_data/normal_speech_sample.wav"
                    ],
                    "reference_texts": [
                        "轻柔的耳语声音",
                        "轻敲声音效果",
                        "正常语音内容"
                    ]
                },
                "performance_benchmark": {
                    "description": "性能基准测试",
                    "enabled": True,
                    "test_files": [
                        "test_data/benchmark_short.wav",
                        "test_data/benchmark_medium.wav",
                        "test_data/benchmark_long.wav"
                    ],
                    "max_processing_time": {
                        "short": 30,   # 短音频(<1分钟)
                        "medium": 120, # 中等音频(1-5分钟)
                        "long": 300    # 长音频(>5分钟)
                    }
                },
                "engine_comparison": {
                    "description": "引擎对比测试",
                    "enabled": True,
                    "test_files": [
                        "test_data/comparison_sample.wav"
                    ],
                    "engines_to_test": ["whisper_cpp", "faster_whisper"]
                },
                "asmr_detection_accuracy": {
                    "description": "ASMR检测精度测试",
                    "enabled": True,
                    "positive_samples": [
                        "test_data/asmr_positive_1.wav",
                        "test_data/asmr_positive_2.wav"
                    ],
                    "negative_samples": [
                        "test_data/normal_speech_1.wav",
                        "test_data/music_sample.wav"
                    ]
                }
            },
            "test_data_generation": {
                "auto_generate": True,
                "synthetic_samples": {
                    "asmr_whisper": {
                        "duration": 30,
                        "volume_range": [0.01, 0.05],
                        "frequency_range": [100, 2000]
                    },
                    "normal_speech": {
                        "duration": 30,
                        "volume_range": [0.1, 0.8],
                        "frequency_range": [200, 8000]
                    }
                }
            },
            "reporting": {
                "output_dir": "test_reports",
                "formats": ["json", "html", "csv"],
                "include_audio_analysis": True,
                "generate_charts": True
            }
        }
        
        if os.path.exists(self.test_config_path):
            with open(self.test_config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        else:
            with open(self.test_config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
                
        return default_config
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("[测试环境] 初始化测试环境...")
        
        # 创建测试目录
        test_dirs = ['test_data', 'test_reports', 'test_outputs']
        for dir_name in test_dirs:
            os.makedirs(dir_name, exist_ok=True)
            print(f"[INFO] 创建目录: {dir_name}")
        
        # 检查测试数据
        self._check_test_data()
        
        # 生成合成测试数据(如果需要)
        if self.test_config['test_data_generation']['auto_generate']:
            self._generate_synthetic_test_data()
    
    def _check_test_data(self):
        """检查测试数据可用性"""
        print("[测试数据] 检查测试文件...")
        
        missing_files = []
        for suite_name, suite_config in self.test_config['test_suites'].items():
            if not suite_config.get('enabled', True):
                continue
                
            test_files = suite_config.get('test_files', [])
            for file_path in test_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
        
        if missing_files:
            print(f"[WARNING] 缺少测试文件: {len(missing_files)} 个")
            for file_path in missing_files[:5]:  # 只显示前5个
                print(f"  - {file_path}")
            if len(missing_files) > 5:
                print(f"  ... 还有 {len(missing_files) - 5} 个文件")
        else:
            print("[INFO] 所有测试文件检查通过")
    
    def _generate_synthetic_test_data(self):
        """生成合成测试数据"""
        print("[数据生成] 生成合成测试数据...")
        
        try:
            import numpy as np
            import soundfile as sf
            
            synthetic_config = self.test_config['test_data_generation']['synthetic_samples']
            
            for sample_type, config in synthetic_config.items():
                output_path = f"test_data/synthetic_{sample_type}.wav"
                
                if os.path.exists(output_path):
                    continue
                
                # 生成音频信号
                duration = config['duration']
                sample_rate = 16000
                t = np.linspace(0, duration, int(sample_rate * duration))
                
                if sample_type == 'asmr_whisper':
                    # 生成低音量、低频的ASMR样本
                    freq = np.random.uniform(config['frequency_range'][0], config['frequency_range'][1])
                    volume = np.random.uniform(config['volume_range'][0], config['volume_range'][1])
                    
                    # 基础正弦波 + 噪声
                    signal = volume * np.sin(2 * np.pi * freq * t)
                    noise = 0.01 * np.random.normal(0, 1, len(t))
                    audio = signal + noise
                    
                    # 添加静音段
                    silence_mask = np.random.random(len(t)) < 0.3
                    audio[silence_mask] *= 0.1
                    
                elif sample_type == 'normal_speech':
                    # 生成正常语音样本
                    freq = np.random.uniform(config['frequency_range'][0], config['frequency_range'][1])
                    volume = np.random.uniform(config['volume_range'][0], config['volume_range'][1])
                    
                    # 复合频率模拟语音
                    audio = volume * (np.sin(2 * np.pi * freq * t) + 
                                    0.5 * np.sin(2 * np.pi * freq * 2 * t) +
                                    0.3 * np.sin(2 * np.pi * freq * 3 * t))
                    
                    # 添加语音包络
                    envelope = 0.5 * (1 + np.sin(2 * np.pi * 2 * t))
                    audio *= envelope
                
                # 保存音频文件
                sf.write(output_path, audio, sample_rate)
                print(f"[INFO] 生成合成音频: {output_path}")
                
        except ImportError:
            print("[WARNING] 缺少音频生成依赖(soundfile, numpy)，跳过合成数据生成")
        except Exception as e:
            print(f"[ERROR] 合成数据生成失败: {e}")
    
    def run_basic_validation_tests(self) -> Dict:
        """运行基础验证测试"""
        print("\n" + "="*50)
        print("🧪 运行基础ASMR验证测试")
        print("="*50)
        
        suite_config = self.test_config['test_suites']['basic_validation']
        if not suite_config.get('enabled', True):
            print("[SKIP] 基础验证测试已禁用")
            return {}
        
        test_files = suite_config.get('test_files', [])
        reference_texts = suite_config.get('reference_texts', [])
        
        # 过滤存在的文件
        valid_files = [f for f in test_files if os.path.exists(f)]
        if not valid_files:
            print("[ERROR] 没有可用的测试文件")
            return {}
        
        print(f"[INFO] 测试文件数量: {len(valid_files)}")
        
        # 运行验证
        results = self.validator.batch_validate(valid_files, reference_texts)
        
        # 分析结果
        passed = len([r for r in results if r.validation_status == "PASSED"])
        asmr_detected = len([r for r in results if r.is_asmr_detected])
        
        test_summary = {
            'test_type': 'basic_validation',
            'total_files': len(results),
            'passed_files': passed,
            'pass_rate': passed / len(results) if results else 0,
            'asmr_detected': asmr_detected,
            'asmr_detection_rate': asmr_detected / len(results) if results else 0,
            'results': results
        }
        
        print(f"[结果] 通过率: {test_summary['pass_rate']:.2%}")
        print(f"[结果] ASMR检测率: {test_summary['asmr_detection_rate']:.2%}")
        
        return test_summary
    
    def run_performance_benchmark(self) -> Dict:
        """运行性能基准测试"""
        print("\n" + "="*50)
        print("⚡ 运行性能基准测试")
        print("="*50)
        
        suite_config = self.test_config['test_suites']['performance_benchmark']
        if not suite_config.get('enabled', True):
            print("[SKIP] 性能基准测试已禁用")
            return {}
        
        test_files = suite_config.get('test_files', [])
        max_times = suite_config.get('max_processing_time', {})
        
        # 过滤存在的文件
        valid_files = [f for f in test_files if os.path.exists(f)]
        if not valid_files:
            print("[WARNING] 没有可用的性能测试文件，使用基础测试文件")
            valid_files = [f for f in self.test_config['test_suites']['basic_validation']['test_files'] 
                          if os.path.exists(f)][:1]  # 只取一个文件
        
        performance_results = []
        
        for file_path in valid_files:
            print(f"[INFO] 性能测试: {os.path.basename(file_path)}")
            
            start_time = time.time()
            result = self.validator.validate_single_file(file_path)
            total_time = time.time() - start_time
            
            # 判断文件类型
            file_type = 'short'
            if result.duration > 300:  # 5分钟
                file_type = 'long'
            elif result.duration > 60:  # 1分钟
                file_type = 'medium'
            
            max_allowed_time = max_times.get(file_type, 300)
            performance_passed = total_time <= max_allowed_time
            
            perf_result = {
                'file_path': file_path,
                'file_type': file_type,
                'duration': result.duration,
                'processing_time': total_time,
                'max_allowed_time': max_allowed_time,
                'performance_passed': performance_passed,
                'speed_ratio': result.duration / total_time if total_time > 0 else 0,
                'validation_result': result
            }
            
            performance_results.append(perf_result)
            
            print(f"  处理时间: {total_time:.2f}s (限制: {max_allowed_time}s)")
            print(f"  速度比: {perf_result['speed_ratio']:.2f}x")
            print(f"  性能测试: {'✅ PASS' if performance_passed else '❌ FAIL'}")
        
        # 统计结果
        total_tests = len(performance_results)
        passed_tests = len([r for r in performance_results if r['performance_passed']])
        avg_speed_ratio = sum(r['speed_ratio'] for r in performance_results) / total_tests if total_tests > 0 else 0
        
        benchmark_summary = {
            'test_type': 'performance_benchmark',
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'pass_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'average_speed_ratio': avg_speed_ratio,
            'results': performance_results
        }
        
        print(f"[结果] 性能通过率: {benchmark_summary['pass_rate']:.2%}")
        print(f"[结果] 平均速度比: {avg_speed_ratio:.2f}x")
        
        return benchmark_summary
    
    def run_engine_comparison(self) -> Dict:
        """运行引擎对比测试"""
        print("\n" + "="*50)
        print("🔄 运行引擎对比测试")
        print("="*50)
        
        suite_config = self.test_config['test_suites']['engine_comparison']
        if not suite_config.get('enabled', True):
            print("[SKIP] 引擎对比测试已禁用")
            return {}
        
        test_files = suite_config.get('test_files', [])
        engines_to_test = suite_config.get('engines_to_test', ['whisper_cpp', 'faster_whisper'])
        
        # 使用第一个可用文件进行对比
        test_file = None
        for file_path in test_files:
            if os.path.exists(file_path):
                test_file = file_path
                break
        
        if not test_file:
            # 使用基础测试文件
            basic_files = self.test_config['test_suites']['basic_validation']['test_files']
            for file_path in basic_files:
                if os.path.exists(file_path):
                    test_file = file_path
                    break
        
        if not test_file:
            print("[ERROR] 没有可用的对比测试文件")
            return {}
        
        print(f"[INFO] 对比测试文件: {os.path.basename(test_file)}")
        
        comparison_results = {}
        
        # 临时修改配置，逐个测试引擎
        original_config = self.validator.config['engines'].copy()
        
        for engine_name in engines_to_test:
            if engine_name not in original_config:
                continue
            
            print(f"\n[测试] 引擎: {engine_name}")
            
            # 只启用当前引擎
            for eng in self.validator.config['engines']:
                self.validator.config['engines'][eng]['enabled'] = (eng == engine_name)
            
            start_time = time.time()
            result = self.validator.validate_single_file(test_file)
            test_time = time.time() - start_time
            
            comparison_results[engine_name] = {
                'validation_result': result,
                'test_time': test_time,
                'transcription_length': len(result.transcription),
                'confidence': result.whisper_confidence
            }
            
            print(f"  处理时间: {test_time:.2f}s")
            print(f"  转录长度: {len(result.transcription)} 字符")
            print(f"  置信度: {result.whisper_confidence:.3f}")
            print(f"  验证状态: {result.validation_status}")
        
        # 恢复原始配置
        self.validator.config['engines'] = original_config
        
        # 分析对比结果
        if len(comparison_results) >= 2:
            engines = list(comparison_results.keys())
            engine1, engine2 = engines[0], engines[1]
            
            time_diff = comparison_results[engine2]['test_time'] - comparison_results[engine1]['test_time']
            faster_engine = engine1 if time_diff > 0 else engine2
            
            print(f"\n[对比结果]")
            print(f"  更快引擎: {faster_engine}")
            print(f"  时间差异: {abs(time_diff):.2f}s")
        
        comparison_summary = {
            'test_type': 'engine_comparison',
            'test_file': test_file,
            'engines_tested': list(comparison_results.keys()),
            'results': comparison_results
        }
        
        return comparison_summary
    
    def run_asmr_detection_accuracy(self) -> Dict:
        """运行ASMR检测精度测试"""
        print("\n" + "="*50)
        print("🎯 运行ASMR检测精度测试")
        print("="*50)
        
        suite_config = self.test_config['test_suites']['asmr_detection_accuracy']
        if not suite_config.get('enabled', True):
            print("[SKIP] ASMR检测精度测试已禁用")
            return {}
        
        positive_samples = suite_config.get('positive_samples', [])
        negative_samples = suite_config.get('negative_samples', [])
        
        # 过滤存在的文件
        valid_positive = [f for f in positive_samples if os.path.exists(f)]
        valid_negative = [f for f in negative_samples if os.path.exists(f)]
        
        if not valid_positive and not valid_negative:
            print("[WARNING] 没有可用的ASMR检测测试文件")
            return {}
        
        detection_results = {
            'true_positives': 0,
            'false_positives': 0,
            'true_negatives': 0,
            'false_negatives': 0,
            'positive_results': [],
            'negative_results': []
        }
        
        # 测试正样本(应该检测为ASMR)
        print(f"[INFO] 测试ASMR正样本: {len(valid_positive)} 个")
        for file_path in valid_positive:
            result = self.validator.validate_single_file(file_path)
            detection_results['positive_results'].append(result)
            
            if result.is_asmr_detected:
                detection_results['true_positives'] += 1
                print(f"  ✅ {os.path.basename(file_path)}: 正确检测为ASMR")
            else:
                detection_results['false_negatives'] += 1
                print(f"  ❌ {os.path.basename(file_path)}: 未检测为ASMR (漏检)")
        
        # 测试负样本(不应该检测为ASMR)
        print(f"[INFO] 测试非ASMR样本: {len(valid_negative)} 个")
        for file_path in valid_negative:
            result = self.validator.validate_single_file(file_path)
            detection_results['negative_results'].append(result)
            
            if not result.is_asmr_detected:
                detection_results['true_negatives'] += 1
                print(f"  ✅ {os.path.basename(file_path)}: 正确识别为非ASMR")
            else:
                detection_results['false_positives'] += 1
                print(f"  ❌ {os.path.basename(file_path)}: 误检测为ASMR (误报)")
        
        # 计算精度指标
        tp = detection_results['true_positives']
        fp = detection_results['false_positives']
        tn = detection_results['true_negatives']
        fn = detection_results['false_negatives']
        
        total = tp + fp + tn + fn
        accuracy = (tp + tn) / total if total > 0 else 0
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        accuracy_summary = {
            'test_type': 'asmr_detection_accuracy',
            'confusion_matrix': {
                'true_positives': tp,
                'false_positives': fp,
                'true_negatives': tn,
                'false_negatives': fn
            },
            'metrics': {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score
            },
            'results': detection_results
        }
        
        print(f"\n[检测精度结果]")
        print(f"  准确率: {accuracy:.3f}")
        print(f"  精确率: {precision:.3f}")
        print(f"  召回率: {recall:.3f}")
        print(f"  F1分数: {f1_score:.3f}")
        
        return accuracy_summary
    
    def run_all_tests(self) -> Dict:
        """运行所有测试套件"""
        print("\n" + "="*60)
        print("🚀 Voice-came ASMR算法验证 - 完整测试套件")
        print("="*60)
        
        # 设置测试环境
        self.setup_test_environment()
        
        all_results = {
            'test_session': {
                'start_time': time.time(),
                'config': self.test_config
            },
            'test_results': {}
        }
        
        # 运行各个测试套件
        test_suites = [
            ('basic_validation', self.run_basic_validation_tests),
            ('performance_benchmark', self.run_performance_benchmark),
            ('engine_comparison', self.run_engine_comparison),
            ('asmr_detection_accuracy', self.run_asmr_detection_accuracy)
        ]
        
        for suite_name, test_function in test_suites:
            try:
                print(f"\n🔄 开始测试套件: {suite_name}")
                result = test_function()
                all_results['test_results'][suite_name] = result
                print(f"✅ 完成测试套件: {suite_name}")
            except Exception as e:
                print(f"❌ 测试套件失败: {suite_name} - {e}")
                all_results['test_results'][suite_name] = {
                    'error': str(e),
                    'status': 'FAILED'
                }
        
        all_results['test_session']['end_time'] = time.time()
        all_results['test_session']['total_duration'] = (
            all_results['test_session']['end_time'] - 
            all_results['test_session']['start_time']
        )
        
        # 生成综合报告
        report_path = self.generate_comprehensive_report(all_results)
        
        print(f"\n" + "="*60)
        print("📊 测试完成！")
        print(f"📋 详细报告: {report_path}")
        print(f"⏱️  总耗时: {all_results['test_session']['total_duration']:.2f}秒")
        print("="*60)
        
        return all_results
    
    def generate_comprehensive_report(self, test_results: Dict) -> str:
        """生成综合测试报告"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_path = f"test_reports/asmr_comprehensive_report_{timestamp}.json"
        
        # 确保报告目录存在
        os.makedirs("test_reports", exist_ok=True)
        
        # 计算总体统计
        total_tests = 0
        passed_tests = 0
        
        for suite_name, suite_result in test_results['test_results'].items():
            if isinstance(suite_result, dict) and 'total_tests' in suite_result:
                total_tests += suite_result.get('total_tests', 0)
                passed_tests += suite_result.get('passed_tests', 0)
            elif isinstance(suite_result, dict) and 'total_files' in suite_result:
                total_tests += suite_result.get('total_files', 0)
                passed_tests += suite_result.get('passed_files', 0)
        
        # 添加总体统计
        test_results['overall_summary'] = {
            'total_test_suites': len(test_results['test_results']),
            'successful_suites': len([r for r in test_results['test_results'].values() 
                                    if not isinstance(r, dict) or 'error' not in r]),
            'total_individual_tests': total_tests,
            'passed_individual_tests': passed_tests,
            'overall_pass_rate': passed_tests / total_tests if total_tests > 0 else 0
        }
        
        # 保存报告
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False, default=str)
        
        return report_path

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='Voice-came ASMR算法验证测试运行器')
    parser.add_argument('--suite', choices=['basic', 'performance', 'comparison', 'accuracy', 'all'], 
                       default='all', help='选择要运行的测试套件')
    parser.add_argument('--config', default='asmr_test_config.json', 
                       help='测试配置文件路径')
    
    args = parser.parse_args()
    
    # 初始化测试运行器
    runner = ASMRTestRunner(args.config)
    
    # 运行指定的测试套件
    if args.suite == 'basic':
        runner.run_basic_validation_tests()
    elif args.suite == 'performance':
        runner.run_performance_benchmark()
    elif args.suite == 'comparison':
        runner.run_engine_comparison()
    elif args.suite == 'accuracy':
        runner.run_asmr_detection_accuracy()
    else:  # 'all'
        runner.run_all_tests()

if __name__ == "__main__":
    main()