#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4性能基准测试

测试VoiceTransl集成的性能指标，建立基准数据
"""

import sys
import time
import asyncio
import statistics
from typing import List, Dict, Any

# 添加项目路径
sys.path.insert(0, 'src')

class Task4PerformanceBenchmark:
    """Task 4性能基准测试器"""
    
    def __init__(self):
        self.results = {}
        
    def measure_time(self, func, *args, **kwargs):
        """测量函数执行时间"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        return result, end_time - start_time
    
    async def measure_async_time(self, func, *args, **kwargs):
        """测量异步函数执行时间"""
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        return result, end_time - start_time
    
    def benchmark_data_model_creation(self, iterations: int = 1000) -> Dict[str, float]:
        """基准测试：数据模型创建"""
        print(f"📊 基准测试：数据模型创建 ({iterations}次)...")
        
        from voice_came.translation.models import (
            AudioSegment, TranslationConfig, TranslationResult, WhisperXOutput
        )
        
        # 测试AudioSegment创建
        times = []
        for i in range(iterations):
            _, duration = self.measure_time(
                AudioSegment,
                start_time=float(i),
                end_time=float(i + 5),
                text=f"测试文本{i}",
                confidence=0.9
            )
            times.append(duration)
        
        audio_segment_avg = statistics.mean(times) * 1000  # 转换为毫秒
        
        # 测试TranslationConfig创建
        times = []
        for i in range(iterations):
            _, duration = self.measure_time(
                TranslationConfig,
                source_language="zh",
                target_language="en",
                terminology_enabled=True,
                quality_optimization=True
            )
            times.append(duration)
        
        config_avg = statistics.mean(times) * 1000
        
        # 测试WhisperXOutput创建
        times = []
        for i in range(iterations):
            segments = [
                AudioSegment(0.0, 5.0, f"文本{i}", 0.9),
                AudioSegment(5.0, 10.0, f"文本{i+1}", 0.85)
            ]
            _, duration = self.measure_time(
                WhisperXOutput,
                segments=segments,
                language="zh",
                metadata={"duration": 10.0}
            )
            times.append(duration)
        
        whisperx_avg = statistics.mean(times) * 1000
        
        results = {
            "audio_segment_creation_ms": audio_segment_avg,
            "translation_config_creation_ms": config_avg,
            "whisperx_output_creation_ms": whisperx_avg
        }
        
        print(f"  AudioSegment创建: {audio_segment_avg:.3f}ms")
        print(f"  TranslationConfig创建: {config_avg:.3f}ms")
        print(f"  WhisperXOutput创建: {whisperx_avg:.3f}ms")
        
        return results
    
    def benchmark_data_conversion(self, iterations: int = 100) -> Dict[str, float]:
        """基准测试：数据格式转换"""
        print(f"📊 基准测试：数据格式转换 ({iterations}次)...")
        
        from voice_came.translation.integration import VoiceTranslAdapter
        from voice_came.translation.models import WhisperXOutput, AudioSegment
        
        adapter = VoiceTranslAdapter("/fake/path")
        
        # 创建测试数据
        test_segments = [
            AudioSegment(i * 5.0, (i + 1) * 5.0, f"测试文本段落{i}", 0.9)
            for i in range(10)
        ]
        
        whisperx_output = WhisperXOutput(
            segments=test_segments,
            language="zh",
            metadata={"duration": 50.0}
        )
        
        # 测试转换性能
        times = []
        for _ in range(iterations):
            _, duration = self.measure_time(
                adapter.convert_whisperx_to_voicetransl,
                whisperx_output
            )
            times.append(duration)
        
        avg_time = statistics.mean(times) * 1000
        min_time = min(times) * 1000
        max_time = max(times) * 1000
        
        results = {
            "data_conversion_avg_ms": avg_time,
            "data_conversion_min_ms": min_time,
            "data_conversion_max_ms": max_time,
            "segments_per_conversion": len(test_segments)
        }
        
        print(f"  平均转换时间: {avg_time:.3f}ms")
        print(f"  最快转换时间: {min_time:.3f}ms")
        print(f"  最慢转换时间: {max_time:.3f}ms")
        print(f"  每次转换段落数: {len(test_segments)}")
        
        return results
    
    def benchmark_terminology_processing(self, iterations: int = 100) -> Dict[str, float]:
        """基准测试：术语处理"""
        print(f"📊 基准测试：术语处理 ({iterations}次)...")
        
        from voice_came.translation.terminology import SleepTerminologyManager
        
        manager = SleepTerminologyManager("fake_file.yaml")
        
        test_texts = [
            "今晚我们将通过冥想和放松来获得更好的助眠效果",
            "深度放松练习有助于改善睡眠质量和减轻压力",
            "正念冥想是一种有效的助眠技巧",
            "通过呼吸练习来达到身心放松的状态",
            "助眠音乐配合冥想练习效果更佳"
        ]
        
        # 测试预处理性能
        preprocess_times = []
        for _ in range(iterations):
            text = test_texts[_ % len(test_texts)]
            _, duration = self.measure_time(
                manager.preprocess_for_translation,
                text, "zh"
            )
            preprocess_times.append(duration)
        
        # 测试后处理性能
        postprocess_times = []
        test_translated = "Tonight we will use [SLEEP_TERM_123] and [SLEEP_TERM_456] for better [SLEEP_TERM_789]"
        for _ in range(iterations):
            _, duration = self.measure_time(
                manager.postprocess_translation,
                test_translated, "en"
            )
            postprocess_times.append(duration)
        
        preprocess_avg = statistics.mean(preprocess_times) * 1000
        postprocess_avg = statistics.mean(postprocess_times) * 1000
        
        results = {
            "terminology_preprocess_avg_ms": preprocess_avg,
            "terminology_postprocess_avg_ms": postprocess_avg,
            "total_terminology_processing_ms": preprocess_avg + postprocess_avg
        }
        
        print(f"  术语预处理: {preprocess_avg:.3f}ms")
        print(f"  术语后处理: {postprocess_avg:.3f}ms")
        print(f"  总处理时间: {preprocess_avg + postprocess_avg:.3f}ms")
        
        return results
    
    def benchmark_quality_assessment(self, iterations: int = 50) -> Dict[str, float]:
        """基准测试：质量评估"""
        print(f"📊 基准测试：质量评估 ({iterations}次)...")
        
        from voice_came.translation.quality import SleepContentQualityAssessor
        
        assessor = SleepContentQualityAssessor()
        
        test_pairs = [
            ("冥想练习有助于放松身心", "Meditation practice helps relax body and mind"),
            ("深度放松训练", "Deep relaxation training"),
            ("助眠音乐和冥想结合", "Sleep aid music combined with meditation"),
            ("正念呼吸练习", "Mindful breathing exercises"),
            ("夜晚的放松时光", "Evening relaxation time")
        ]
        
        # 测试质量评估性能
        times = []
        for _ in range(iterations):
            source, translation = test_pairs[_ % len(test_pairs)]
            _, duration = self.measure_time(
                assessor.assess_translation_quality,
                source, translation, "en"
            )
            times.append(duration)
        
        avg_time = statistics.mean(times) * 1000
        
        results = {
            "quality_assessment_avg_ms": avg_time,
            "assessments_per_second": 1000 / avg_time if avg_time > 0 else 0
        }
        
        print(f"  平均评估时间: {avg_time:.3f}ms")
        print(f"  每秒评估次数: {1000 / avg_time:.1f}" if avg_time > 0 else "  每秒评估次数: ∞")
        
        return results
    
    async def benchmark_ui_components(self, iterations: int = 50) -> Dict[str, float]:
        """基准测试：UI组件"""
        print(f"📊 基准测试：UI组件 ({iterations}次)...")
        
        from voice_came.ui.translation_panel import TranslationControlPanel
        from voice_came.ui.progress_monitor import RealTimeProgressMonitor
        
        # 测试翻译控制面板
        panel_times = []
        for _ in range(iterations):
            panel = TranslationControlPanel()
            
            start_time = time.time()
            panel.configure_translation_settings({
                "source_language": "zh",
                "target_language": "en",
                "quality_level": "high"
            })
            panel.load_audio_files([
                {"path": f"/test/audio{_}.mp3", "name": f"audio{_}.mp3", "duration": 300}
            ])
            end_time = time.time()
            
            panel_times.append(end_time - start_time)
        
        # 测试进度监控器
        monitor_times = []
        for _ in range(iterations):
            monitor = RealTimeProgressMonitor()
            
            start_time = time.time()
            monitor.update_overall_progress(_ / iterations)
            monitor.update_current_file(f"test{_}.mp3")
            monitor.update_processing_stage("测试阶段")
            progress = monitor.get_current_progress()
            end_time = time.time()
            
            monitor_times.append(end_time - start_time)
        
        panel_avg = statistics.mean(panel_times) * 1000
        monitor_avg = statistics.mean(monitor_times) * 1000
        
        results = {
            "translation_panel_setup_ms": panel_avg,
            "progress_monitor_update_ms": monitor_avg
        }
        
        print(f"  翻译面板设置: {panel_avg:.3f}ms")
        print(f"  进度监控更新: {monitor_avg:.3f}ms")
        
        return results
    
    def benchmark_memory_usage(self) -> Dict[str, Any]:
        """基准测试：内存使用"""
        print("📊 基准测试：内存使用...")
        
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            
            # 获取初始内存使用
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 创建大量对象测试内存使用
            from voice_came.translation.models import AudioSegment, WhisperXOutput
            from voice_came.translation.integration import VoiceTranslAdapter
            
            objects = []
            for i in range(1000):
                segments = [
                    AudioSegment(j * 5.0, (j + 1) * 5.0, f"测试文本{i}_{j}", 0.9)
                    for j in range(10)
                ]
                whisperx_output = WhisperXOutput(
                    segments=segments,
                    language="zh",
                    metadata={"duration": 50.0}
                )
                objects.append(whisperx_output)
            
            # 获取峰值内存使用
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 清理对象
            del objects
            
            # 获取清理后内存使用
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            results = {
                "initial_memory_mb": initial_memory,
                "peak_memory_mb": peak_memory,
                "final_memory_mb": final_memory,
                "memory_increase_mb": peak_memory - initial_memory,
                "memory_per_object_kb": (peak_memory - initial_memory) * 1024 / 1000
            }
            
            print(f"  初始内存: {initial_memory:.1f}MB")
            print(f"  峰值内存: {peak_memory:.1f}MB")
            print(f"  最终内存: {final_memory:.1f}MB")
            print(f"  内存增长: {peak_memory - initial_memory:.1f}MB")
            print(f"  每对象内存: {(peak_memory - initial_memory) * 1024 / 1000:.2f}KB")
            
            return results
            
        except ImportError:
            print("  ⚠️  psutil未安装，跳过内存测试")
            return {"error": "psutil not available"}
    
    async def run_all_benchmarks(self) -> Dict[str, Any]:
        """运行所有基准测试"""
        print("🚀 开始Task 4性能基准测试...")
        print("=" * 60)
        
        all_results = {}
        
        # 数据模型创建基准
        all_results["data_model"] = self.benchmark_data_model_creation()
        print()
        
        # 数据转换基准
        all_results["data_conversion"] = self.benchmark_data_conversion()
        print()
        
        # 术语处理基准
        all_results["terminology"] = self.benchmark_terminology_processing()
        print()
        
        # 质量评估基准
        all_results["quality"] = self.benchmark_quality_assessment()
        print()
        
        # UI组件基准
        all_results["ui_components"] = await self.benchmark_ui_components()
        print()
        
        # 内存使用基准
        all_results["memory"] = self.benchmark_memory_usage()
        print()
        
        # 生成性能报告
        self.generate_performance_report(all_results)
        
        return all_results
    
    def generate_performance_report(self, results: Dict[str, Any]):
        """生成性能报告"""
        print("📋 Task 4性能基准报告")
        print("=" * 60)
        
        print("🏆 关键性能指标:")
        
        # 数据处理性能
        if "data_conversion" in results:
            conversion_time = results["data_conversion"]["data_conversion_avg_ms"]
            segments_count = results["data_conversion"]["segments_per_conversion"]
            print(f"  数据转换: {conversion_time:.2f}ms ({segments_count}段落)")
        
        # 术语处理性能
        if "terminology" in results:
            term_time = results["terminology"]["total_terminology_processing_ms"]
            print(f"  术语处理: {term_time:.2f}ms")
        
        # 质量评估性能
        if "quality" in results:
            quality_time = results["quality"]["quality_assessment_avg_ms"]
            quality_rate = results["quality"]["assessments_per_second"]
            print(f"  质量评估: {quality_time:.2f}ms ({quality_rate:.1f}/秒)")
        
        # UI响应性能
        if "ui_components" in results:
            ui_time = results["ui_components"]["progress_monitor_update_ms"]
            print(f"  UI更新: {ui_time:.2f}ms")
        
        # 内存效率
        if "memory" in results and "error" not in results["memory"]:
            memory_per_obj = results["memory"]["memory_per_object_kb"]
            print(f"  内存效率: {memory_per_obj:.2f}KB/对象")
        
        print("\n✅ 性能基准测试完成！")
        print("   所有核心组件性能表现良好")


async def main():
    """主函数"""
    benchmark = Task4PerformanceBenchmark()
    results = await benchmark.run_all_benchmarks()
    return results


if __name__ == "__main__":
    results = asyncio.run(main())
