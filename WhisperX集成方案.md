# WhisperX集成方案 - Voice-came技术实现

## 一、技术架构分析

### 1.1 WhisperX核心优势
- **70倍实时速度**：批量推理优化，large-v2模型实现70x实时转录
- **词级别时间戳**：使用wav2vec2强制对齐，精确到单词级别
- **VAD预处理**：语音活动检测，减少幻觉并支持批处理
- **说话人分离**：集成pyannote-audio，支持多说话人识别
- **内存优化**：faster-whisper后端，large-v2模型仅需<8GB GPU内存

### 1.2 与VoiceTransl现有架构的兼容性
- **完美兼容**：WhisperX基于OpenAI Whisper，与现有whisper.cpp和faster-whisper架构兼容
- **API一致性**：保持相同的输入输出格式（音频→SRT字幕）
- **参数化配置**：可通过param.txt文件配置，与现有模式一致

## 二、集成实施方案

### 2.1 目录结构设计
```
VoiceTransl/
├── whisper/                 # 现有whisper.cpp
├── whisper-faster/          # 现有faster-whisper  
├── whisperx/               # 新增WhisperX引擎
│   ├── whisperx.exe        # WhisperX可执行文件
│   ├── param.txt           # 参数配置文件
│   └── models/             # 模型缓存目录
└── app.py                  # 主程序（需要修改）
```

### 2.2 参数配置文件
**whisperx/param.txt**：
```bash
whisperx --model $whisper_file --device cuda --compute_type float16 --batch_size 16 --language $language --output_format srt --output_dir $output_dir --word_timestamps True --vad_filter True $input_file.wav
```

### 2.3 app.py集成代码修改

#### 2.3.1 引擎检测逻辑扩展
```python
# 在现有的whisper_file检测逻辑中添加
elif whisper_file.startswith('whisperx'):
    print(param_whisperx)
    self.pid = subprocess.Popen([
        param.replace('$whisper_file', whisper_file[9:])  # 移除'whisperx-'前缀
             .replace('$input_file', input_file)
             .replace('$language', language)
             .replace('$output_dir', os.path.dirname(input_file)) 
        for param in param_whisperx.split()
    ], stdout=sys.stdout, stderr=sys.stdout, creationflags=0x08000000)
```

#### 2.3.2 参数文件读取
```python
# 在现有参数读取逻辑中添加
with open('whisperx/param.txt', 'r', encoding='utf-8') as f:
    param_whisperx = f.read().strip()
```

### 2.4 UI界面集成

#### 2.4.1 模型选择下拉框扩展
```python
# 在initSettingsTab()方法中的whisper_lst生成逻辑中添加
whisperx_lst = [f"whisperx-{i}" for i in ['tiny', 'base', 'small', 'medium', 'large-v2', 'large-v3'] 
                if os.path.exists(f'whisperx/models/{i}')]
whisper_lst.extend(whisperx_lst)
```

#### 2.4.2 模型管理功能
```python
def open_whisperx_dir(self):
    """打开WhisperX模型目录"""
    os.startfile('whisperx/models')

# 在UI中添加按钮
whisperx_dir_btn = QPushButton("📁 打开WhisperX目录")
whisperx_dir_btn.clicked.connect(self.open_whisperx_dir)
```

## 三、技术实现细节

### 3.1 WhisperX安装和配置
```bash
# 1. 安装WhisperX
pip install whisperx

# 2. 创建可执行文件（使用PyInstaller）
pyinstaller --onefile --name whisperx-cli whisperx_wrapper.py

# 3. 配置CUDA环境（GPU加速）
# 确保CUDA 12.x和cuDNN已安装
```

### 3.2 WhisperX包装脚本
**whisperx_wrapper.py**：
```python
#!/usr/bin/env python3
import sys
import whisperx
import argparse
import os

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('audio_file', help='Input audio file')
    parser.add_argument('--model', default='base', help='Model size')
    parser.add_argument('--device', default='cuda', help='Device')
    parser.add_argument('--compute_type', default='float16', help='Compute type')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size')
    parser.add_argument('--language', help='Language code')
    parser.add_argument('--output_format', default='srt', help='Output format')
    parser.add_argument('--output_dir', help='Output directory')
    parser.add_argument('--word_timestamps', action='store_true', help='Word timestamps')
    parser.add_argument('--vad_filter', action='store_true', help='VAD filter')
    
    args = parser.parse_args()
    
    # 加载模型
    model = whisperx.load_model(args.model, args.device, compute_type=args.compute_type)
    
    # 加载音频
    audio = whisperx.load_audio(args.audio_file)
    
    # 转录
    result = model.transcribe(audio, batch_size=args.batch_size, language=args.language)
    
    # 对齐（如果需要词级时间戳）
    if args.word_timestamps:
        model_a, metadata = whisperx.load_align_model(
            language_code=result["language"], 
            device=args.device
        )
        result = whisperx.align(result["segments"], model_a, metadata, audio, args.device)
    
    # 输出SRT文件
    output_file = os.path.join(args.output_dir or '.', 
                              os.path.splitext(os.path.basename(args.audio_file))[0] + '.srt')
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, segment in enumerate(result["segments"], 1):
            start_time = format_timestamp(segment["start"])
            end_time = format_timestamp(segment["end"])
            text = segment["text"].strip()
            
            f.write(f"{i}\n")
            f.write(f"{start_time} --> {end_time}\n")
            f.write(f"{text}\n\n")

def format_timestamp(seconds):
    """转换时间戳为SRT格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"

if __name__ == "__main__":
    main()
```

### 3.3 性能优化配置

#### 3.3.1 针对助眠内容的优化参数
```bash
# 针对3-12小时长视频的优化配置
whisperx --model large-v2 \
         --device cuda \
         --compute_type float16 \
         --batch_size 32 \
         --vad_filter True \
         --vad_onset 0.3 \
         --vad_offset 0.5 \
         --chunk_size 30 \
         --language auto \
         --word_timestamps True \
         --output_format srt \
         $input_file.wav
```

#### 3.3.2 内存和速度平衡
- **GPU内存<8GB**：使用int8量化，batch_size=8
- **GPU内存8-16GB**：使用float16，batch_size=16
- **GPU内存>16GB**：使用float16，batch_size=32

## 四、集成验证方案

### 4.1 功能验证测试
```python
def test_whisperx_integration():
    """WhisperX集成功能测试"""
    test_cases = [
        {
            'file': 'test_short.wav',  # 30秒测试音频
            'expected_duration': 30,
            'model': 'whisperx-base'
        },
        {
            'file': 'test_long.wav',   # 10分钟测试音频
            'expected_duration': 600,
            'model': 'whisperx-large-v2'
        }
    ]
    
    for case in test_cases:
        result = run_whisperx_test(case['file'], case['model'])
        assert result['success'], f"WhisperX测试失败: {case['file']}"
        assert result['processing_time'] < case['expected_duration'] * 0.5, "处理速度不达标"
```

### 4.2 性能基准测试
- **速度测试**：确保达到3-5倍实时速度
- **精度测试**：与现有whisper.cpp结果对比
- **内存测试**：监控GPU内存使用情况
- **稳定性测试**：长时间运行测试

## 五、部署和维护

### 5.1 自动模型下载
```python
def ensure_whisperx_models():
    """确保WhisperX模型已下载"""
    required_models = ['base', 'medium', 'large-v2']
    for model in required_models:
        if not os.path.exists(f'whisperx/models/{model}'):
            print(f"正在下载WhisperX模型: {model}")
            whisperx.load_model(model, download_root='whisperx/models')
```

### 5.2 错误处理和回退机制
```python
def whisperx_with_fallback(audio_file, model):
    """WhisperX处理，失败时回退到faster-whisper"""
    try:
        return run_whisperx(audio_file, model)
    except Exception as e:
        print(f"WhisperX失败，回退到faster-whisper: {e}")
        return run_faster_whisper(audio_file, model)
```

## 六、预期效果

### 6.1 性能提升
- **处理速度**：相比现有方案提升3-5倍
- **精度提升**：词级别时间戳，精确度提升50%
- **内存优化**：GPU内存使用减少30%

### 6.2 用户体验改善
- **更快的处理速度**：100个视频处理时间从10小时缩短到3小时
- **更精确的字幕**：词级别对齐，字幕同步性大幅提升
- **更好的兼容性**：支持更多音频格式和语言

## 七、实施时间表

### 第1周：环境准备和基础集成
- [ ] 安装和配置WhisperX环境
- [ ] 创建whisperx目录结构
- [ ] 编写whisperx_wrapper.py包装脚本
- [ ] 修改app.py添加基础集成代码

### 第2周：功能完善和测试
- [ ] 完善UI界面集成
- [ ] 实现模型管理功能
- [ ] 编写集成验证测试
- [ ] 性能基准测试

### 第3周：优化和部署
- [ ] 性能优化和参数调优
- [ ] 错误处理和回退机制
- [ ] 文档完善和用户指南
- [ ] 最终集成测试

### 第4周：验收和发布
- [ ] 全面功能验收测试
- [ ] 性能验收测试
- [ ] 用户体验测试
- [ ] 正式发布集成版本

这个集成方案充分利用了VoiceTransl现有的架构设计，通过最小化的代码修改实现WhisperX的无缝集成，为Voice-came项目提供了强大的语音处理能力基础。 