#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4 Phase 3-4 实现验证脚本

验证翻译质量优化和UI集成的TDD Green实现是否正常工作
"""

import sys
import os
import asyncio
sys.path.append('src')

def test_sleep_content_prompt_generator():
    """测试助眠内容提示词生成器"""
    try:
        from voice_came.translation.optimization import SleepContentPromptGenerator
        
        # 创建提示词生成器
        generator = SleepContentPromptGenerator()
        print("✅ SleepContentPromptGenerator 创建成功")
        
        # 测试冥想提示词生成
        meditation_text = "让我们开始今晚的冥想练习，慢慢地深呼吸"
        meditation_prompt = generator.generate_meditation_prompt(meditation_text, None)
        print("✅ 冥想提示词生成功能正常")
        print(f"   提示词长度: {len(meditation_prompt)} 字符")
        
        # 测试自动选择提示词
        auto_prompt = generator.auto_select_prompt(meditation_text, "zh", "en")
        print("✅ 自动提示词选择功能正常")
        
        return True
    except Exception as e:
        print(f"❌ SleepContentPromptGenerator 测试失败: {e}")
        return False

def test_translation_quality_optimizer():
    """测试翻译质量优化器"""
    try:
        from voice_came.translation.optimization import TranslationQualityOptimizer
        from voice_came.translation.models import TranslationResult
        
        # 创建质量优化器
        optimizer = TranslationQualityOptimizer()
        print("✅ TranslationQualityOptimizer 创建成功")
        
        # 创建测试翻译结果
        test_result = TranslationResult(
            job_id="test_job",
            original_text="冥想练习",
            translated_text="thinking practice",  # 错误术语
            quality_score=0.4,
            terminology_applied=[],
            processing_time=1.0,
            metadata={}
        )
        
        # 测试质量改进
        improved_result = optimizer.improve_translation_quality(test_result)
        print("✅ 翻译质量改进功能正常")
        print(f"   改进前: {test_result.translated_text}")
        print(f"   改进后: {improved_result.translated_text}")
        print(f"   质量分数: {test_result.quality_score:.2f} → {improved_result.quality_score:.2f}")
        
        return True
    except Exception as e:
        print(f"❌ TranslationQualityOptimizer 测试失败: {e}")
        return False

def test_automatic_retry_manager():
    """测试自动重试管理器"""
    try:
        from voice_came.translation.optimization import AutomaticRetryManager
        from voice_came.translation.models import TranslationResult
        
        # 创建重试管理器
        retry_manager = AutomaticRetryManager(max_retries=3, quality_threshold=0.8)
        print("✅ AutomaticRetryManager 创建成功")
        
        # 测试重试判断
        low_quality_result = TranslationResult(
            job_id="test_job",
            original_text="测试文本",
            translated_text="test text",
            quality_score=0.5,  # 低于阈值
            terminology_applied=[],
            processing_time=1.0,
            metadata={}
        )
        
        should_retry = retry_manager.should_retry(low_quality_result)
        print("✅ 重试判断功能正常")
        print(f"   质量分数: {low_quality_result.quality_score}")
        print(f"   是否需要重试: {should_retry}")
        
        # 测试自适应策略
        strategy = retry_manager.get_adaptive_strategy("terminology_inconsistency", 1)
        print("✅ 自适应重试策略功能正常")
        print(f"   策略: {strategy}")
        
        return True
    except Exception as e:
        print(f"❌ AutomaticRetryManager 测试失败: {e}")
        return False

def test_translation_control_panel():
    """测试翻译控制面板"""
    try:
        from voice_came.ui.translation_panel import TranslationControlPanel
        
        # 创建控制面板
        panel = TranslationControlPanel()
        print("✅ TranslationControlPanel 创建成功")
        
        # 测试文件加载
        mock_files = [
            {"path": "/test/audio1.mp3", "name": "meditation.mp3", "size": 1024, "duration": 300},
            {"path": "/test/audio2.mp3", "name": "relaxation.mp3", "size": 2048, "duration": 600}
        ]
        panel.load_audio_files(mock_files)
        print("✅ 音频文件加载功能正常")
        
        # 测试设置配置
        settings = {
            "source_language": "zh",
            "target_language": "en",
            "quality_level": "high",
            "terminology_enabled": True,
            "batch_size": 5
        }
        panel.configure_translation_settings(settings)
        print("✅ 翻译设置配置功能正常")
        
        # 测试文件选择
        panel.select_files(["/test/audio1.mp3"])
        selected = panel.get_selected_files()
        print(f"✅ 文件选择功能正常，已选择 {len(selected)} 个文件")
        
        # 测试任务创建
        job = panel.create_translation_job()
        print("✅ 翻译任务创建功能正常")
        print(f"   任务ID: {job.job_id}")
        print(f"   文件数量: {len(job.audio_files)}")
        
        return True
    except Exception as e:
        print(f"❌ TranslationControlPanel 测试失败: {e}")
        return False

async def test_progress_monitor():
    """测试实时进度监控"""
    try:
        from voice_came.ui.progress_monitor import RealTimeProgressMonitor
        
        # 创建进度监控器
        monitor = RealTimeProgressMonitor()
        print("✅ RealTimeProgressMonitor 创建成功")
        
        # 测试进度更新
        monitor.update_overall_progress(0.45)
        monitor.update_current_file("meditation.mp3")
        monitor.update_processing_stage("语音识别中...")
        
        progress = monitor.get_current_progress()
        print("✅ 进度更新功能正常")
        print(f"   整体进度: {progress['overall_progress']:.1%}")
        print(f"   当前文件: {progress['current_file']}")
        print(f"   处理阶段: {progress['processing_stage']}")
        
        # 测试错误显示
        monitor.show_error("测试错误信息")
        monitor.show_warning("测试警告信息")
        
        error_history = monitor.get_error_history()
        print(f"✅ 错误警告显示功能正常，历史记录 {len(error_history)} 条")
        
        # 测试实时监控
        await monitor.start_real_time_updates("test_job")
        await asyncio.sleep(0.1)  # 短暂等待
        await monitor.stop_real_time_updates()
        print("✅ 实时监控功能正常")
        
        return True
    except Exception as e:
        print(f"❌ RealTimeProgressMonitor 测试失败: {e}")
        return False

def test_batch_interface():
    """测试批量处理界面"""
    try:
        from voice_came.ui.batch_interface import BatchProcessingInterface
        
        # 创建批量处理界面
        interface = BatchProcessingInterface()
        print("✅ BatchProcessingInterface 创建成功")
        
        # 测试批量配置
        config = {
            "job_name": "ASMR视频批量翻译",
            "source_language": "zh",
            "target_languages": ["en", "es"],
            "quality_level": "high",
            "parallel_workers": 3,
            "output_format": "srt"
        }
        interface.configure_batch_job(config)
        print("✅ 批量任务配置功能正常")
        
        # 测试配置验证
        is_valid = interface.validate_batch_config()
        print(f"✅ 配置验证功能正常，配置有效: {is_valid}")
        
        # 测试队列管理
        job_id = interface.add_to_batch_queue("测试任务", ["file1.mp3", "file2.mp3"])
        print(f"✅ 队列管理功能正常，任务ID: {job_id}")
        
        queue_status = interface.get_queue_status()
        print(f"   队列状态: {queue_status}")
        
        # 测试并行设置
        interface.set_parallel_workers(4)
        print("✅ 并行工作线程设置功能正常")
        
        return True
    except Exception as e:
        print(f"❌ BatchProcessingInterface 测试失败: {e}")
        return False

def test_results_viewer():
    """测试翻译结果查看器"""
    try:
        from voice_came.ui.results_viewer import TranslationResultsViewer
        from voice_came.translation.models import TranslationResult
        
        # 创建结果查看器
        viewer = TranslationResultsViewer()
        print("✅ TranslationResultsViewer 创建成功")
        
        # 测试结果加载
        test_results = [
            TranslationResult(
                job_id="test_job_1",
                original_text="冥想练习",
                translated_text="meditation practice",
                quality_score=0.92,
                terminology_applied=["冥想→meditation"],
                processing_time=1.5,
                metadata={}
            )
        ]
        viewer.load_translation_results("batch_job_1", test_results)
        print("✅ 翻译结果加载功能正常")
        
        # 测试对比显示
        comparison_data = {
            "original": "让我们开始冥想练习",
            "translation": "Let's begin the meditation practice",
            "quality_score": 0.92,
            "terminology_applied": ["冥想→meditation"]
        }
        viewer.display_translation_comparison(comparison_data)
        print("✅ 翻译对比显示功能正常")
        
        # 测试质量指标可视化
        quality_metrics = {
            "overall_score": 0.88,
            "fluency": 0.92,
            "accuracy": 0.85,
            "terminology_consistency": 0.95
        }
        viz_data = viewer.visualize_quality_metrics(quality_metrics)
        print("✅ 质量指标可视化功能正常")
        print(f"   图表数据点: {len(viz_data['chart_data'])}")
        print(f"   改进建议: {len(viz_data['recommendations'])}")
        
        # 测试编辑功能
        edited_text = viewer.edit_translation("Let's start thinking practice")
        print("✅ 翻译编辑功能正常")
        print(f"   编辑前: Let's start thinking practice")
        print(f"   编辑后: {edited_text}")
        
        return True
    except Exception as e:
        print(f"❌ TranslationResultsViewer 测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始Task 4 Phase 3-4实现验证...")
    print("=" * 60)
    
    tests = [
        ("助眠内容提示词生成器", test_sleep_content_prompt_generator),
        ("翻译质量优化器", test_translation_quality_optimizer),
        ("自动重试管理器", test_automatic_retry_manager),
        ("翻译控制面板", test_translation_control_panel),
        ("实时进度监控", test_progress_monitor),
        ("批量处理界面", test_batch_interface),
        ("翻译结果查看器", test_results_viewer)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试 {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
        
        print("-" * 40)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Task 4 Phase 3-4 TDD Green实现成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
