#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhisperX基础转录功能测试

TDD-Red阶段：这些测试应该初始失败，直到实现对应功能
"""

import pytest
import tempfile
import numpy as np
from unittest.mock import patch, MagicMock
from voice_came.speech_recognition.whisperx_engine import WhisperXEng<PERSON>, WhisperXConfig


class TestWhisperXTranscription:
    """WhisperX基础转录测试类"""
    
    @pytest.fixture
    def audio_config(self):
        """音频配置"""
        return WhisperXConfig(
            model_name="tiny",
            device="cpu", 
            compute_type="float32",
            language="zh"
        )
    
    @pytest.fixture
    def temp_audio_file(self):
        """临时音频文件"""
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp:
            yield tmp.name
    
    @pytest.mark.tdd_red
    def test_transcribe_audio_file(self, audio_config, temp_audio_file):
        """测试音频文件转录"""
        engine = WhisperXEngine(audio_config)
        
        with patch('whisperx.load_audio') as mock_load_audio, \
             patch('whisperx.load_model') as mock_load_model:
            
            mock_audio = np.random.randn(16000).astype(np.float32)
            mock_load_audio.return_value = mock_audio
            
            mock_model = MagicMock()
            mock_model.transcribe.return_value = {
                "segments": [
                    {
                        "text": "这是测试音频",
                        "start": 0.0,
                        "end": 1.0,
                        "words": [
                            {"word": "这是", "start": 0.0, "end": 0.5},
                            {"word": "测试", "start": 0.5, "end": 0.8},
                            {"word": "音频", "start": 0.8, "end": 1.0}
                        ]
                    }
                ]
            }
            mock_load_model.return_value = mock_model
            engine.model = mock_model
            
            result = engine.transcribe(temp_audio_file)
            
            assert result is not None
            assert "segments" in result
            assert len(result["segments"]) > 0
            assert result["segments"][0]["text"] == "这是测试音频"
    
    @pytest.mark.tdd_red
    def test_transcribe_with_language_detection(self, audio_config, temp_audio_file):
        """测试自动语言检测转录"""
        audio_config.language = None
        engine = WhisperXEngine(audio_config)
        
        with patch('whisperx.load_audio') as mock_load_audio, \
             patch('whisperx.load_model') as mock_load_model:
            
            mock_audio = np.random.randn(16000).astype(np.float32)
            mock_load_audio.return_value = mock_audio
            
            mock_model = MagicMock()
            mock_model.transcribe.return_value = {
                "language": "zh",
                "segments": [{"text": "自动检测语言", "start": 0.0, "end": 1.0}]
            }
            mock_load_model.return_value = mock_model
            engine.model = mock_model
            
            result = engine.transcribe(temp_audio_file)
            
            assert result["language"] == "zh"
            assert "segments" in result
    
    @pytest.mark.tdd_red
    def test_transcribe_error_handling(self, audio_config):
        """测试转录错误处理"""
        engine = WhisperXEngine(audio_config)
        
        # 测试文件不存在
        with pytest.raises(FileNotFoundError):
            engine.transcribe("/nonexistent/file.wav")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"]) 