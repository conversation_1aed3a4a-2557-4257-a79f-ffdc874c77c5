"""
pytest配置文件和全局fixtures定义

这个文件包含了项目的pytest配置和全局fixtures，
为所有测试提供共享的测试环境和数据。
"""

import os
import sys
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock
from typing import Generator, Any

# 添加src目录到Python路径，解决模块导入问题
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# 设置测试环境变量
os.environ["TESTING"] = "1"
os.environ["LOG_LEVEL"] = "DEBUG"


@pytest.fixture(scope="session")
def test_data_dir() -> Path:
    """返回测试数据目录路径"""
    return Path(__file__).parent / "data"


@pytest.fixture(scope="session") 
def temp_dir() -> Generator[Path, None, None]:
    """创建临时目录用于测试"""
    with tempfile.TemporaryDirectory() as temp_dir_path:
        yield Path(temp_dir_path)


@pytest.fixture
def sample_audio_file(test_data_dir: Path) -> Path:
    """返回示例音频文件路径"""
    audio_file = test_data_dir / "sample.wav"
    if not audio_file.exists():
        # 如果测试数据不存在，创建一个空文件作为占位符
        audio_file.parent.mkdir(parents=True, exist_ok=True)
        audio_file.touch()
    return audio_file


@pytest.fixture
def mock_whisperx_model():
    """模拟WhisperX模型"""
    mock_model = Mock()
    mock_model.transcribe.return_value = {
        "segments": [
            {
                "start": 0.0,
                "end": 5.0,
                "text": "Hello world",
                "words": [
                    {"word": "Hello", "start": 0.0, "end": 2.0, "score": 0.95},
                    {"word": "world", "start": 2.0, "end": 5.0, "score": 0.92}
                ]
            }
        ]
    }
    return mock_model


@pytest.fixture
def mock_translation_model():
    """模拟翻译模型"""
    mock_model = Mock()
    mock_model.translate.return_value = "你好世界"
    return mock_model


@pytest.fixture
def sample_config() -> dict[str, Any]:
    """返回示例配置"""
    return {
        "whisperx": {
            "model_size": "base",
            "language": "auto",
            "device": "cpu"
        },
        "translation": {
            "model_name": "gemma3-12b-q4",
            "target_language": "zh"
        },
        "audio": {
            "sample_rate": 16000,
            "chunk_length": 30
        }
    }


@pytest.fixture(autouse=True)
def clean_environment():
    """自动清理测试环境"""
    # 测试前准备
    yield
    # 测试后清理
    # 这里可以添加清理逻辑，如删除临时文件等


@pytest.fixture
def mock_logger():
    """模拟日志记录器"""
    return Mock()


# 测试标记定义
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration  
pytest.mark.slow = pytest.mark.slow
pytest.mark.api = pytest.mark.api
pytest.mark.offline = pytest.mark.offline 