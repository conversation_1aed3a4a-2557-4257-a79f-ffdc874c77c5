#!/usr/bin/env python3
"""
Voice-came 测试数据生成器
用于生成各种测试场景的模拟数据，支持TDD开发模式
"""

import os
import json
import random
import wave
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
from faker import Faker
import cv2

class VoiceCameTestDataGenerator:
    """Voice-came项目测试数据生成器"""
    
    def __init__(self, output_dir: str = "test_data"):
        """
        初始化测试数据生成器
        
        Args:
            output_dir: 测试数据输出目录
        """
        self.output_dir = Path(output_dir)
        self.faker = Faker(['zh_CN', 'en_US'])
        self._setup_directories()
    
    def _setup_directories(self):
        """创建测试数据目录结构"""
        directories = [
            "video_samples",
            "audio_samples", 
            "translation_samples",
            "terminology",
            "batch_processing/video_batch_10files",
            "invalid_files",
            "performance_test"
        ]
        
        for dir_name in directories:
            (self.output_dir / dir_name).mkdir(parents=True, exist_ok=True)
    
    def generate_video_samples(self):
        """生成视频测试样本"""
        video_configs = [
            {
                "name": "clear_speech_5min.mp4",
                "duration": 300,  # 5分钟
                "width": 1920,
                "height": 1080,
                "fps": 30,
                "description": "清晰语音5分钟测试视频"
            },
            {
                "name": "speech_with_music.mp4", 
                "duration": 600,  # 10分钟
                "width": 1280,
                "height": 720,
                "fps": 24,
                "description": "带背景音乐的语音视频"
            },
            {
                "name": "long_video_3h.mp4",
                "duration": 10800,  # 3小时
                "width": 1920,
                "height": 1080,
                "fps": 30,
                "description": "3小时长视频测试文件"
            },
            {
                "name": "medium_video.mp4",
                "duration": 3600,  # 1小时
                "width": 1280,
                "height": 720,
                "fps": 25,
                "description": "1小时中等长度视频"
            },
            {
                "name": "short_video.mp4",
                "duration": 60,  # 1分钟
                "width": 640,
                "height": 480,
                "fps": 30,
                "description": "1分钟短视频"
            }
        ]
        
        for config in video_configs:
            self._generate_test_video(config)
    
    def _generate_test_video(self, config: Dict):
        """
        生成单个测试视频文件
        
        Args:
            config: 视频配置参数
        """
        output_path = self.output_dir / "video_samples" / config["name"]
        
        # 创建视频写入对象
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        writer = cv2.VideoWriter(
            str(output_path),
            fourcc,
            config["fps"],
            (config["width"], config["height"])
        )
        
        # 生成视频帧
        total_frames = config["duration"] * config["fps"]
        
        for frame_num in range(total_frames):
            # 生成简单的测试帧（渐变背景）
            frame = np.zeros((config["height"], config["width"], 3), dtype=np.uint8)
            
            # 添加渐变背景
            for y in range(config["height"]):
                for x in range(config["width"]):
                    frame[y, x] = [
                        int((x / config["width"]) * 255),
                        int((y / config["height"]) * 255),
                        int(((frame_num % 255) / 255) * 255)
                    ]
            
            # 添加时间戳文字
            timestamp = frame_num / config["fps"]
            cv2.putText(
                frame,
                f"Time: {timestamp:.1f}s",
                (50, 50),
                cv2.FONT_HERSHEY_SIMPLEX,
                1,
                (255, 255, 255),
                2
            )
            
            writer.write(frame)
        
        writer.release()
        print(f"✅ 生成视频文件: {output_path}")
    
    def generate_audio_samples(self):
        """生成音频测试样本"""
        audio_configs = [
            {
                "name": "clean_speech.wav",
                "duration": 60,
                "sample_rate": 44100,
                "description": "清晰语音音频"
            },
            {
                "name": "noisy_speech.wav",
                "duration": 60,
                "sample_rate": 44100,
                "description": "带噪音的语音音频"
            },
            {
                "name": "mixed_content.wav",
                "duration": 120,
                "sample_rate": 44100,
                "description": "语音和音乐混合音频"
            },
            {
                "name": "silence.wav",
                "duration": 30,
                "sample_rate": 44100,
                "description": "静音音频文件"
            }
        ]
        
        for config in audio_configs:
            self._generate_test_audio(config)
    
    def _generate_test_audio(self, config: Dict):
        """
        生成单个测试音频文件
        
        Args:
            config: 音频配置参数
        """
        output_path = self.output_dir / "audio_samples" / config["name"]
        
        sample_rate = config["sample_rate"]
        duration = config["duration"]
        samples = int(sample_rate * duration)
        
        # 生成不同类型的音频信号
        if "clean_speech" in config["name"]:
            # 生成模拟语音信号（多频率正弦波）
            t = np.linspace(0, duration, samples)
            audio = np.zeros(samples)
            for freq in [200, 400, 800, 1600]:  # 语音频率范围
                audio += np.sin(2 * np.pi * freq * t) * 0.1
            
        elif "noisy_speech" in config["name"]:
            # 生成带噪音的语音信号
            t = np.linspace(0, duration, samples)
            speech = np.zeros(samples)
            for freq in [200, 400, 800, 1600]:
                speech += np.sin(2 * np.pi * freq * t) * 0.1
            noise = np.random.normal(0, 0.05, samples)
            audio = speech + noise
            
        elif "mixed_content" in config["name"]:
            # 生成语音+音乐混合信号
            t = np.linspace(0, duration, samples)
            speech = np.sin(2 * np.pi * 400 * t) * 0.3
            music = np.sin(2 * np.pi * 440 * t) * 0.2  # A4音符
            audio = speech + music
            
        else:  # silence
            # 生成静音
            audio = np.zeros(samples)
        
        # 归一化音频
        audio = np.clip(audio, -1.0, 1.0)
        audio_int16 = (audio * 32767).astype(np.int16)
        
        # 保存音频文件
        with wave.open(str(output_path), 'w') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        print(f"✅ 生成音频文件: {output_path}")
    
    def generate_translation_samples(self):
        """生成翻译测试样本"""
        # 中文助眠内容
        sleep_content_cn = """
深呼吸，让你的身体完全放松下来。闭上眼睛，感受内心的宁静。
想象你正躺在一片柔软的云朵上，四周是温和的微风。
让所有的压力和焦虑都随着呼吸缓缓离开你的身体。
现在，专注于你的呼吸，慢慢地吸气，慢慢地呼气。
感受你的肌肉一点一点地放松，从头顶到脚趾。
你的思绪变得越来越平静，就像湖面一样宁静。
让冥想的力量引导你进入深度睡眠的状态。
释放今天所有的疲惫，拥抱即将到来的安宁。
"""
        
        # 专业术语集
        technical_terms = """
冥想,meditation
深度睡眠,deep sleep
放松,relaxation
呼吸练习,breathing exercise
正念,mindfulness
睡眠引导,sleep guidance
渐进式肌肉放松,progressive muscle relaxation
可视化,visualization
内心平静,inner peace
睡眠质量,sleep quality
"""
        
        # 边界情况测试文本
        edge_cases = """
测试空字符串：""
测试特殊字符：@#$%^&*()
测试数字：12345 67890
测试混合内容：Hello世界123！
测试长句子：这是一个非常非常非常非常长的句子，用来测试翻译系统处理长文本的能力和稳定性。
测试重复术语：冥想是很好的，冥想能帮助睡眠，冥想冥想冥想。
"""
        
        # 保存翻译测试文件
        files = {
            "sleep_content_cn.txt": sleep_content_cn,
            "technical_terms.txt": technical_terms,
            "edge_cases.txt": edge_cases
        }
        
        for filename, content in files.items():
            file_path = self.output_dir / "translation_samples" / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content.strip())
            print(f"✅ 生成翻译测试文件: {file_path}")
    
    def generate_terminology_data(self):
        """生成术语库测试数据"""
        # 100词核心术语表
        sleep_terms_100 = {
            "冥想": "meditation",
            "深度睡眠": "deep sleep",
            "放松": "relaxation",
            "呼吸": "breathing",
            "正念": "mindfulness",
            "平静": "calm",
            "安宁": "tranquility",
            "休息": "rest",
            "梦境": "dream",
            "宁静": "serenity",
            "舒缓": "soothing",
            "缓解": "relief",
            "压力": "stress",
            "焦虑": "anxiety",
            "紧张": "tension",
            "疲劳": "fatigue",
            "困倦": "drowsiness",
            "睡意": "sleepiness",
            "入睡": "fall asleep",
            "失眠": "insomnia"
        }
        
        # 扩展到100个术语
        additional_terms = {}
        for i in range(80):
            cn_term = f"术语{i+21}"
            en_term = f"term_{i+21}"
            additional_terms[cn_term] = en_term
        
        sleep_terms_100.update(additional_terms)
        
        # 自定义术语
        custom_terms = {
            "深度睡眠": "deep sleep",
            "快速眼动": "REM sleep",
            "睡眠周期": "sleep cycle"
        }
        
        # 冲突术语
        conflict_terms = {
            "放松": "relax",  # 与标准术语"relaxation"冲突
            "冥想": "meditate"  # 与标准术语"meditation"冲突
        }
        
        # 保存术语库文件
        terminology_files = {
            "sleep_terms_100.json": sleep_terms_100,
            "custom_terms.json": custom_terms,
            "conflict_terms.json": conflict_terms
        }
        
        for filename, terms in terminology_files.items():
            file_path = self.output_dir / "terminology" / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(terms, f, ensure_ascii=False, indent=2)
            print(f"✅ 生成术语库文件: {file_path}")
    
    def generate_batch_processing_data(self):
        """生成批处理测试数据"""
        batch_dir = self.output_dir / "batch_processing" / "video_batch_10files"
        
        # 生成10个不同规格的测试视频
        for i in range(10):
            duration = random.randint(60, 600)  # 1-10分钟随机时长
            width = random.choice([640, 1280, 1920])
            height = 480 if width == 640 else (720 if width == 1280 else 1080)
            
            config = {
                "name": f"batch_video_{i+1:02d}.mp4",
                "duration": duration,
                "width": width,
                "height": height,
                "fps": random.choice([24, 25, 30]),
                "description": f"批处理测试视频 #{i+1}"
            }
            
            self._generate_batch_video(config, batch_dir)
    
    def _generate_batch_video(self, config: Dict, output_dir: Path):
        """生成批处理测试视频"""
        output_path = output_dir / config["name"]
        
        # 创建简化的测试视频（仅生成元数据文件）
        metadata = {
            "filename": config["name"],
            "duration": config["duration"],
            "width": config["width"],
            "height": config["height"],
            "fps": config["fps"],
            "size_mb": config["duration"] * 0.5,  # 估算文件大小
            "format": "mp4",
            "created_at": self.faker.date_time().isoformat()
        }
        
        # 保存元数据而不是实际视频文件（节省空间）
        metadata_path = output_path.with_suffix('.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"✅ 生成批处理视频元数据: {metadata_path}")
    
    def generate_invalid_files(self):
        """生成无效文件测试数据"""
        invalid_files = [
            {
                "name": "text_file.txt",
                "content": "这是一个文本文件，不是视频文件",
                "type": "text"
            },
            {
                "name": "corrupted_video.mp4",
                "content": b"\x00\x01\x02\x03" * 1000,  # 损坏的二进制数据
                "type": "binary"
            },
            {
                "name": "empty_file.mp4",
                "content": "",
                "type": "empty"
            },
            {
                "name": "wrong_extension.avi",
                "content": "这不是真正的AVI文件",
                "type": "text"
            }
        ]
        
        for file_config in invalid_files:
            file_path = self.output_dir / "invalid_files" / file_config["name"]
            
            if file_config["type"] == "binary":
                with open(file_path, 'wb') as f:
                    f.write(file_config["content"])
            else:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(file_config["content"])
            
            print(f"✅ 生成无效文件: {file_path}")
    
    def generate_performance_test_data(self):
        """生成性能测试数据"""
        performance_scenarios = [
            {
                "name": "high_concurrency_test",
                "description": "高并发测试场景",
                "concurrent_files": 5,
                "file_sizes": ["1GB", "2GB", "3GB", "500MB", "1.5GB"]
            },
            {
                "name": "memory_stress_test", 
                "description": "内存压力测试场景",
                "large_file_size": "6GB",
                "memory_limit": "8GB"
            },
            {
                "name": "processing_speed_benchmark",
                "description": "处理速度基准测试",
                "video_duration": "3hours",
                "target_time": "30minutes"
            }
        ]
        
        for scenario in performance_scenarios:
            file_path = self.output_dir / "performance_test" / f"{scenario['name']}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(scenario, f, indent=2)
            print(f"✅ 生成性能测试配置: {file_path}")
    
    def generate_all_test_data(self):
        """生成所有测试数据"""
        print("🚀 开始生成Voice-came测试数据...")
        
        try:
            print("\n📹 生成视频测试样本...")
            self.generate_video_samples()
            
            print("\n🎵 生成音频测试样本...")
            self.generate_audio_samples()
            
            print("\n📝 生成翻译测试样本...")
            self.generate_translation_samples()
            
            print("\n📚 生成术语库测试数据...")
            self.generate_terminology_data()
            
            print("\n📦 生成批处理测试数据...")
            self.generate_batch_processing_data()
            
            print("\n❌ 生成无效文件测试数据...")
            self.generate_invalid_files()
            
            print("\n⚡ 生成性能测试数据...")
            self.generate_performance_test_data()
            
            print("\n✅ 所有测试数据生成完成！")
            self._print_summary()
            
        except Exception as e:
            print(f"\n❌ 测试数据生成失败: {e}")
            raise
    
    def _print_summary(self):
        """打印测试数据生成总结"""
        print("\n📊 测试数据生成总结：")
        print(f"📁 输出目录: {self.output_dir.absolute()}")
        
        # 统计各类型文件数量
        for subdir in self.output_dir.iterdir():
            if subdir.is_dir():
                file_count = len(list(subdir.rglob("*"))) 
                print(f"   {subdir.name}: {file_count} 个文件")


def main():
    """主函数 - 生成所有测试数据"""
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    test_data_dir = project_root / "test_data"
    
    # 创建测试数据生成器并生成所有数据
    generator = VoiceCameTestDataGenerator(str(test_data_dir))
    generator.generate_all_test_data()


if __name__ == "__main__":
    main() 