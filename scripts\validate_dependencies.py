#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务依赖关系验证工具
检查任务系统中的依赖关系问题，包括循环依赖、状态不一致等
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    DONE = "done"

class Priority(Enum):
    """优先级枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class ValidationIssue:
    """验证问题数据类"""
    severity: str  # ERROR, WARNING, INFO
    category: str  # CIRCULAR_DEPENDENCY, STATUS_INCONSISTENCY, etc.
    task_id: int
    message: str
    suggestion: str = ""

class DependencyValidator:
    """依赖关系验证器"""
    
    def __init__(self, tasks_file: str = ".taskmaster/tasks/tasks.json"):
        self.tasks_file = Path(tasks_file)
        self.tasks = {}
        self.issues = []
        
    def load_tasks(self) -> bool:
        """加载任务数据"""
        try:
            if not self.tasks_file.exists():
                print(f"❌ 任务文件不存在: {self.tasks_file}")
                return False
                
            with open(self.tasks_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            self.tasks = {task['id']: task for task in data['master']['tasks']}
            print(f"✅ 已加载 {len(self.tasks)} 个任务")
            return True
            
        except Exception as e:
            print(f"❌ 加载任务文件失败: {e}")
            return False
    
    def check_circular_dependencies(self) -> None:
        """检查循环依赖"""
        print("\n🔄 检查循环依赖...")
        
        # 构建依赖图
        graph = defaultdict(list)
        for task_id, task in self.tasks.items():
            deps = task.get('dependencies', [])
            for dep_id in deps:
                graph[dep_id].append(task_id)
        
        # 使用DFS检测循环
        visited = set()
        rec_stack = set()
        
        def has_cycle(node):
            visited.add(node)
            rec_stack.add(node)
            
            for neighbor in graph[node]:
                if neighbor not in visited:
                    if has_cycle(neighbor):
                        return True
                elif neighbor in rec_stack:
                    return True
            
            rec_stack.remove(node)
            return False
        
        cycles_found = False
        for task_id in self.tasks:
            if task_id not in visited:
                if has_cycle(task_id):
                    cycles_found = True
                    self.issues.append(ValidationIssue(
                        severity="ERROR",
                        category="CIRCULAR_DEPENDENCY",
                        task_id=task_id,
                        message=f"检测到循环依赖，涉及任务 {task_id}",
                        suggestion="重新设计任务依赖关系，消除循环"
                    ))
        
        if not cycles_found:
            print("  ✅ 未发现循环依赖")
    
    def check_dependency_existence(self) -> None:
        """检查依赖任务是否存在"""
        print("\n📋 检查依赖任务存在性...")
        
        missing_deps = []
        for task_id, task in self.tasks.items():
            deps = task.get('dependencies', [])
            for dep_id in deps:
                if dep_id not in self.tasks:
                    missing_deps.append((task_id, dep_id))
                    self.issues.append(ValidationIssue(
                        severity="ERROR",
                        category="MISSING_DEPENDENCY",
                        task_id=task_id,
                        message=f"依赖的任务 {dep_id} 不存在",
                        suggestion=f"创建任务 {dep_id} 或移除此依赖"
                    ))
        
        if missing_deps:
            print(f"  ❌ 发现 {len(missing_deps)} 个缺失的依赖")
        else:
            print("  ✅ 所有依赖任务都存在")
    
    def check_status_consistency(self) -> None:
        """检查状态一致性"""
        print("\n📊 检查任务状态一致性...")
        
        inconsistencies = []
        
        for task_id, task in self.tasks.items():
            task_status = task.get('status', 'pending')
            deps = task.get('dependencies', [])
            
            # 检查：如果任务已完成，其依赖也应该完成
            if task_status in ['completed', 'done']:
                for dep_id in deps:
                    if dep_id in self.tasks:
                        dep_status = self.tasks[dep_id].get('status', 'pending')
                        if dep_status not in ['completed', 'done']:
                            inconsistencies.append((task_id, dep_id))
                            self.issues.append(ValidationIssue(
                                severity="ERROR",
                                category="STATUS_INCONSISTENCY",
                                task_id=task_id,
                                message=f"任务 {task_id} 已完成但依赖任务 {dep_id} 未完成",
                                suggestion=f"更新任务 {dep_id} 状态为已完成或重新评估任务 {task_id} 状态"
                            ))
            
            # 检查：如果任务进行中，其依赖应该完成
            elif task_status == 'in_progress':
                for dep_id in deps:
                    if dep_id in self.tasks:
                        dep_status = self.tasks[dep_id].get('status', 'pending')
                        if dep_status not in ['completed', 'done']:
                            self.issues.append(ValidationIssue(
                                severity="WARNING",
                                category="STATUS_INCONSISTENCY",
                                task_id=task_id,
                                message=f"任务 {task_id} 进行中但依赖任务 {dep_id} 未完成",
                                suggestion=f"完成依赖任务 {dep_id} 或暂停任务 {task_id}"
                            ))
        
        if inconsistencies:
            print(f"  ⚠️  发现 {len(inconsistencies)} 个状态不一致问题")
        else:
            print("  ✅ 任务状态一致")
    
    def check_priority_conflicts(self) -> None:
        """检查优先级冲突"""
        print("\n🎯 检查优先级冲突...")
        
        priority_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        conflicts = []
        
        for task_id, task in self.tasks.items():
            task_priority = task.get('priority', 'medium')
            task_priority_value = priority_order.get(task_priority, 2)
            deps = task.get('dependencies', [])
            
            for dep_id in deps:
                if dep_id in self.tasks:
                    dep_priority = self.tasks[dep_id].get('priority', 'medium')
                    dep_priority_value = priority_order.get(dep_priority, 2)
                    
                    # 如果依赖任务的优先级低于当前任务，可能有问题
                    if dep_priority_value < task_priority_value:
                        conflicts.append((task_id, dep_id))
                        self.issues.append(ValidationIssue(
                            severity="WARNING",
                            category="PRIORITY_CONFLICT",
                            task_id=task_id,
                            message=f"任务 {task_id} 优先级({task_priority})高于其依赖任务 {dep_id} 优先级({dep_priority})",
                            suggestion=f"考虑提高任务 {dep_id} 的优先级至 {task_priority} 或以上"
                        ))
        
        if conflicts:
            print(f"  ⚠️  发现 {len(conflicts)} 个优先级冲突")
        else:
            print("  ✅ 优先级设置合理")
    
    def check_critical_path(self) -> None:
        """分析关键路径"""
        print("\n🚀 分析关键路径和执行顺序...")
        
        # 拓扑排序确定执行顺序
        in_degree = defaultdict(int)
        graph = defaultdict(list)
        
        # 构建图和入度
        for task_id, task in self.tasks.items():
            deps = task.get('dependencies', [])
            in_degree[task_id] = len(deps)
            for dep_id in deps:
                graph[dep_id].append(task_id)
        
        # 拓扑排序
        queue = deque([task_id for task_id in self.tasks if in_degree[task_id] == 0])
        execution_order = []
        
        while queue:
            current = queue.popleft()
            execution_order.append(current)
            
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        # 检查是否有任务无法执行（循环依赖导致）
        if len(execution_order) != len(self.tasks):
            remaining_tasks = set(self.tasks.keys()) - set(execution_order)
            for task_id in remaining_tasks:
                self.issues.append(ValidationIssue(
                    severity="ERROR",
                    category="UNEXECUTABLE_TASK",
                    task_id=task_id,
                    message=f"任务 {task_id} 由于依赖关系无法执行（可能存在循环依赖）",
                    suggestion="检查并解决循环依赖问题"
                ))
        
        print(f"  📋 建议执行顺序: {' → '.join(map(str, execution_order[:10]))}{'...' if len(execution_order) > 10 else ''}")
        
        # 标识关键任务
        critical_tasks = []
        for task_id, task in self.tasks.items():
            if task.get('priority') == 'critical':
                critical_tasks.append(task_id)
        
        if critical_tasks:
            print(f"  🎯 关键任务: {', '.join(map(str, critical_tasks))}")
            
            # 检查关键任务是否被适当优先处理
            for critical_task in critical_tasks:
                if critical_task not in execution_order[:3]:  # 前3个位置
                    self.issues.append(ValidationIssue(
                        severity="WARNING",
                        category="CRITICAL_TASK_DELAY",
                        task_id=critical_task,
                        message=f"关键任务 {critical_task} 在执行顺序中位置较后",
                        suggestion="考虑减少此任务的依赖或提高依赖任务的优先级"
                    ))
    
    def analyze_task_13_special_case(self) -> None:
        """特别分析Task 13的情况"""
        print("\n🔧 特别检查 TDD流程监控任务 (Task 13)...")
        
        task_13 = self.tasks.get(13)
        if not task_13:
            print("  ⚠️  Task 13 不存在")
            return
        
        # 检查有多少任务依赖Task 13
        dependent_tasks = []
        for task_id, task in self.tasks.items():
            if 13 in task.get('dependencies', []):
                dependent_tasks.append(task_id)
        
        print(f"  📊 {len(dependent_tasks)} 个任务依赖 Task 13: {dependent_tasks}")
        
        # 检查Task 13的状态
        task_13_status = task_13.get('status', 'pending')
        print(f"  📋 Task 13 当前状态: {task_13_status}")
        
        if task_13_status == 'pending':
            # 检查依赖Task 13的任务中有多少已经开始或完成
            started_dependents = []
            for task_id in dependent_tasks:
                if task_id in self.tasks:
                    status = self.tasks[task_id].get('status', 'pending')
                    if status in ['in_progress', 'completed', 'done']:
                        started_dependents.append((task_id, status))
            
            if started_dependents:
                print(f"  ⚠️  发现问题：Task 13 未开始，但以下依赖任务已开始:")
                for task_id, status in started_dependents:
                    print(f"    - Task {task_id}: {status}")
                
                self.issues.append(ValidationIssue(
                    severity="ERROR",
                    category="INFRASTRUCTURE_DEPENDENCY",
                    task_id=13,
                    message="TDD基础设施任务未完成，但依赖任务已开始",
                    suggestion="立即启动并完成 Task 13，或调整依赖关系"
                ))
        
        # 检查Task 13的优先级
        task_13_priority = task_13.get('priority', 'medium')
        if task_13_priority != 'critical':
            self.issues.append(ValidationIssue(
                severity="WARNING",
                category="PRIORITY_MISMATCH",
                task_id=13,
                message=f"Task 13 应该是 critical 优先级，当前是 {task_13_priority}",
                suggestion="将 Task 13 优先级调整为 critical"
            ))
    
    def generate_report(self) -> None:
        """生成验证报告"""
        print("\n" + "="*60)
        print("📋 依赖关系验证报告")
        print("="*60)
        
        if not self.issues:
            print("🎉 恭喜！所有依赖关系检查通过，未发现问题。")
            return
        
        # 按严重程度分组
        errors = [issue for issue in self.issues if issue.severity == "ERROR"]
        warnings = [issue for issue in self.issues if issue.severity == "WARNING"]
        infos = [issue for issue in self.issues if issue.severity == "INFO"]
        
        print(f"📊 问题统计: {len(errors)} 个错误, {len(warnings)} 个警告, {len(infos)} 个信息")
        
        if errors:
            print(f"\n❌ 错误 ({len(errors)} 个):")
            for i, issue in enumerate(errors, 1):
                print(f"  {i}. [Task {issue.task_id}] {issue.message}")
                if issue.suggestion:
                    print(f"     💡 建议: {issue.suggestion}")
        
        if warnings:
            print(f"\n⚠️  警告 ({len(warnings)} 个):")
            for i, issue in enumerate(warnings, 1):
                print(f"  {i}. [Task {issue.task_id}] {issue.message}")
                if issue.suggestion:
                    print(f"     💡 建议: {issue.suggestion}")
        
        if infos:
            print(f"\nℹ️  信息 ({len(infos)} 个):")
            for i, issue in enumerate(infos, 1):
                print(f"  {i}. [Task {issue.task_id}] {issue.message}")
        
        # 生成修复建议优先级
        print(f"\n🚀 修复建议优先级:")
        if errors:
            print("  1. 优先修复所有错误，特别是基础设施依赖问题")
        if [issue for issue in warnings if issue.category == "PRIORITY_CONFLICT"]:
            print("  2. 调整优先级冲突")
        if [issue for issue in warnings if issue.category == "STATUS_INCONSISTENCY"]:
            print("  3. 解决状态不一致问题")
        
        print(f"\n📄 详细问题分析已保存到: dependency_validation_report.json")
        
        # 保存详细报告
        self.save_detailed_report()
    
    def save_detailed_report(self) -> None:
        """保存详细报告到JSON文件"""
        report = {
            "validation_time": "2025-06-16T10:00:00Z",
            "total_tasks": len(self.tasks),
            "total_issues": len(self.issues),
            "issues_by_severity": {
                "errors": len([i for i in self.issues if i.severity == "ERROR"]),
                "warnings": len([i for i in self.issues if i.severity == "WARNING"]),
                "infos": len([i for i in self.issues if i.severity == "INFO"])
            },
            "issues": [
                {
                    "severity": issue.severity,
                    "category": issue.category,
                    "task_id": issue.task_id,
                    "message": issue.message,
                    "suggestion": issue.suggestion
                }
                for issue in self.issues
            ]
        }
        
        with open("dependency_validation_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
    
    def validate_all(self) -> bool:
        """执行所有验证检查"""
        if not self.load_tasks():
            return False
        
        print("🔍 开始依赖关系验证...")
        
        self.check_dependency_existence()
        self.check_circular_dependencies()
        self.check_status_consistency()
        self.check_priority_conflicts()
        self.check_critical_path()
        self.analyze_task_13_special_case()
        
        self.generate_report()
        
        # 返回是否有错误
        has_errors = any(issue.severity == "ERROR" for issue in self.issues)
        return not has_errors

def main():
    """主函数"""
    validator = DependencyValidator()
    success = validator.validate_all()
    
    if success:
        print("\n✅ 验证完成，依赖关系健康！")
        sys.exit(0)
    else:
        print("\n❌ 验证失败，发现严重问题需要修复！")
        sys.exit(1)

if __name__ == "__main__":
    main() 