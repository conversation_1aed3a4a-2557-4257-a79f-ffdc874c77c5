# Voice-came 语音翻译系统

Voice-came 是一个基于 WhisperX 和本地大语言模型的智能语音翻译系统，支持实时语音识别、文本翻译和语音合成功能。

## 项目特性

- 🎤 **高精度语音识别**：基于 WhisperX 的高质量语音转文字
- 🌐 **多语言翻译**：集成本地 LLM（Gemma3-12B-Q4/Qwen3）进行文本翻译
- 🔊 **语音合成**：支持翻译结果的语音输出
- 💻 **跨平台支持**：兼容 Windows、macOS 和 Linux
- 🚀 **本地部署**：完全本地化运行，保护隐私安全

## 快速开始

### 系统要求

- Python 3.8+
- 8GB+ RAM（推荐 16GB）
- CUDA 兼容的 GPU（可选，用于加速）

### 安装指南

详细的环境搭建和依赖安装说明请参考：[安装文档](docs/setup_guide.md)

#### 快速开始

```bash
# 1. 克隆仓库
git clone https://github.com/voice-came/voice-came.git
cd voice-came

# 2. 一键安装（推荐）
python setup.py

# 3. 验证安装
python test_installation.py
```

### 基本使用

```python
# 示例代码将在后续版本中提供
```

## 项目结构

```
Voice-came/
├── src/                    # 源代码
├── tests/                  # 测试文件
├── docs/                   # 文档
├── configs/                # 配置文件
├── requirements.txt        # Python依赖
└── README.md              # 项目说明
```

## 开发状态

🚧 项目正在积极开发中...

## 贡献指南

欢迎贡献代码！请查看 [开发规范](Voice-came_开发规范_v1.0.md) 了解详细信息。

## 许可证

[MIT License](LICENSE)

## 联系方式

如有问题或建议，请通过 Issues 联系我们。 