"""
Voice-came 翻译模块

本模块实现Voice-came与VoiceTransl的集成，按照接口契约分为：
- integration: 系统集成接口层 (Task 4)
- business: 翻译业务逻辑层 (Task 5)
"""

from .integration import VoiceTranslIntegrationService, ProcessManager, DataAdapter
from .business import TerminologyService, QualityOptimizer, BatchTranslationController
from .models import (
    AudioData, TranslationConfig, TranslationJob, TranslationResult,
    JobStatus, HealthStatus, ServiceStatus, TerminologyRules, ValidationResult,
    QualityScore, BatchJob, BatchProgress, ErrorReport
)

__all__ = [
    # Task 4: 系统集成接口层
    'VoiceTranslIntegrationService',
    'ProcessManager', 
    'DataAdapter',
    
    # Task 5: 翻译业务逻辑层
    'TerminologyService',
    'QualityOptimizer',
    'BatchTranslationController',
    
    # 数据模型
    'AudioData',
    'TranslationConfig',
    'TranslationJob', 
    'TranslationResult',
    'JobStatus',
    'HealthStatus',
    'ServiceStatus',
    'TerminologyRules',
    'ValidationResult',
    'QualityScore',
    'BatchJob',
    'BatchProgress',
    'ErrorReport',
] 