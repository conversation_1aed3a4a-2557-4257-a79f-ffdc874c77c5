"""
Voice-came 翻译模块

本模块实现Voice-came与VoiceTransl的集成，按照接口契约分为：
- integration: 系统集成接口层 (Task 4)
- business: 翻译业务逻辑层 (Task 5)
- terminology: 术语管理 (Task 4.2)
- quality: 质量评估 (Task 4.2)
- optimization: 质量优化 (Task 4.3)
"""

# Task 4实际实现的类
try:
    from .integration import (
        VoiceTranslAdapter, VoiceTranslProcessManager, VoiceTranslHealthChecker,
        VoiceTranslConfigManager, convert_whisperx_segment, convert_voicetransl_output
    )
    from .terminology import SleepTerminologyManager, TerminologyRuleEngine
    from .quality import SleepContentQualityAssessor
    from .optimization import (
        SleepContentPromptGenerator, TranslationQualityOptimizer, AutomaticRetryManager
    )
    TASK4_AVAILABLE = True
except ImportError:
    TASK4_AVAILABLE = False

# 原有的接口定义（向后兼容）
try:
    from .integration import VoiceTranslIntegrationService, ProcessManager, DataAdapter
    from .business import TerminologyService, QualityOptimizer, BatchTranslationController
    LEGACY_AVAILABLE = True
except ImportError:
    LEGACY_AVAILABLE = False

from .models import (
    AudioSegment, TranslationConfig, TranslationJob, TranslationResult,
    JobStatus, HealthStatus, ServiceStatus, TerminologyRules, ValidationResult,
    QualityScore, BatchJob, BatchProgress, ErrorReport, WhisperXOutput,
    VoiceTranslInput, VoiceTranslOutput, AudioFile, TerminologyRule
)

# 动态构建__all__列表
__all__ = [
    # 数据模型（总是可用）
    'AudioSegment',
    'TranslationConfig',
    'TranslationJob',
    'TranslationResult',
    'JobStatus',
    'HealthStatus',
    'ServiceStatus',
    'TerminologyRules',
    'ValidationResult',
    'QualityScore',
    'BatchJob',
    'BatchProgress',
    'ErrorReport',
    'WhisperXOutput',
    'VoiceTranslInput',
    'VoiceTranslOutput',
    'AudioFile',
    'TerminologyRule'
]

# Task 4实现的类
if TASK4_AVAILABLE:
    __all__.extend([
        'VoiceTranslAdapter',
        'VoiceTranslProcessManager',
        'VoiceTranslHealthChecker',
        'VoiceTranslConfigManager',
        'convert_whisperx_segment',
        'convert_voicetransl_output',
        'SleepTerminologyManager',
        'TerminologyRuleEngine',
        'SleepContentQualityAssessor',
        'SleepContentPromptGenerator',
        'TranslationQualityOptimizer',
        'AutomaticRetryManager'
    ])

# 原有接口（向后兼容）
if LEGACY_AVAILABLE:
    __all__.extend([
        'VoiceTranslIntegrationService',
        'ProcessManager',
        'DataAdapter',
        'TerminologyService',
        'QualityOptimizer',
        'BatchTranslationController'
    ])