#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhisperX引擎实现

基于WhisperX的高精度语音识别引擎
TDD-Refactor阶段：在测试保护下重构优化
优化重点：模型加载性能、错误处理、代码结构、性能监控
"""

import os
import gc
import time
import psutil
import torch
import logging
import threading
from pathlib import Path
from functools import wraps
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable, Union
from contextlib import contextmanager

# 导入WhisperX
try:
    import whisperx
except ImportError:
    whisperx = None

from voice_came.exceptions import WhisperXError
from voice_came.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class WhisperXConfig:
    """WhisperX配置类"""
    model_name: str = "tiny"
    device: str = "cpu"
    compute_type: str = "float32"
    language: Optional[str] = "zh"
    word_timestamps: bool = True
    output_format: str = "json"
    batch_size: int = 16
    model_cache_dir: Optional[str] = None
    vad_threshold: float = 0.5
    vad_min_speech_duration_ms: int = 250
    vad_max_silence_duration_ms: int = 2000
    asmr_mode: bool = False
    speaker_diarization: bool = False
    
    # 新增性能和重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    performance_monitoring: bool = True
    memory_optimization: bool = True
    preload_models: bool = False


@dataclass
class PerformanceMetrics:
    """性能指标类"""
    processing_time: float = 0.0
    memory_used: float = 0.0
    cpu_usage: float = 0.0
    gpu_memory_used: float = 0.0
    throughput: float = 0.0
    audio_duration: float = 0.0
    accuracy_score: float = 0.0
    timestamp: float = field(default_factory=time.time)


class ModelCache:
    """模型缓存管理器"""
    
    def __init__(self):
        self._cache = {}
        self._access_count = {}
        self._last_access = {}
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存的模型"""
        with self._lock:
            if key in self._cache:
                self._access_count[key] = self._access_count.get(key, 0) + 1
                self._last_access[key] = time.time()
                return self._cache[key]
            return None
    
    def put(self, key: str, model: Any) -> None:
        """缓存模型"""
        with self._lock:
            self._cache[key] = model
            self._access_count[key] = 0
            self._last_access[key] = time.time()
    
    def clear(self, key: Optional[str] = None) -> None:
        """清理缓存"""
        with self._lock:
            if key:
                self._cache.pop(key, None)
                self._access_count.pop(key, None)
                self._last_access.pop(key, None)
            else:
                self._cache.clear()
                self._access_count.clear()
                self._last_access.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            return {
                "cache_size": len(self._cache),
                "access_counts": self._access_count.copy(),
                "last_access": self._last_access.copy()
            }


def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        if not self.config.performance_monitoring:
            return func(self, *args, **kwargs)
        
        # 记录开始时间和资源使用
        start_time = time.time()
        start_memory = self._get_memory_usage()
        start_cpu = psutil.cpu_percent()
        
        try:
            result = func(self, *args, **kwargs)
            
            # 记录结束时间和资源使用
            end_time = time.time()
            end_memory = self._get_memory_usage()
            end_cpu = psutil.cpu_percent()
            
            # 计算性能指标
            metrics = PerformanceMetrics(
                processing_time=end_time - start_time,
                memory_used=end_memory - start_memory,
                cpu_usage=(start_cpu + end_cpu) / 2,
                gpu_memory_used=self._get_gpu_memory_usage()
            )
            
            # 记录性能指标
            self._record_performance(func.__name__, metrics)
            
            return result
            
        except Exception as e:
            # 记录错误性能指标
            end_time = time.time()
            metrics = PerformanceMetrics(
                processing_time=end_time - start_time,
                memory_used=self._get_memory_usage() - start_memory
            )
            self._record_performance(f"{func.__name__}_error", metrics)
            raise
    
    return wrapper


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(self, *args, **kwargs)
                    
                except Exception as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"{func.__name__} 最终失败，已重试 {max_retries} 次: {e}")
                        raise
                    
                    logger.warning(f"{func.__name__} 失败，第 {attempt + 1} 次重试: {e}")
                    
                    # 智能延迟策略
                    actual_delay = delay * (2 ** attempt)  # 指数退避
                    time.sleep(actual_delay)
                    
                    # 特殊处理GPU内存不足的情况
                    if "out of memory" in str(e).lower():
                        self._handle_memory_error()
            
            raise last_exception
        
        return wrapper
    return decorator


class WhisperXEngine:
    """WhisperX语音识别引擎
    
    集成WhisperX提供高精度语音识别、时间戳对齐和说话人分离功能。
    支持GPU加速和批量处理。
    
    重构优化特性：
    - 智能模型缓存和预加载
    - 增强错误处理和重试机制
    - 性能监控和资源管理
    - 内存优化和自动清理
    """
    
    def __init__(self, config: WhisperXConfig):
        """初始化WhisperX引擎
        
        Args:
            config: WhisperX配置对象
        """
        self.config = config
        self.model = None
        self.align_model = None
        self.align_metadata = None
        self.diarize_model = None
        
        # 新增组件
        self._model_cache = ModelCache()
        self._performance_history = []
        self._lock = threading.RLock()
        self._is_warmed_up = False
        
        # 检查WhisperX是否可用
        if whisperx is None:
            raise WhisperXError("WhisperX库未安装，请先安装whisperx")
        
        logger.info(f"初始化WhisperX引擎: {config.model_name}, 设备: {config.device}")
        
        # 预加载模型（如果启用）
        if self.config.preload_models:
            self._preload_models()
    
    @performance_monitor
    @retry_on_failure()
    def load_model(self) -> bool:
        """加载转录模型
        
        Returns:
            bool: 加载是否成功
        """
        cache_key = f"model_{self.config.model_name}_{self.config.device}_{self.config.compute_type}"
        
        # 尝试从缓存加载
        cached_model = self._model_cache.get(cache_key)
        if cached_model:
            logger.info(f"从缓存加载模型: {self.config.model_name}")
            self.model = cached_model
            return True
        
        try:
            logger.info(f"正在加载模型: {self.config.model_name}")
            
            # 内存优化：清理不必要的缓存
            if self.config.memory_optimization:
                self._optimize_memory_before_loading()
            
            self.model = whisperx.load_model(
                self.config.model_name,
                self.config.device,
                compute_type=self.config.compute_type
            )
            
            # 缓存模型
            self._model_cache.put(cache_key, self.model)
            
            logger.info("模型加载成功")
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            self.model = None
            
            # 智能降级处理
            if "out of memory" in str(e).lower() and self.config.device == "cuda":
                return self._handle_gpu_memory_fallback()
            
            return False
    
    @performance_monitor
    @retry_on_failure()
    def load_align_model(self) -> bool:
        """加载对齐模型
        
        Returns:
            bool: 加载是否成功
        """
        if not self.config.language:
            logger.warning("未指定语言，跳过对齐模型加载")
            return True
        
        cache_key = f"align_{self.config.language}_{self.config.device}"
        
        # 尝试从缓存加载
        cached_models = self._model_cache.get(cache_key)
        if cached_models:
            logger.info(f"从缓存加载对齐模型: {self.config.language}")
            self.align_model, self.align_metadata = cached_models
            return True
            
        try:
            logger.info(f"正在加载对齐模型: {self.config.language}")
            
            self.align_model, self.align_metadata = whisperx.load_align_model(
                language_code=self.config.language,
                device=self.config.device
            )
            
            # 缓存模型
            self._model_cache.put(cache_key, (self.align_model, self.align_metadata))
            
            logger.info("对齐模型加载成功")
            return True
            
        except Exception as e:
            logger.error(f"对齐模型加载失败: {e}")
            self.align_model = None
            self.align_metadata = None
            return False
    
    def load_cached_model(self) -> bool:
        """加载缓存模型
        
        Returns:
            bool: 缓存加载是否成功
        """
        if self.model is not None:
            return True
        
        # 检查缓存统计
        cache_stats = self._model_cache.get_stats()
        if cache_stats["cache_size"] > 0:
            logger.info(f"模型缓存统计: {cache_stats}")
        
        return self.load_model()
    
    @performance_monitor
    def clear_model_cache(self) -> bool:
        """清理模型缓存
        
        Returns:
            bool: 清理是否成功
        """
        try:
            with self._lock:
                # 清理模型引用
                self.model = None
                self.align_model = None
                self.align_metadata = None
                self.diarize_model = None
                
                # 清理缓存
                self._model_cache.clear()
                
                # 强制垃圾回收
                gc.collect()
                
                # 清理GPU内存
                if self.config.device == "cuda" and torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.ipc_collect()
            
            logger.info("模型缓存清理完成")
            return True
            
        except Exception as e:
            logger.error(f"缓存清理失败: {e}")
            return False
    
    def check_model_health(self) -> bool:
        """检查模型健康状态
        
        Returns:
            bool: 模型是否健康
        """
        if self.model is None:
            return False
        
        try:
            # 增强的健康检查
            if not hasattr(self.model, 'transcribe'):
                return False
            
            # 检查GPU内存状态（如果使用GPU）
            if self.config.device == "cuda" and torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated()
                memory_cached = torch.cuda.memory_reserved()
                
                # 如果内存使用过高，可能存在问题
                if memory_allocated > memory_cached * 0.9:
                    logger.warning("GPU内存使用率过高")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"模型健康检查失败: {e}")
            return False
    
    def check_model_compatibility(self, model_name: str) -> bool:
        """检查模型兼容性
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 是否兼容
        """
        supported_models = [
            "tiny", "tiny.en", 
            "base", "base.en", 
            "small", "small.en", 
            "medium", "medium.en", 
            "large", "large-v1", "large-v2", "large-v3"
        ]
        return model_name in supported_models
    
    @performance_monitor
    def warm_up_model(self) -> bool:
        """预热模型
        
        Returns:
            bool: 预热是否成功
        """
        if self.model is None:
            if not self.load_model():
                return False
        
        if self._is_warmed_up:
            return True
        
        try:
            logger.info("开始模型预热...")
            
            # 创建短音频进行预热
            import numpy as np
            dummy_audio = np.zeros(16000, dtype=np.float32)  # 1秒静音
            
            # 执行一次虚拟转录以预热模型
            _ = self.model.transcribe(
                dummy_audio, 
                batch_size=1,
                language=self.config.language
            )
            
            self._is_warmed_up = True
            logger.info("模型预热完成")
            return True
            
        except Exception as e:
            logger.error(f"模型预热失败: {e}")
            return False
    
    @performance_monitor
    def transcribe(self, audio_path: str, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """转录音频文件（重构优化版）
        
        Args:
            audio_path: 音频文件路径
            progress_callback: 进度回调函数
            
        Returns:
            Dict: 转录结果
            
        Raises:
            FileNotFoundError: 音频文件不存在
            WhisperXError: 转录过程中的错误
        """
        audio_path = Path(audio_path)
        
        # 输入验证
        if not audio_path.exists():
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        # 文件格式验证
        if not self._is_supported_audio_format(audio_path):
            raise WhisperXError(f"不支持的音频格式: {audio_path.suffix}")
        
        # 确保模型已加载
        if not self._ensure_models_loaded():
            raise WhisperXError("无法加载必要的模型")
        
        try:
            return self._transcribe_with_optimization(audio_path, progress_callback)
            
        except Exception as e:
            logger.error(f"转录失败 {audio_path}: {e}")
            raise WhisperXError(f"转录失败: {e}")
    
    def _transcribe_with_optimization(self, audio_path: Path, progress_callback: Optional[Callable]) -> Dict[str, Any]:
        """优化的转录实现"""
        logger.info(f"开始转录音频: {audio_path}")
        
        # 进度回调
        if progress_callback:
            progress_callback(0.1)
        
        # 智能音频加载和预处理
        audio = self._load_and_preprocess_audio(audio_path)
        
        if progress_callback:
            progress_callback(0.3)
        
        # 执行转录
        result = self._execute_transcription(audio)
        
        if progress_callback:
            progress_callback(0.7)
        
        # 词级对齐（如果启用且可用）
        if self._should_perform_alignment():
            result = self._perform_alignment(result, audio)
        
        # 说话人分离（如果启用）
        if self.config.speaker_diarization and self.diarize_model:
            result = self._perform_diarization(result, audio)
        
        if progress_callback:
            progress_callback(1.0)
        
        logger.info(f"转录完成: {audio_path}")
        return result
    
    def _load_and_preprocess_audio(self, audio_path: Path) -> Any:
        """加载和预处理音频"""
        try:
            # 使用WhisperX的优化加载器
            audio = whisperx.load_audio(str(audio_path))
            
            # 可选的音频预处理
            if self.config.asmr_mode:
                audio = self._preprocess_asmr_audio(audio)
            
            return audio
            
        except Exception as e:
            raise WhisperXError(f"音频加载失败: {e}")
    
    def _execute_transcription(self, audio: Any) -> Dict[str, Any]:
        """执行核心转录逻辑"""
        return self.model.transcribe(
            audio,
            batch_size=self.config.batch_size,
            language=self.config.language,
            vad_filter=True,
            vad_parameters={
                "vad_onset": self.config.vad_threshold,
                "vad_offset": self.config.vad_threshold,
                "min_speech_duration_ms": self.config.vad_min_speech_duration_ms,
                "max_silence_duration_ms": self.config.vad_max_silence_duration_ms
            }
        )
    
    def _should_perform_alignment(self) -> bool:
        """检查是否应该执行对齐"""
        return (
            self.config.word_timestamps and 
            self.align_model is not None and 
            self.align_metadata is not None
        )
    
    def _perform_alignment(self, result: Dict[str, Any], audio: Any) -> Dict[str, Any]:
        """执行词级对齐"""
        try:
            return whisperx.align(
                result["segments"],
                self.align_model,
                self.align_metadata,
                audio,
                self.config.device,
                return_char_alignments=False
            )
        except Exception as e:
            logger.warning(f"词级对齐失败，使用原始结果: {e}")
            return result
    
    def _perform_diarization(self, result: Dict[str, Any], audio: Any) -> Dict[str, Any]:
        """执行说话人分离"""
        try:
            # 实现说话人分离逻辑
            # 这里需要根据实际需求实现
            logger.info("说话人分离功能待实现")
            return result
        except Exception as e:
            logger.warning(f"说话人分离失败: {e}")
            return result
    
    @performance_monitor
    def transcribe_batch(self, audio_files: List[str], 
                        max_workers: Optional[int] = None) -> List[Dict[str, Any]]:
        """优化的批量转录功能
        
        Args:
            audio_files: 音频文件路径列表
            max_workers: 最大并发工作线程数
            
        Returns:
            List[Dict]: 转录结果列表
        """
        import concurrent.futures
        from threading import Semaphore
        
        # 智能确定并发数
        if max_workers is None:
            max_workers = min(len(audio_files), self._get_optimal_worker_count())
        
        # 使用信号量控制内存使用
        memory_semaphore = Semaphore(max_workers)
        results = []
        
        def process_single_file(audio_file: str) -> Dict[str, Any]:
            with memory_semaphore:
                try:
                    return self.transcribe(audio_file)
                except Exception as e:
                    logger.error(f"批量转录失败 {audio_file}: {e}")
                    return {"error": str(e), "file": audio_file}
        
        # 并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(process_single_file, af): af 
                for af in audio_files
            }
            
            for future in concurrent.futures.as_completed(future_to_file):
                results.append(future.result())
        
        return results
    
    def transcribe_to_srt(self, audio_path: str) -> str:
        """转录为SRT格式
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            str: SRT格式字符串
        """
        result = self.transcribe(audio_path)
        
        srt_content = []
        for i, segment in enumerate(result.get("segments", []), 1):
            start_time = self._seconds_to_srt_time(segment["start"])
            end_time = self._seconds_to_srt_time(segment["end"])
            text = segment["text"].strip()
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(text)
            srt_content.append("")
        
        return "\n".join(srt_content)
    
    def transcribe_to_vtt(self, audio_path: str) -> str:
        """转录为VTT格式
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            str: VTT格式字符串
        """
        result = self.transcribe(audio_path)
        
        vtt_content = ["WEBVTT", ""]
        
        for segment in result.get("segments", []):
            start_time = self._seconds_to_vtt_time(segment["start"])
            end_time = self._seconds_to_vtt_time(segment["end"])
            text = segment["text"].strip()
            
            vtt_content.append(f"{start_time} --> {end_time}")
            vtt_content.append(text)
            vtt_content.append("")
        
        return "\n".join(vtt_content)
    
    def transcribe_to_json(self, audio_path: str) -> str:
        """转录为JSON格式
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            str: JSON格式字符串
        """
        import json
        result = self.transcribe(audio_path)
        return json.dumps(result, ensure_ascii=False, indent=2)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式
        
        Args:
            seconds: 秒数
            
        Returns:
            str: SRT时间格式 (HH:MM:SS,mmm)
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"
    
    def _seconds_to_vtt_time(self, seconds: float) -> str:
        """将秒数转换为VTT时间格式
        
        Args:
            seconds: 秒数
            
        Returns:
            str: VTT时间格式 (HH:MM:SS.mmm)
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}.{millis:03d}"
    
    # 新增的辅助和优化方法
    def _ensure_models_loaded(self) -> bool:
        """确保所有必要的模型都已加载"""
        if self.model is None:
            if not self.load_model():
                return False
        
        if self.config.word_timestamps and self.align_model is None:
            if not self.load_align_model():
                logger.warning("对齐模型加载失败，将跳过词级对齐")
        
        return True
    
    def _is_supported_audio_format(self, audio_path: Path) -> bool:
        """检查音频格式是否支持"""
        supported_formats = {'.wav', '.mp3', '.flac', '.m4a', '.mp4', '.avi', '.mov'}
        return audio_path.suffix.lower() in supported_formats
    
    def _preprocess_asmr_audio(self, audio: Any) -> Any:
        """ASMR音频预处理"""
        # 实现ASMR特定的音频预处理逻辑
        # 例如：噪声减少、音量标准化等
        logger.info("应用ASMR音频预处理")
        return audio
    
    def _get_optimal_worker_count(self) -> int:
        """获取最优工作线程数"""
        cpu_count = os.cpu_count() or 4
        
        # 根据设备类型调整
        if self.config.device == "cuda":
            # GPU模式下减少并发以避免内存不足
            return max(1, cpu_count // 4)
        else:
            # CPU模式下可以使用更多并发
            return max(1, cpu_count // 2)
    
    def _handle_gpu_memory_fallback(self) -> bool:
        """处理GPU内存不足的智能降级"""
        logger.warning("GPU内存不足，尝试智能降级...")
        
        # 策略1：清理缓存后重试
        self.clear_model_cache()
        
        try:
            self.model = whisperx.load_model(
                self.config.model_name,
                self.config.device,
                compute_type=self.config.compute_type
            )
            logger.info("清理缓存后GPU加载成功")
            return True
        except:
            pass
        
        # 策略2：降级计算精度
        if self.config.compute_type == "float16":
            try:
                logger.info("尝试使用float32精度...")
                self.config.compute_type = "float32"
                self.model = whisperx.load_model(
                    self.config.model_name,
                    self.config.device,
                    compute_type=self.config.compute_type
                )
                logger.info("降级精度后GPU加载成功")
                return True
            except:
                pass
        
        # 策略3：降级到CPU
        logger.warning("降级到CPU模式")
        self.config.device = "cpu"
        self.config.compute_type = "float32"
        return self.load_model()
    
    def _handle_memory_error(self):
        """处理内存错误"""
        logger.info("处理内存错误，清理缓存...")
        self.clear_model_cache()
    
    def _optimize_memory_before_loading(self):
        """加载前的内存优化"""
        # 强制垃圾回收
        gc.collect()
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    def _preload_models(self):
        """预加载模型"""
        logger.info("开始预加载模型...")
        try:
            self.load_model()
            if self.config.word_timestamps:
                self.load_align_model()
            logger.info("模型预加载完成")
        except Exception as e:
            logger.warning(f"模型预加载失败: {e}")
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0
    
    def _get_gpu_memory_usage(self) -> float:
        """获取GPU内存使用量（MB）"""
        try:
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / 1024 / 1024
        except:
            pass
        return 0.0
    
    def _record_performance(self, operation: str, metrics: PerformanceMetrics):
        """记录性能指标"""
        with self._lock:
            metrics.timestamp = time.time()
            self._performance_history.append({
                "operation": operation,
                "metrics": metrics,
                "timestamp": metrics.timestamp
            })
            
            # 保持历史记录在合理大小
            if len(self._performance_history) > 1000:
                self._performance_history = self._performance_history[-500:]
        
        # 记录关键性能指标到日志
        if metrics.processing_time > 0:
            logger.info(f"{operation}: {metrics.processing_time:.2f}s, "
                       f"内存: {metrics.memory_used:.1f}MB, "
                       f"GPU内存: {metrics.gpu_memory_used:.1f}MB")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self._lock:
            if not self._performance_history:
                return {"message": "暂无性能数据"}
            
            # 计算统计信息
            processing_times = [h["metrics"].processing_time for h in self._performance_history]
            memory_usage = [h["metrics"].memory_used for h in self._performance_history]
            
            return {
                "total_operations": len(self._performance_history),
                "avg_processing_time": sum(processing_times) / len(processing_times),
                "max_processing_time": max(processing_times),
                "min_processing_time": min(processing_times),
                "avg_memory_usage": sum(memory_usage) / len(memory_usage),
                "cache_stats": self._model_cache.get_stats(),
                "recent_operations": self._performance_history[-10:]
            }
    
    def reset_performance_stats(self):
        """重置性能统计"""
        with self._lock:
            self._performance_history.clear()
        logger.info("性能统计已重置") 