#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细依赖关系检查工具 - 检查.txt文件中的实际状态
"""

import re
from pathlib import Path
from typing import Dict, List, Tuple

class DetailedDependencyChecker:
    """详细依赖关系检查器"""
    
    def __init__(self):
        self.tasks_dir = Path(".taskmaster/tasks")
        self.tasks_info = {}
        self.critical_issues = []
        
    def parse_task_file(self, task_file: Path) -> Dict:
        """解析单个任务文件"""
        try:
            with open(task_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取基本信息
            task_id_match = re.search(r'# Task ID: (\d+)', content)
            title_match = re.search(r'# Title: (.+)', content)
            status_match = re.search(r'# Status: (.+)', content)
            dependencies_match = re.search(r'# Dependencies: (.+)', content)
            priority_match = re.search(r'# Priority: (.+)', content)
            
            task_info = {
                'id': int(task_id_match.group(1)) if task_id_match else None,
                'title': title_match.group(1).strip() if title_match else "Unknown",
                'status': status_match.group(1).strip() if status_match else "pending",
                'priority': priority_match.group(1).strip() if priority_match else "medium",
                'dependencies': []
            }
            
            # 解析依赖关系
            if dependencies_match:
                deps_str = dependencies_match.group(1).strip()
                if deps_str and deps_str.lower() != 'none':
                    deps = [int(x.strip()) for x in deps_str.split(',') if x.strip().isdigit()]
                    task_info['dependencies'] = deps
            
            # 检查子任务完成情况
            completed_subtasks = len(re.findall(r'## \d+\. .+ \[completed\]', content))
            total_subtasks = len(re.findall(r'## \d+\. .+', content))
            
            task_info['completed_subtasks'] = completed_subtasks
            task_info['total_subtasks'] = total_subtasks
            task_info['progress_percent'] = (completed_subtasks / total_subtasks * 100) if total_subtasks > 0 else 0
            
            return task_info
            
        except Exception as e:
            print(f"❌ 解析文件 {task_file} 失败: {e}")
            return None
    
    def load_all_tasks(self) -> bool:
        """加载所有任务文件"""
        try:
            task_files = list(self.tasks_dir.glob("task_*.txt"))
            print(f"📁 发现 {len(task_files)} 个任务文件")
            
            for task_file in task_files:
                task_info = self.parse_task_file(task_file)
                if task_info and task_info['id']:
                    self.tasks_info[task_info['id']] = task_info
            
            print(f"✅ 成功加载 {len(self.tasks_info)} 个任务")
            return True
            
        except Exception as e:
            print(f"❌ 加载任务文件失败: {e}")
            return False
    
    def check_status_dependency_conflicts(self) -> None:
        """检查状态与依赖关系的冲突"""
        print("\n🔍 检查状态与依赖关系冲突...")
        
        for task_id, task in self.tasks_info.items():
            task_status = task['status']
            dependencies = task['dependencies']
            
            # 解析复合状态（如 "in_progress (3/9 subtasks completed)"）
            if 'in_progress' in task_status.lower() or 'completed' in task_status.lower():
                print(f"  📋 Task {task_id}: {task_status}")
                
                # 检查依赖任务状态
                for dep_id in dependencies:
                    if dep_id in self.tasks_info:
                        dep_status = self.tasks_info[dep_id]['status']
                        dep_title = self.tasks_info[dep_id]['title']
                        
                        if 'pending' in dep_status.lower():
                            severity = "🚨 CRITICAL" if task['progress_percent'] > 0 else "⚠️  WARNING"
                            self.critical_issues.append({
                                'severity': severity,
                                'task_id': task_id,
                                'task_title': task['title'],
                                'task_status': task_status,
                                'task_progress': f"{task['completed_subtasks']}/{task['total_subtasks']} subtasks completed",
                                'dep_id': dep_id,
                                'dep_title': dep_title,
                                'dep_status': dep_status,
                                'issue': f"Task {task_id} 有进展但依赖任务 {dep_id} 未开始"
                            })
                            
                            print(f"    {severity} 依赖任务 {dep_id} ({dep_title}) 状态: {dep_status}")
    
    def check_task_13_special_status(self) -> None:
        """特别检查Task 13的状态影响"""
        print("\n🎯 特别检查 Task 13 (TDD基础设施) 的状态影响...")
        
        task_13 = self.tasks_info.get(13)
        if not task_13:
            print("  ❌ Task 13 不存在")
            return
        
        print(f"  📋 Task 13 状态: {task_13['status']}")
        print(f"  📊 Task 13 进度: {task_13['completed_subtasks']}/{task_13['total_subtasks']} subtasks")
        
        # 找出所有依赖Task 13的任务
        dependent_tasks = []
        for task_id, task in self.tasks_info.items():
            if 13 in task['dependencies']:
                dependent_tasks.append((task_id, task))
        
        print(f"  📈 {len(dependent_tasks)} 个任务依赖 Task 13")
        
        # 检查依赖任务的状态
        tasks_with_progress = []
        for task_id, task in dependent_tasks:
            if task['progress_percent'] > 0 or 'in_progress' in task['status'].lower():
                tasks_with_progress.append((task_id, task))
        
        if tasks_with_progress and 'pending' in task_13['status'].lower():
            print(f"  🚨 严重问题: Task 13 未开始，但 {len(tasks_with_progress)} 个依赖任务已有进展:")
            
            for task_id, task in tasks_with_progress:
                progress_info = f"{task['completed_subtasks']}/{task['total_subtasks']} subtasks ({task['progress_percent']:.0f}%)"
                print(f"    - Task {task_id}: {task['status']} - {progress_info}")
                
                self.critical_issues.append({
                    'severity': '🚨 CRITICAL',
                    'task_id': task_id,
                    'task_title': task['title'],
                    'task_status': task['status'],
                    'task_progress': progress_info,
                    'dep_id': 13,
                    'dep_title': task_13['title'],
                    'dep_status': task_13['status'],
                    'issue': 'TDD基础设施未完成但依赖任务已开始 - 违反TDD原则'
                })
    
    def check_priority_execution_order(self) -> None:
        """检查优先级和执行顺序"""
        print("\n🎯 检查优先级和建议执行顺序...")
        
        # 按优先级分组
        critical_tasks = []
        high_tasks = []
        medium_tasks = []
        
        for task_id, task in self.tasks_info.items():
            priority = task['priority'].lower()
            if 'critical' in priority:
                critical_tasks.append((task_id, task))
            elif 'high' in priority:
                high_tasks.append((task_id, task))
            else:
                medium_tasks.append((task_id, task))
        
        print(f"  🔴 Critical 任务: {[t[0] for t in critical_tasks]}")
        print(f"  🟠 High 任务: {[t[0] for t in high_tasks]}")
        print(f"  🟡 Medium 任务: {[t[0] for t in medium_tasks]}")
        
        # 检查Critical任务是否被优先处理
        for task_id, task in critical_tasks:
            if 'pending' in task['status'].lower() and task['progress_percent'] == 0:
                print(f"  ⚠️  Critical 任务 {task_id} 未开始: {task['title']}")
    
    def generate_fix_recommendations(self) -> None:
        """生成修复建议"""
        print("\n" + "="*60)
        print("🛠️  修复建议")
        print("="*60)
        
        if not self.critical_issues:
            print("✅ 未发现严重的依赖关系问题")
            return
        
        print(f"🚨 发现 {len(self.critical_issues)} 个严重问题:")
        
        for i, issue in enumerate(self.critical_issues, 1):
            print(f"\n{i}. {issue['severity']} - {issue['issue']}")
            print(f"   任务: Task {issue['task_id']} - {issue['task_title']}")
            print(f"   状态: {issue['task_status']}")
            if 'task_progress' in issue:
                print(f"   进度: {issue['task_progress']}")
            print(f"   依赖: Task {issue['dep_id']} - {issue['dep_title']} ({issue['dep_status']})")
        
        print(f"\n🎯 立即行动建议:")
        print("1. 🚨 立即启动 Task 13 (TDD流程监控和质量保障)")
        print("2. 📋 完成 Task 13.1 (TDD度量体系建立)")
        print("3. 🔄 暂停其他依赖Task 13的开发工作，直到基础设施完成")
        print("4. 📊 重新规划任务执行顺序，确保基础设施优先")
        print("5. 🎯 将 Task 13 优先级确保为 critical")
        
        print(f"\n⚠️  风险提醒:")
        print("- 当前状态违反了TDD最佳实践：应该先建立监控体系再开发")
        print("- 继续在没有TDD基础设施的情况下开发可能导致质量问题")
        print("- Task 2 的子任务完成可能缺乏适当的质量保障")
    
    def run_detailed_check(self) -> bool:
        """运行详细检查"""
        print("🔍 开始详细依赖关系检查...")
        
        if not self.load_all_tasks():
            return False
        
        self.check_status_dependency_conflicts()
        self.check_task_13_special_status()
        self.check_priority_execution_order()
        self.generate_fix_recommendations()
        
        return len(self.critical_issues) == 0

def main():
    """主函数"""
    checker = DetailedDependencyChecker()
    success = checker.run_detailed_check()
    
    if success:
        print("\n✅ 详细检查完成，无严重问题！")
        return 0
    else:
        print("\n❌ 发现严重的依赖关系问题，需要立即修复！")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main()) 