# Voice-came 语音翻译系统 - 环境搭建指南

## 📋 目录

- [系统要求](#系统要求)
- [快速安装](#快速安装)
- [详细安装步骤](#详细安装步骤)
  - [Windows 10/11](#windows-1011)
  - [macOS](#macos)
  - [Linux](#linux)
- [验证安装](#验证安装)
- [常见问题](#常见问题)
- [手动安装](#手动安装)

## 🔧 系统要求

### 硬件要求

**最低配置：**
- CPU: Intel Core i5 或 AMD Ryzen 5
- 内存: 8GB RAM
- 存储: 10GB 可用空间
- 网络: 稳定的互联网连接（用于下载依赖）

**推荐配置：**
- CPU: Intel Core i7 或 AMD Ryzen 7
- 内存: 16GB+ RAM  
- 存储: 50GB+ SSD 空间
- GPU: NVIDIA GPU（可选，用于加速）

### 软件要求

- **Python**: 3.8 - 3.11
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+

## 🚀 快速安装

### 方法一：一键安装（推荐）

1. **克隆仓库**
   ```bash
   git clone https://github.com/voice-came/voice-came.git
   cd voice-came
   ```

2. **运行安装脚本**
   ```bash
   python setup.py
   ```
   
   脚本会自动检测您的操作系统并运行相应的安装脚本。

### 方法二：操作系统特定脚本

**Windows:**
```batch
scripts\setup_windows.bat
```

**macOS/Linux:**
```bash
bash scripts/setup_unix.sh
```

## 📖 详细安装步骤

### Windows 10/11

#### 前置要求

1. **安装 Python 3.8-3.11**
   - 从 [Python官网](https://www.python.org/downloads/) 下载
   - ⚠️ 安装时勾选 "Add Python to PATH"

2. **安装 Git**（可选）
   - 从 [Git官网](https://git-scm.com/download/win) 下载

#### 安装步骤

1. **以管理员身份运行PowerShell**
   ```powershell
   # 检查Python版本
   python --version
   
   # 克隆项目
   git clone https://github.com/voice-came/voice-came.git
   cd voice-came
   ```

2. **运行Windows安装脚本**
   ```batch
   scripts\setup_windows.bat
   ```

3. **选择安装选项**
   - 是否安装Chocolatey（推荐）
   - 是否安装FFmpeg
   - 是否创建虚拟环境（推荐）

#### GPU支持（可选）

如果您有NVIDIA GPU：

1. **安装CUDA Toolkit**
   - 下载：[CUDA Toolkit 12.1](https://developer.nvidia.com/cuda-12-1-0-download-archive)
   - 按照安装向导完成安装

2. **验证CUDA安装**
   ```batch
   nvidia-smi
   ```

### macOS

#### 前置要求

1. **安装 Homebrew**（推荐）
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. **安装 Python**
   ```bash
   brew install python@3.10
   ```

#### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/voice-came/voice-came.git
   cd voice-came
   ```

2. **运行安装脚本**
   ```bash
   chmod +x scripts/setup_unix.sh
   bash scripts/setup_unix.sh
   ```

3. **按照提示选择安装选项**

### Linux

#### Ubuntu/Debian

1. **更新系统**
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

2. **安装Python和依赖**
   ```bash
   sudo apt install python3 python3-pip python3-venv git ffmpeg build-essential -y
   ```

3. **克隆项目并安装**
   ```bash
   git clone https://github.com/voice-came/voice-came.git
   cd voice-came
   chmod +x scripts/setup_unix.sh
   bash scripts/setup_unix.sh
   ```

#### CentOS/RHEL

1. **安装EPEL仓库**
   ```bash
   sudo yum install epel-release -y
   ```

2. **安装依赖**
   ```bash
   sudo yum install python3 python3-pip git ffmpeg gcc gcc-c++ make -y
   ```

3. **克隆项目并安装**
   ```bash
   git clone https://github.com/voice-came/voice-came.git
   cd voice-came
   chmod +x scripts/setup_unix.sh
   bash scripts/setup_unix.sh
   ```

## ✅ 验证安装

### 运行测试脚本

```bash
python test_installation.py
```

### 预期输出

```
Voice-came 系统测试
------------------------------
PyTorch: 2.1.0
WhisperX: OK
Transformers: 4.35.0
音频处理库: OK
✅ 所有测试通过！
```

### 手动验证

```python
# 测试核心组件
import torch
print(f"PyTorch: {torch.__version__}")
print(f"CUDA 可用: {torch.cuda.is_available()}")

import whisperx
print("WhisperX: 导入成功")

import transformers
print(f"Transformers: {transformers.__version__}")
```

## 🔧 常见问题

### Q1: Python版本不兼容

**问题**: 显示Python版本不在3.8-3.11范围内

**解决方案**:
- Windows: 从Python官网重新下载安装
- macOS: `brew install python@3.10`
- Linux: 使用系统包管理器安装指定版本

### Q2: FFmpeg未找到

**问题**: 安装过程中提示FFmpeg未找到

**解决方案**:
- Windows: 安装Chocolatey后运行 `choco install ffmpeg`
- macOS: `brew install ffmpeg`
- Linux: `sudo apt install ffmpeg` (Ubuntu/Debian)

### Q3: CUDA相关错误

**问题**: PyTorch无法使用GPU

**解决方案**:
1. 确认安装了CUDA Toolkit
2. 重新安装PyTorch:
   ```bash
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
   ```

### Q4: 内存不足

**问题**: 运行时内存不足

**解决方案**:
- 关闭其他应用程序
- 使用CPU版本而非GPU版本
- 增加虚拟内存/交换分区

### Q5: 权限问题

**问题**: Linux/macOS上权限被拒绝

**解决方案**:
```bash
chmod +x scripts/setup_unix.sh
# 或者使用sudo（不推荐用于虚拟环境）
```

## 🛠 手动安装

如果自动安装脚本失败，可以手动安装：

### 1. 创建虚拟环境

```bash
python -m venv voice-came-env

# Windows
voice-came-env\Scripts\activate

# macOS/Linux  
source voice-came-env/bin/activate
```

### 2. 安装PyTorch

**CPU版本:**
```bash
pip install torch torchvision torchaudio
```

**GPU版本（NVIDIA）:**
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### 3. 安装其他依赖

```bash
pip install -r requirements.txt
```

### 4. 验证安装

```bash
python test_installation.py
```

## 🚦 下一步

安装完成后，您可以：

1. **查看项目结构**: 了解代码布局
2. **运行示例**: 测试基本功能
3. **查看API文档**: 了解如何使用
4. **参与开发**: 查看贡献指南

## 📞 获取帮助

如果遇到问题：

1. **查看日志**: 查看安装脚本的详细输出
2. **搜索Issues**: 在GitHub上搜索类似问题
3. **提交Issue**: 详细描述您的环境和错误信息
4. **社区讨论**: 加入我们的讨论组

---

## 📝 更新日志

- **v0.1.0**: 初始版本，支持基本安装流程
- 支持Windows、macOS、Linux三大平台
- 自动检测CUDA支持
- 完整的依赖管理

---

**注意**: 此文档会随着项目发展持续更新。建议定期检查最新版本。 