---
description: 
globs: 
alwaysApply: false
---
# Voice-came Testing Strategy (TDD)

## Testing Philosophy
Voice-came follows **Test-Driven Development (TDD)** with the "Red-Green-Refactor" cycle:
- 🔴 **Red**: Write failing test first
- 🟢 **Green**: Write minimal code to pass test
- 🔵 **Refactor**: Optimize while keeping tests green

## Test Pyramid Architecture
```
        /\     E2E Tests (10%)
       /  \    End-to-end user scenarios
      /____\   
     /      \  Integration Tests (20%) 
    /        \ Module interaction testing
   /__________\
  /            \ Unit Tests (70%)
 /              \ Function/class logic testing
/________________\
```

## Test Coverage Requirements
- **Unit Tests**: 70% of test suite, ≥90% code coverage
- **Integration Tests**: 20% of test suite, module interactions
- **E2E Tests**: 10% of test suite, complete user workflows
- **Overall Coverage**: ≥80% code coverage minimum

## Test Organization

### Test Directory Structure
```
tests/
├── unit/                           # Unit tests (70%)
│   ├── test_whisperx_engine.py     # WhisperX engine tests
│   ├── test_batch_processor.py     # Batch processing tests
│   ├── test_translation_manager.py # Translation tests
│   └── test_terminology.py         # Terminology management tests
├── integration/                    # Integration tests (20%)
│   ├── test_whisperx_integration.py
│   ├── test_translation_integration.py
│   └── test_file_processing.py
├── e2e/                           # End-to-end tests (10%)
│   ├── test_complete_workflow.py
│   └── test_batch_processing_e2e.py
├── fixtures/                      # Test data
│   ├── sample_audio.wav
│   ├── sample_video.mp4
│   └── terminology_test.json
└── conftest.py                    # Pytest configuration
```

## TDD Development Cycle (75 minutes per feature)

### 1. Analyze Requirements (5 min)
- Review user story and acceptance criteria
- Identify testable behaviors
- Plan test scenarios

### 2. Write Failing Test (15 min)
```python
def test_whisperx_transcribe_video():
    """Test WhisperX transcription of video file"""
    # Arrange
    engine = WhisperXEngine(model="base", device="cpu")
    video_path = "tests/fixtures/sample_video.mp4"
    
    # Act & Assert - Should fail initially
    with pytest.raises(NotImplementedError):
        result = engine.transcribe_video(video_path)
```

### 3. Confirm Test Fails (2 min)
```bash
pytest tests/unit/test_whisperx_engine.py::test_whisperx_transcribe_video -v
```

### 4. Write Minimal Implementation (30 min)
```python
class WhisperXEngine:
    def transcribe_video(self, video_path: str) -> dict:
        # Minimal implementation to pass test
        raise NotImplementedError("Feature not implemented yet")
```

### 5. Make Test Pass (5 min)
```bash
pytest tests/unit/test_whisperx_engine.py::test_whisperx_transcribe_video -v
```

### 6. Refactor and Optimize (15 min)
- Improve code structure
- Add error handling
- Optimize performance
- Keep tests passing

### 7. Regression Testing (3 min)
```bash
pytest tests/ -v --cov=voice_came
```

## Test Categories and Examples

### Unit Tests (175 detailed test cases)

#### WhisperX Engine Tests (45 cases)
```python
class TestWhisperXEngine:
    def test_engine_initialization(self):
        """Test engine creates correctly with valid params"""
        
    def test_transcribe_success(self):
        """Test successful audio transcription"""
        
    def test_transcribe_file_not_found(self):
        """Test handling of missing audio files"""
        
    def test_batch_processing(self):
        """Test batch audio processing"""
        
    def test_vad_filtering(self):
        """Test voice activity detection"""
```

#### Translation Engine Tests (38 cases)
```python
class TestTranslationManager:
    def test_gemma_translation(self):
        """Test Gemma3-12B translation"""
        
    def test_qwen_fallback(self):
        """Test fallback to Qwen model"""
        
    def test_terminology_replacement(self):
        """Test automatic term replacement"""
        
    def test_multi_language_batch(self):
        """Test batch translation to multiple languages"""

class TestTranslationIntegration:
    def test_voicetransl_adapter_contract(self):
        """Test VoiceTransl adapter interface contract"""
        
    def test_translation_interface_compliance(self):
        """Test all engines follow translation interface"""
        
    def test_fallback_chain_behavior(self):
        """Test translation engine fallback chain"""
        
    def test_terminology_consistency(self):
        """Test terminology consistency across engines"""
```

#### Interface Contract Tests (15 cases)
```python
@pytest.mark.interface
class TestTranslationContracts:
    def test_translation_request_response_contract(self):
        """Test translation request/response data models"""
        
    def test_engine_interface_contract(self):
        """Test translation engine interface compliance"""
        
    def test_voicetransl_integration_contract(self):
        """Test VoiceTransl system integration contract"""
```

### Integration Tests (20% coverage)
```python
class TestWhisperXIntegration:
    def test_whisperx_with_translation(self):
        """Test complete speech-to-translation pipeline"""
        
    def test_batch_processing_pipeline(self):
        """Test full batch processing workflow"""
        
    def test_error_recovery_integration(self):
        """Test error handling across modules"""
```

### End-to-End Tests (10% coverage)
```python
class TestCompleteWorkflow:
    def test_video_upload_to_export(self):
        """Test complete user workflow from upload to export"""
        
    def test_batch_100_videos(self):
        """Test processing 100 videos end-to-end"""
        
    def test_terminology_consistency(self):
        """Test terminology consistency across full workflow"""
```

## Test Data Management

### Mock Data Generator
```python
# tests/fixtures/mock_data_generator.py
def generate_test_video(duration_minutes: int, language: str = "zh") -> str:
    """Generate test video with known content"""
    
def generate_terminology_conflicts() -> Dict[str, str]:
    """Generate terminology test cases with conflicts"""
    
def generate_batch_test_files(count: int) -> List[str]:
    """Generate batch of test files for stress testing"""
```

### Test Data Categories
- **Audio Samples**: Clear speech, noisy audio, multi-speaker
- **Video Samples**: 5min-3hour various formats
- **Translation Samples**: Sleep terminology, edge cases
- **Invalid Files**: Corrupted, wrong format, empty files
- **Performance Data**: Large files for stress testing

## Quality Gates and CI/CD

### Pre-commit Hooks
```bash
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: pytest-unit
        name: Run unit tests
        entry: pytest tests/unit/ -v
        language: system
      - id: coverage-check
        name: Check test coverage
        entry: pytest --cov=voice_came --cov-fail-under=80
        language: system
```

### CI Pipeline Quality Gates
1. **Unit Tests**: 100% pass rate required
2. **Coverage**: ≥80% code coverage required
3. **Integration Tests**: 100% pass rate required
4. **Performance Tests**: Meet PRD speed requirements
5. **Code Quality**: Linting and type checking pass

### Release Quality Gates
- **Complete Test Suite**: 100% pass rate
- **Performance Benchmarks**: 3-hour video ≤ 30min processing
- **Stress Testing**: 100 consecutive video processing
- **User Acceptance**: ≥4.0/5.0 satisfaction score

## Test Execution

### Local Development
```bash
# Run specific test categories
pytest tests/unit/ -v                    # Unit tests only
pytest tests/integration/ -v             # Integration tests
pytest tests/e2e/ -v                     # End-to-end tests

# Coverage reporting
pytest --cov=voice_came --cov-report=html

# Performance benchmarking
pytest tests/performance/ --benchmark-only
```

### Continuous Integration
```bash
# Daily build pipeline
pytest tests/ -v --cov=voice_came --cov-report=xml
pytest tests/performance/ --benchmark-json=benchmark.json
pytest tests/e2e/ --video-samples=production
```

## Error Simulation Testing
```python
# Test error scenarios
class TestErrorScenarios:
    def test_gpu_memory_exhaustion(self):
        """Simulate GPU out of memory"""
        
    def test_network_interruption(self):
        """Simulate network failure during API calls"""
        
    def test_disk_space_full(self):
        """Simulate disk space exhaustion"""
        
    def test_model_corruption(self):
        """Simulate corrupted model files"""
```

## Success Metrics
- **Defect Discovery Rate**: ≥85%
- **Defect Escape Rate**: ≤10%
- **Test Automation Rate**: ≥80%
- **Code Coverage**: ≥80%
- **Build Success Rate**: ≥95%
- **Performance Regression**: 0 tolerance

