# Voice-came 模型下载管理文档

本文档整理了Voice-came项目中所需的各种AI模型下载地址和管理方案，为开发人员和用户提供便捷的模型获取指南。

## 1. 语音识别模型

### 1.1 Whisper.cpp 模型

**存放位置**: `whisper/` 文件夹  
**文件格式**: `.bin` 文件，以 `ggml` 开头  
**兼容性**: 支持 NVIDIA/AMD/Intel GPU (Vulkan)

| 模型名称 | 文件大小 | 显存需求 | 下载地址 | 描述 |
|---------|---------|---------|----------|------|
| ggml-small.bin | 466 MiB | ~852 MB | [下载](https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-small.bin?download=true) | 小型模型，速度快，适合快速转录 |
| ggml-medium.bin | 1.5 GiB | ~2.1 GB | [下载](https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-medium.bin?download=true) | 中型模型，平衡性能与精度 |
| ggml-large-v2.bin | 2.9 GiB | ~3.9 GB | [下载](https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-large-v2.bin?download=true) | 大型模型，最高精度 |

### 1.2 Faster-Whisper 模型

**存放位置**: `whisper-faster/` 文件夹  
**文件格式**: 文件夹结构，以 `faster-whisper-` 开头  
**兼容性**: 主要支持 NVIDIA GPU，需要CUDA支持  
**额外要求**: 需要下载 [Faster-Whisper-XXL](https://github.com/Purfview/whisper-standalone-win/releases/tag/Faster-Whisper-XXL) 并重命名为 `whisper-faster.exe`

| 模型名称 | 文件大小 | 显存需求 | 下载地址 | 描述 |
|---------|---------|---------|----------|------|
| faster-whisper-small | 463 MiB | ~1 GB | [下载](https://huggingface.co/Systran/faster-whisper-small) | GPU加速小型模型，支持VAD |
| faster-whisper-medium | 1.42 GiB | ~2 GB | [下载](https://huggingface.co/Systran/faster-whisper-medium) | GPU加速中型模型 |
| faster-whisper-large-v3 | 2.87 GiB | ~3 GB | [下载](https://huggingface.co/Systran/faster-whisper-large-v3) | GPU加速大型模型，最新版本 |

**文件夹结构要求**:
```
faster-whisper-xxx/
├── config.json
├── model.bin
├── preprocessor_config.json
├── tokenizer.json
└── vocabulary.json
```

### 1.3 WhisperX 模型 (推荐集成)

**项目地址**: [https://github.com/m-bain/whisperX](https://github.com/m-bain/whisperX)  
**特色功能**: 
- 70倍实时转录速度
- 词级别精确时间戳
- 多说话人识别
- VAD预处理
- 批量推理优化

**安装方式**:
```bash
pip install whisperx
```

**模型下载**: WhisperX会自动下载所需模型，包括:
- Whisper模型 (自动从OpenAI获取)
- WAV2VEC2对齐模型 (自动从Hugging Face获取)
- 说话人识别模型 (需要Hugging Face Token)

## 2. 离线翻译模型

### 2.1 LLaMA.cpp 模型

**存放位置**: `llama/` 文件夹  
**文件格式**: `.gguf` 文件  
**兼容性**: 支持 NVIDIA/AMD/Intel GPU (Vulkan/CUDA)

#### 2.1.1 日语专用模型

| 模型名称 | 语言 | 代码标识 | 文件大小 | 显存需求 | 下载地址 | 描述 |
|---------|------|----------|----------|----------|----------|------|
| Sakura-7B-v1.0-Q4 | 日语 | sakura-010 | ~5 GiB | ~8 GB | [下载](https://huggingface.co/SakuraLLM/Sakura-7B-Qwen2.5-v1.0-GGUF) | 日语翻译专用，7B参数 |
| Sakura-GalTransl-7B-v3 | 日语 | galtransl | ~5 GiB | ~8 GB | [下载](https://huggingface.co/SakuraLLM/Sakura-GalTransl-7B-v3) | 游戏文本翻译优化 |
| Sakura-14B-v1.0-Q4 | 日语 | sakura-010 | ~9 GiB | ~16 GB | [下载](https://huggingface.co/SakuraLLM/Sakura-14B-Qwen2.5-v1.0-GGUF) | 高精度日语翻译，14B参数 |

#### 2.1.2 多语种模型

| 模型名称 | 语言 | 代码标识 | 文件大小 | 显存需求 | 下载地址 | 描述 |
|---------|------|----------|----------|----------|----------|------|
| Qwen3-8B-Q4 | 多语种 | llamacpp | ~5 GiB | ~8 GB | [下载](https://huggingface.co/ggml-org/Qwen3-8B-GGUF) | 通用多语种翻译模型 |
| Gemma3-12B-Q4 | 多语种 | llamacpp | ~7 GiB | ~14 GB | [下载](https://huggingface.co/ggml-org/gemma-3-12b-it-GGUF) | Google开发的多语种模型 |

### 2.2 CUDA加速版本 (NVIDIA GPU用户)

**下载地址**: 
- [cudart-llama-bin-win-cu12.4-x64.zip](https://github.com/ggerganov/llama.cpp/releases)
- [llama-bxxxx-bin-win-cuda-cu12.4-x64.zip](https://github.com/ggerganov/llama.cpp/releases)

**安装方式**: 解压到 `llama/` 文件夹覆盖原有文件

## 3. 在线翻译模型API

### 3.1 支持的在线服务

| 服务商 | API地址 | 模型示例 | 获取方式 |
|--------|---------|----------|----------|
| DeepSeek | https://api.deepseek.com | deepseek-chat | [官网注册](https://platform.deepseek.com/) |
| Moonshot | https://api.moonshot.cn | moonshot-v1-8k | [官网注册](https://platform.moonshot.cn/) |
| GLM | https://open.bigmodel.cn/api/paas | glm-4 | [智谱AI](https://open.bigmodel.cn/) |
| Minimax | https://api.minimax.chat | abab6.5s-chat | [Minimax](https://api.minimax.chat/) |
| Doubao | https://ark.cn-beijing.volces.com/api | doubao-lite-4k | [火山引擎](https://console.volcengine.com/) |
| Qwen | https://dashscope.aliyuncs.com/compatible-mode | qwen-turbo | [阿里云](https://dashscope.aliyun.com/) |
| Gemini | https://generativelanguage.googleapis.com | gemini-pro | [Google AI](https://ai.google.dev/) |

### 3.2 本地部署服务

| 服务 | 默认地址 | 描述 |
|------|----------|------|
| Ollama | http://localhost:11434 | 本地LLM部署平台 |
| LlamaCpp | http://localhost:8989 | LlamaCpp服务器模式 |

## 4. 模型管理建议

### 4.1 硬件配置推荐

**最低配置**:
- CPU: Intel i5 或 AMD Ryzen 5
- 内存: 16GB
- 存储: 20GB可用空间
- GPU: 可选，但推荐用于加速

**推荐配置**:
- CPU: Intel i7 或 AMD Ryzen 7
- 内存: 32GB+
- 存储: 50GB+ SSD
- GPU: NVIDIA RTX 4060Ti 16GB+ (如用户配置)

### 4.2 模型选择指南

**语音识别模型选择**:
- 快速转录: `ggml-small.bin` 或 `faster-whisper-small`
- 平衡性能: `ggml-medium.bin` 或 `faster-whisper-medium`
- 最高精度: `ggml-large-v2.bin` 或 `faster-whisper-large-v3`
- 专业需求: 集成 `WhisperX` (推荐)

**翻译模型选择**:
- 日语翻译: `Sakura-7B-v1.0-Q4` 或 `Sakura-GalTransl-7B-v3`
- 多语种翻译: `Qwen3-8B-Q4`
- 高精度需求: `Sakura-14B-v1.0-Q4` 或 `Gemma3-12B-Q4`
- 在线服务: DeepSeek、Moonshot等（需要网络连接）

### 4.3 存储空间管理

**预估空间需求**:
- 基础配置: ~10GB (小型语音识别 + 中型翻译模型)
- 标准配置: ~25GB (中型语音识别 + 大型翻译模型)
- 专业配置: ~50GB+ (大型语音识别 + 多个翻译模型)

### 4.4 下载优化建议

1. **使用下载工具**: 推荐使用支持断点续传的下载工具
2. **镜像站点**: 如果Hugging Face访问困难，可使用国内镜像
3. **批量下载**: 建议一次性下载所需的所有模型
4. **验证完整性**: 下载完成后验证文件大小和MD5

## 5. 自动化模型管理方案

### 5.1 模型下载管理器设计

建议在Voice-came中集成自动模型下载管理功能:

```python
class ModelDownloadManager:
    def __init__(self):
        self.models_registry = {
            # 语音识别模型
            'whisper': {...},
            'faster-whisper': {...},
            'whisperx': {...},
            # 翻译模型
            'llama': {...}
        }
    
    def check_model_availability(self, model_type, model_name):
        """检查模型是否已下载"""
        pass
    
    def download_model(self, model_type, model_name, progress_callback=None):
        """下载指定模型"""
        pass
    
    def get_download_progress(self, download_id):
        """获取下载进度"""
        pass
    
    def verify_model_integrity(self, model_path):
        """验证模型文件完整性"""
        pass
```

### 5.2 用户界面集成

- **模型管理标签页**: 显示已安装和可下载的模型
- **一键安装**: 根据用户硬件配置推荐模型组合
- **进度显示**: 实时显示下载进度和状态
- **存储管理**: 显示磁盘使用情况和清理选项

## 6. 故障排除

### 6.1 常见下载问题

1. **网络连接问题**: 检查防火墙和代理设置
2. **存储空间不足**: 清理磁盘空间或更换存储位置
3. **文件损坏**: 重新下载并验证文件完整性
4. **权限问题**: 确保程序有写入权限

### 6.2 模型加载问题

1. **显存不足**: 降低模型大小或调整批处理参数
2. **CUDA错误**: 更新GPU驱动和CUDA版本
3. **依赖缺失**: 安装所需的运行时库

---

**更新日期**: 2024年12月
**维护者**: Voice-came开发团队
**参考项目**: 
- [VoiceTransl](https://github.com/shinnpuru/VoiceTransl)
- [WhisperX](https://github.com/m-bain/whisperX)
- [LLaMA.cpp](https://github.com/ggerganov/llama.cpp)
- [Faster-Whisper](https://github.com/Purfview/whisper-standalone-win)