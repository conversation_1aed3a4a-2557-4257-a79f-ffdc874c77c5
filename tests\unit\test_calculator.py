"""
计算器模块测试

测试src/voice_came/example.py模块的功能
"""

import pytest
from src.voice_came.example import add, multiply, divide, Calculator


class TestBasicFunctions:
    """测试基本数学函数"""
    
    @pytest.mark.unit
    def test_add(self):
        """测试加法函数"""
        assert add(2, 3) == 5
        assert add(-1, 1) == 0
        assert add(0, 0) == 0
    
    @pytest.mark.unit
    def test_multiply(self):
        """测试乘法函数"""
        assert multiply(2, 3) == 6
        assert multiply(-2, 3) == -6
        assert multiply(0, 5) == 0
    
    @pytest.mark.unit
    def test_divide(self):
        """测试除法函数"""
        assert divide(6, 2) == 3.0
        assert divide(-6, 2) == -3.0
        assert divide(1, 2) == 0.5
    
    @pytest.mark.unit
    def test_divide_by_zero(self):
        """测试除零异常"""
        with pytest.raises(ValueError, match="Cannot divide by zero"):
            divide(1, 0)


class TestCalculator:
    """测试计算器类"""
    
    @pytest.fixture
    def calculator(self):
        """提供计算器实例"""
        return Calculator()
    
    @pytest.mark.unit
    def test_init(self, calculator):
        """测试初始化"""
        assert calculator.history == []
    
    @pytest.mark.unit
    def test_add_operation(self, calculator):
        """测试加法操作"""
        result = calculator.calculate("add", 2, 3)
        assert result == 5
        assert len(calculator.history) == 1
        assert "2 add 3 = 5" in calculator.history[0]
    
    @pytest.mark.unit
    def test_multiply_operation(self, calculator):
        """测试乘法操作"""
        result = calculator.calculate("multiply", 2, 3)
        assert result == 6
        assert len(calculator.history) == 1
    
    @pytest.mark.unit
    def test_divide_operation(self, calculator):
        """测试除法操作"""
        result = calculator.calculate("divide", 6, 2)
        assert result == 3.0
        assert len(calculator.history) == 1
    
    @pytest.mark.unit
    def test_unknown_operation(self, calculator):
        """测试未知操作"""
        with pytest.raises(ValueError, match="Unknown operation"):
            calculator.calculate("subtract", 5, 3)
    
    @pytest.mark.unit
    def test_history(self, calculator):
        """测试历史记录功能"""
        calculator.calculate("add", 1, 2)
        calculator.calculate("multiply", 3, 4)
        
        history = calculator.get_history()
        assert len(history) == 2
        assert "1 add 2 = 3" in history[0]
        assert "3 multiply 4 = 12" in history[1]
        
        # 确保返回的是副本
        history.append("test")
        assert len(calculator.get_history()) == 2 