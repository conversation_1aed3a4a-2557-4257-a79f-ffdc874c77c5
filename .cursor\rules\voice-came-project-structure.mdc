---
description:
globs:
alwaysApply: false
---
# Voice-came Project Structure Guide

## Project Overview
Voice-came is a sleep content globalization tool based on VoiceTransl architecture, integrating WhisperX for speech processing and local translation models (Gemma3-12B, Qwen3) for cost-effective multilingual translation.

## Directory Structure
```
Voice-came/
├── voice_came/                       # 🏗️ Main code directory
│   ├── core/                         # Core business logic (engine-agnostic)
│   │   ├── base_engine.py            # Base engine abstract class
│   │   ├── whisperx_engine.py        # WhisperX engine implementation
│   │   ├── batch_processor.py        # Batch processing controller
│   │   └── translation_manager.py    # Translation management
│   │
│   ├── engines/                      # 🔧 Engine implementations (pluggable)
│   │   ├── whisperx/                 # WhisperX related
│   │   │   ├── whisperx_wrapper.py   # WhisperX wrapper
│   │   │   └── models/               # Model files directory
│   │   └── translation/              # Translation engines
│   │       ├── gemma_translator.py   # Gemma translator
│   │       └── qwen_translator.py    # Qwen translator
│   │
│   ├── translation/                  # 🔄 Translation module (layered architecture)
│   │   ├── models.py                 # Data models and interfaces
│   │   ├── business.py               # Business logic layer
│   │   └── integration.py            # External system integration (VoiceTransl adapter)
│   │
│   ├── utils/                        # 🛠️ Utility functions (business-agnostic)
│   │   ├── audio_utils.py            # Audio processing tools
│   │   ├── file_utils.py             # File operation tools
│   │   ├── text_utils.py             # Text processing tools
│   │   └── logger.py                 # Logging utilities
│   │
│   ├── config/                       # ⚙️ Configuration management
│   │   ├── settings.py               # Main configuration file
│   │   ├── whisperx_config.yaml      # WhisperX configuration
│   │   └── translation_config.json   # Translation configuration
│   │
│   ├── ui/                           # 🖥️ User interface
│   │   ├── main_window.py            # Main window
│   │   ├── batch_dialog.py           # Batch processing dialog
│   │   └── progress_widget.py        # Progress display component
│   │
│   └── exceptions/                   # ❌ Exception definitions
│       ├── engine_exceptions.py      # Engine exceptions
│       └── processing_exceptions.py  # Processing exceptions
│
├── tests/                            # 🧪 Test directory
│   ├── test_whisperx_engine.py       # WhisperX engine tests
│   ├── test_batch_processor.py       # Batch processing tests
│   ├── translation/                  # Translation module tests
│   │   ├── test_models.py            # Data model tests
│   │   ├── test_business.py          # Business logic tests
│   │   └── test_integration.py       # Integration/contract tests
│   └── fixtures/                     # Test data
│
├── scripts/                          # 📜 Scripts directory
├── data/                             # 📊 Data directory (models, temp, output)
├── logs/                             # 📋 Log directory
└── docs/                             # 📚 Documentation
    └── development/                  # Development documentation
        ├── [Voice-came_开发规范_v1.0.md](mdc:Voice-came_开发规范_v1.0.md)
        ├── [Voice-came_PRD_v4.1.md](mdc:Voice-came_PRD_v4.1.md)
        └── [WhisperX集成方案.md](mdc:WhisperX集成方案.md)
```

## File Placement Decision Rules

### Core Business Logic → `voice_came/core/`
- Engine-agnostic business logic
- Main processing workflows
- Abstract base classes

### Engine Implementations → `voice_came/engines/`
- WhisperX integration → `voice_came/engines/whisperx/`
- Translation engines → `voice_came/engines/translation/`
- Pluggable components

### Translation Module → `voice_came/translation/`
- Data models and interfaces → `voice_came/translation/models.py`
- Business logic layer → `voice_came/translation/business.py`
- External system integration → `voice_came/translation/integration.py`
- VoiceTransl adapter and interface contracts

### Utility Functions → `voice_came/utils/`
- Audio processing tools
- File operation helpers
- Business-agnostic utilities

### Configuration → `voice_came/config/`
- YAML/JSON configuration files
- Settings management
- Environment-specific configs

### Tests → `tests/`
- Unit tests: `test_[module_name].py`
- Integration tests: `tests/integration/`
- Test data: `tests/fixtures/`

### Documentation → `docs/`
- Development docs: `docs/development/`
- API reference: `docs/API_REFERENCE.md`
- User guides: `docs/`

## Naming Conventions
- **Python files**: `snake_case.py` (e.g., `whisperx_engine.py`)
- **Classes**: `PascalCase` (e.g., `WhisperXEngine`)
- **Functions/Variables**: `snake_case` (e.g., `process_audio_file`)
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `MAX_BATCH_SIZE`)
- **Test files**: `test_[module_name].py`
- **Config files**: `[component]_config.[yaml|json]`
