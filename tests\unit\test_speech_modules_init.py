#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音相关模块初始化文件测试

测试speech_recognition、speech_synthesis、translation模块的__init__.py
"""

import sys
import os
import pytest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

import voice_came.speech_recognition as speech_recognition_module
import voice_came.speech_synthesis as speech_synthesis_module
import voice_came.translation as translation_module


class TestSpeechRecognitionModule:
    """测试speech_recognition模块"""
    
    def test_module_exists(self):
        """测试模块存在"""
        assert speech_recognition_module is not None
    
    def test_module_name(self):
        """测试模块名称"""
        assert speech_recognition_module.__name__ == 'voice_came.speech_recognition'
    
    def test_module_file(self):
        """测试模块文件"""
        assert hasattr(speech_recognition_module, '__file__')
        assert speech_recognition_module.__file__.endswith('__init__.py')
    
    def test_module_package(self):
        """测试模块包名"""
        assert hasattr(speech_recognition_module, '__package__')
        assert speech_recognition_module.__package__ == 'voice_came.speech_recognition'
    
    def test_module_path(self):
        """测试模块路径"""
        assert hasattr(speech_recognition_module, '__path__')
    
    def test_module_importable(self):
        """测试模块可以导入"""
        from voice_came import speech_recognition
        assert speech_recognition is not None
    
    def test_module_reload(self):
        """测试模块重新加载"""
        import importlib
        reloaded_module = importlib.reload(speech_recognition_module)
        assert reloaded_module is not None
    
    def test_module_dir(self):
        """测试模块目录内容"""
        module_attrs = dir(speech_recognition_module)
        # 应该至少有基本的模块属性
        expected_attrs = ['__name__', '__file__', '__package__', '__path__']
        for attr in expected_attrs:
            assert attr in module_attrs


class TestSpeechSynthesisModule:
    """测试speech_synthesis模块"""
    
    def test_module_exists(self):
        """测试模块存在"""
        assert speech_synthesis_module is not None
    
    def test_module_name(self):
        """测试模块名称"""
        assert speech_synthesis_module.__name__ == 'voice_came.speech_synthesis'
    
    def test_module_file(self):
        """测试模块文件"""
        assert hasattr(speech_synthesis_module, '__file__')
        assert speech_synthesis_module.__file__.endswith('__init__.py')
    
    def test_module_package(self):
        """测试模块包名"""
        assert hasattr(speech_synthesis_module, '__package__')
        assert speech_synthesis_module.__package__ == 'voice_came.speech_synthesis'
    
    def test_module_path(self):
        """测试模块路径"""
        assert hasattr(speech_synthesis_module, '__path__')
    
    def test_module_importable(self):
        """测试模块可以导入"""
        from voice_came import speech_synthesis
        assert speech_synthesis is not None
    
    def test_module_reload(self):
        """测试模块重新加载"""
        import importlib
        reloaded_module = importlib.reload(speech_synthesis_module)
        assert reloaded_module is not None
    
    def test_module_dir(self):
        """测试模块目录内容"""
        module_attrs = dir(speech_synthesis_module)
        # 应该至少有基本的模块属性
        expected_attrs = ['__name__', '__file__', '__package__', '__path__']
        for attr in expected_attrs:
            assert attr in module_attrs


class TestTranslationModule:
    """测试translation模块"""
    
    def test_module_exists(self):
        """测试模块存在"""
        assert translation_module is not None
    
    def test_module_name(self):
        """测试模块名称"""
        assert translation_module.__name__ == 'voice_came.translation'
    
    def test_module_file(self):
        """测试模块文件"""
        assert hasattr(translation_module, '__file__')
        assert translation_module.__file__.endswith('__init__.py')
    
    def test_module_package(self):
        """测试模块包名"""
        assert hasattr(translation_module, '__package__')
        assert translation_module.__package__ == 'voice_came.translation'
    
    def test_module_path(self):
        """测试模块路径"""
        assert hasattr(translation_module, '__path__')
    
    def test_module_importable(self):
        """测试模块可以导入"""
        from voice_came import translation
        assert translation is not None
    
    def test_module_reload(self):
        """测试模块重新加载"""
        import importlib
        reloaded_module = importlib.reload(translation_module)
        assert reloaded_module is not None
    
    def test_module_dir(self):
        """测试模块目录内容"""
        module_attrs = dir(translation_module)
        # 应该至少有基本的模块属性
        expected_attrs = ['__name__', '__file__', '__package__', '__path__']
        for attr in expected_attrs:
            assert attr in module_attrs


class TestUtilsModule:
    """测试utils模块"""
    
    def test_utils_module_import(self):
        """测试utils模块导入"""
        import voice_came.utils as utils_module
        assert utils_module is not None
    
    def test_utils_module_name(self):
        """测试utils模块名称"""
        import voice_came.utils as utils_module
        assert utils_module.__name__ == 'voice_came.utils'
    
    def test_utils_module_file(self):
        """测试utils模块文件"""
        import voice_came.utils as utils_module
        assert hasattr(utils_module, '__file__')
        assert utils_module.__file__.endswith('__init__.py')
    
    def test_utils_module_package(self):
        """测试utils模块包名"""
        import voice_came.utils as utils_module
        assert hasattr(utils_module, '__package__')
        assert utils_module.__package__ == 'voice_came.utils'
    
    def test_utils_module_path(self):
        """测试utils模块路径"""
        import voice_came.utils as utils_module
        assert hasattr(utils_module, '__path__')
    
    def test_utils_module_importable(self):
        """测试utils模块可以导入"""
        from voice_came import utils
        assert utils is not None
    
    def test_utils_module_reload(self):
        """测试utils模块重新加载"""
        import importlib
        import voice_came.utils as utils_module
        reloaded_module = importlib.reload(utils_module)
        assert reloaded_module is not None


class TestModuleIntegration:
    """测试模块集成"""
    
    def test_all_modules_importable_from_parent(self):
        """测试所有模块都可以从父包导入"""
        from voice_came import speech_recognition
        from voice_came import speech_synthesis
        from voice_came import translation
        from voice_came import utils
        
        assert speech_recognition is not None
        assert speech_synthesis is not None
        assert translation is not None
        assert utils is not None
    
    def test_modules_are_different_objects(self):
        """测试模块是不同的对象"""
        modules = [
            speech_recognition_module,
            speech_synthesis_module,
            translation_module
        ]
        
        # 每个模块都应该是不同的对象
        for i, module1 in enumerate(modules):
            for j, module2 in enumerate(modules):
                if i != j:
                    assert module1 is not module2
    
    def test_modules_have_unique_names(self):
        """测试模块有唯一的名称"""
        module_names = [
            speech_recognition_module.__name__,
            speech_synthesis_module.__name__,
            translation_module.__name__
        ]
        
        # 所有模块名应该是唯一的
        assert len(module_names) == len(set(module_names))
    
    def test_modules_in_same_package(self):
        """测试模块在同一个包中"""
        modules = [
            speech_recognition_module,
            speech_synthesis_module,
            translation_module
        ]
        
        for module in modules:
            assert module.__package__.startswith('voice_came.')
    
    def test_star_imports_work(self):
        """测试星号导入工作正常"""
        # 这些模块目前是空的，所以星号导入应该不会导入任何内容
        namespace = {}
        
        exec("from voice_came.speech_recognition import *", namespace)
        exec("from voice_came.speech_synthesis import *", namespace)
        exec("from voice_came.translation import *", namespace)
        
        # 由于模块是空的，不应该有新的导入
        # 只检查没有错误发生
        assert True  # 如果到达这里，说明导入成功


class TestModuleEdgeCases:
    """测试模块边界情况"""
    
    def test_module_attributes_exist(self):
        """测试模块属性存在"""
        modules = [
            speech_recognition_module,
            speech_synthesis_module,
            translation_module
        ]
        
        for module in modules:
            # 每个模块都应该有这些基本属性
            assert hasattr(module, '__name__')
            assert hasattr(module, '__file__')
            assert hasattr(module, '__package__')
            assert hasattr(module, '__path__')
    
    def test_module_file_paths_are_different(self):
        """测试模块文件路径不同"""
        modules = [
            speech_recognition_module,
            speech_synthesis_module,
            translation_module
        ]
        
        file_paths = [module.__file__ for module in modules]
        
        # 所有文件路径应该是唯一的
        assert len(file_paths) == len(set(file_paths))
    
    def test_module_paths_are_different(self):
        """测试模块路径不同"""
        modules = [
            speech_recognition_module,
            speech_synthesis_module,
            translation_module
        ]
        
        module_paths = [str(module.__path__) for module in modules]
        
        # 所有模块路径应该是唯一的
        assert len(module_paths) == len(set(module_paths))
    
    def test_modules_are_packages(self):
        """测试模块是包"""
        modules = [
            speech_recognition_module,
            speech_synthesis_module,
            translation_module
        ]
        
        for module in modules:
            # 包应该有__path__属性
            assert hasattr(module, '__path__')
            # __path__应该是一个列表
            assert isinstance(module.__path__, list)
            # __path__应该不为空
            assert len(module.__path__) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 