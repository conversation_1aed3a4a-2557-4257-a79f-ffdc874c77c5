#!/usr/bin/env python3
"""
TDD开发工具脚本

提供便捷的TDD开发命令，包括：
- 运行测试
- 检查代码覆盖率
- 代码格式化
- 代码质量检查
- 生成测试报告
"""

import sys
import subprocess
import argparse
from pathlib import Path
from typing import List, Optional


def run_command(command: List[str], cwd: Optional[Path] = None) -> int:
    """运行命令并返回退出码"""
    print(f"执行命令: {' '.join(command)}")
    result = subprocess.run(command, cwd=cwd)
    return result.returncode


def run_tests(test_type: str = "all", verbose: bool = False) -> int:
    """运行测试"""
    command = ["pytest"]
    
    if verbose:
        command.append("-v")
    
    if test_type == "unit":
        command.extend(["-m", "unit"])
    elif test_type == "integration":
        command.extend(["-m", "integration"])
    elif test_type == "fast":
        command.extend(["-m", "not slow"])
    
    command.extend([
        "--cov=src/voice_came",
        "--cov-report=term-missing",
        "--cov-report=html"
    ])
    
    return run_command(command)


def format_code() -> int:
    """格式化代码"""
    commands = [
        ["black", "src/", "tests/"],
        ["isort", "src/", "tests/"]
    ]
    
    for command in commands:
        result = run_command(command)
        if result != 0:
            return result
    
    return 0


def check_code_quality() -> int:
    """检查代码质量"""
    commands = [
        ["flake8", "src/", "tests/"],
        ["mypy", "src/voice_came"],
        ["bandit", "-r", "src/"]
    ]
    
    for command in commands:
        result = run_command(command)
        if result != 0:
            print(f"代码质量检查失败: {' '.join(command)}")
            return result
    
    return 0


def generate_coverage_report() -> int:
    """生成覆盖率报告"""
    commands = [
        ["coverage", "run", "-m", "pytest"],
        ["coverage", "report"],
        ["coverage", "html"]
    ]
    
    for command in commands:
        result = run_command(command)
        if result != 0:
            return result
    
    print("覆盖率报告已生成到 htmlcov/ 目录")
    return 0


def pre_commit_check() -> int:
    """运行pre-commit检查"""
    return run_command(["pre-commit", "run", "--all-files"])


def tdd_cycle() -> int:
    """完整的TDD循环：测试->代码质量检查->格式化"""
    print("=== TDD循环开始 ===")
    
    # 1. 运行测试
    print("\n1. 运行测试...")
    result = run_tests("fast", verbose=True)
    if result != 0:
        print("❌ 测试失败，请修复测试")
        return result
    
    # 2. 代码质量检查
    print("\n2. 代码质量检查...")
    result = check_code_quality()
    if result != 0:
        print("❌ 代码质量检查失败")
        return result
    
    # 3. 格式化代码
    print("\n3. 格式化代码...")
    result = format_code()
    if result != 0:
        print("❌ 代码格式化失败")
        return result
    
    print("\n✅ TDD循环完成，所有检查通过！")
    return 0


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="TDD开发工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 测试命令
    test_parser = subparsers.add_parser("test", help="运行测试")
    test_parser.add_argument("--type", choices=["all", "unit", "integration", "fast"], 
                           default="all", help="测试类型")
    test_parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    
    # 格式化命令
    subparsers.add_parser("format", help="格式化代码")
    
    # 质量检查命令
    subparsers.add_parser("check", help="检查代码质量")
    
    # 覆盖率报告命令
    subparsers.add_parser("coverage", help="生成覆盖率报告")
    
    # pre-commit命令
    subparsers.add_parser("pre-commit", help="运行pre-commit检查")
    
    # TDD循环命令
    subparsers.add_parser("cycle", help="运行完整TDD循环")
    
    args = parser.parse_args()
    
    if args.command == "test":
        sys.exit(run_tests(args.type, args.verbose))
    elif args.command == "format":
        sys.exit(format_code())
    elif args.command == "check":
        sys.exit(check_code_quality())
    elif args.command == "coverage":
        sys.exit(generate_coverage_report())
    elif args.command == "pre-commit":
        sys.exit(pre_commit_check())
    elif args.command == "cycle":
        sys.exit(tdd_cycle())
    else:
        parser.print_help()
        sys.exit(1) 