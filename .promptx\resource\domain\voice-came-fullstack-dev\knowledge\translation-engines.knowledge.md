# 翻译引擎专业知识

## 🌐 本地LLM翻译架构

### 核心翻译引擎对比
| 模型 | 大小 | 优势 | 适用场景 |
|------|------|------|----------|
| **Gemma3-12B-Q4** | 6.5GB | Google技术，多语言均衡 | 通用翻译，质量稳定 |
| **Qwen3-14B-Q4** | 7.8GB | 中文优化，术语理解强 | 中文相关翻译 |
| **LLaMA3-8B-Q4** | 4.2GB | 轻量高效，速度快 | 资源受限环境 |

### 翻译引擎架构设计
```python
# 翻译引擎抽象架构
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

@dataclass
class TranslationRequest:
    """翻译请求数据类"""
    text: str
    source_language: str
    target_language: str
    context: Optional[str] = None
    terminology: Optional[Dict[str, str]] = None
    quality_level: str = "balanced"  # fast, balanced, high

@dataclass
class TranslationResult:
    """翻译结果数据类"""
    translated_text: str
    confidence_score: float
    processing_time: float
    model_used: str
    terminology_applied: List[str]
    quality_metrics: Dict[str, float]

class BaseTranslationEngine(ABC):
    """翻译引擎基类"""
    
    def __init__(self, model_path: str, device: str = "cuda"):
        self.model_path = model_path
        self.device = device
        self.model = None
        self.tokenizer = None
        self.terminology_manager = None
    
    @abstractmethod
    def load_model(self):
        """加载模型"""
        pass
    
    @abstractmethod
    def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行翻译"""
        pass
    
    @abstractmethod
    def batch_translate(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """批量翻译"""
        pass
```

## 🔧 Gemma3翻译引擎实现

### Gemma3-12B-Q4集成
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemma3翻译引擎实现

基于Google Gemma3-12B-Q4模型的本地翻译引擎，
专为Voice-came项目优化，支持助眠内容专业术语翻译。
"""

import time
import torch
import gc
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from typing import Dict, List, Optional
import logging

from voice_came.core.base_translation_engine import BaseTranslationEngine
from voice_came.utils.terminology_manager import TerminologyManager
from voice_came.exceptions import TranslationError
from voice_came.utils.logger import get_logger

logger = get_logger(__name__)

class Gemma3TranslationEngine(BaseTranslationEngine):
    """Gemma3翻译引擎
    
    基于Google Gemma3-12B-Q4模型的高质量翻译引擎，
    特别优化了助眠内容的专业术语翻译。
    """
    
    def __init__(
        self,
        model_path: str = "google/gemma-2-12b-it",
        device: str = "cuda",
        quantization: bool = True,
        max_length: int = 2048,
        temperature: float = 0.1
    ):
        """初始化Gemma3翻译引擎
        
        Args:
            model_path: 模型路径或HuggingFace模型名
            device: 计算设备
            quantization: 是否使用量化
            max_length: 最大生成长度
            temperature: 生成温度
        """
        super().__init__(model_path, device)
        
        self.quantization = quantization
        self.max_length = max_length
        self.temperature = temperature
        
        # 术语管理器
        self.terminology_manager = TerminologyManager()
        
        # 加载模型
        self.load_model()
    
    def load_model(self):
        """加载Gemma3模型"""
        try:
            logger.info(f"正在加载Gemma3模型: {self.model_path}")
            
            # 量化配置
            if self.quantization:
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            else:
                quantization_config = None
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                quantization_config=quantization_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            logger.info("Gemma3模型加载完成")
            
        except Exception as e:
            logger.error(f"Gemma3模型加载失败: {e}")
            raise TranslationError(f"模型加载失败: {e}")
    
    def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行翻译
        
        Args:
            request: 翻译请求
            
        Returns:
            TranslationResult: 翻译结果
        """
        start_time = time.time()
        
        try:
            # 术语预处理
            preprocessed_text = self.terminology_manager.preprocess_text(
                request.text, 
                request.source_language
            )
            
            # 构建提示词
            prompt = self._build_translation_prompt(
                preprocessed_text,
                request.source_language,
                request.target_language,
                request.context,
                request.quality_level
            )
            
            # 执行翻译
            translated_text = self._generate_translation(prompt)
            
            # 术语后处理
            final_text = self.terminology_manager.postprocess_text(
                translated_text,
                request.target_language
            )
            
            # 计算置信度
            confidence_score = self._calculate_confidence(
                request.text,
                final_text,
                request.source_language,
                request.target_language
            )
            
            processing_time = time.time() - start_time
            
            return TranslationResult(
                translated_text=final_text,
                confidence_score=confidence_score,
                processing_time=processing_time,
                model_used="gemma3-12b-q4",
                terminology_applied=self.terminology_manager.get_applied_terms(),
                quality_metrics=self._calculate_quality_metrics(request.text, final_text)
            )
            
        except Exception as e:
            logger.error(f"Gemma3翻译失败: {e}")
            raise TranslationError(f"翻译失败: {e}")
    
    def _build_translation_prompt(
        self,
        text: str,
        source_lang: str,
        target_lang: str,
        context: Optional[str] = None,
        quality_level: str = "balanced"
    ) -> str:
        """构建翻译提示词"""
        
        # 语言映射
        lang_map = {
            "zh": "Chinese",
            "en": "English", 
            "ja": "Japanese",
            "ko": "Korean",
            "es": "Spanish",
            "fr": "French",
            "de": "German"
        }
        
        source_lang_name = lang_map.get(source_lang, source_lang)
        target_lang_name = lang_map.get(target_lang, target_lang)
        
        # 基础提示词
        base_prompt = f"""You are a professional translator specializing in sleep and relaxation content. 
Translate the following {source_lang_name} text to {target_lang_name}.

Requirements:
- Maintain the calming and soothing tone
- Preserve technical terms related to sleep, meditation, and ASMR
- Keep the original meaning and emotional impact
- Use natural, fluent {target_lang_name}"""
        
        # 根据质量级别调整提示词
        if quality_level == "high":
            base_prompt += """
- Pay special attention to nuances and cultural context
- Ensure perfect grammar and style
- Optimize for native speaker naturalness"""
        elif quality_level == "fast":
            base_prompt += """
- Focus on accuracy and clarity
- Maintain essential meaning"""
        
        # 添加上下文
        if context:
            base_prompt += f"\n\nContext: {context}"
        
        # 添加术语指导
        base_prompt += """

Important sleep/ASMR terms to preserve:
- 冥想 → meditation
- 放松 → relaxation  
- 助眠 → sleep aid
- 白噪音 → white noise
- 深度睡眠 → deep sleep
- 正念 → mindfulness

Text to translate:
"""
        
        return base_prompt + f"\n{text}\n\nTranslation:"
    
    def _generate_translation(self, prompt: str) -> str:
        """生成翻译文本"""
        try:
            # 编码输入
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                max_length=self.max_length,
                truncation=True,
                padding=True
            ).to(self.device)
            
            # 生成参数
            generation_config = {
                "max_new_tokens": 512,
                "temperature": self.temperature,
                "do_sample": True if self.temperature > 0 else False,
                "top_p": 0.9,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "pad_token_id": self.tokenizer.pad_token_id,
                "eos_token_id": self.tokenizer.eos_token_id
            }
            
            # 生成翻译
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    **generation_config
                )
            
            # 解码输出
            generated_text = self.tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:],
                skip_special_tokens=True
            )
            
            # 清理输出
            translation = self._clean_generated_text(generated_text)
            
            return translation
            
        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            raise TranslationError(f"生成失败: {e}")
    
    def _clean_generated_text(self, text: str) -> str:
        """清理生成的文本"""
        # 移除多余的空白
        text = " ".join(text.split())
        
        # 移除可能的提示词残留
        cleanup_patterns = [
            "Translation:",
            "翻译:",
            "Translate:",
            "Here is the translation:",
            "The translation is:"
        ]
        
        for pattern in cleanup_patterns:
            if text.startswith(pattern):
                text = text[len(pattern):].strip()
        
        return text.strip()
    
    def _calculate_confidence(
        self,
        source_text: str,
        translated_text: str,
        source_lang: str,
        target_lang: str
    ) -> float:
        """计算翻译置信度"""
        try:
            # 基于长度比例的置信度
            length_ratio = len(translated_text) / len(source_text)
            length_confidence = 1.0 - abs(1.0 - length_ratio) * 0.5
            length_confidence = max(0.0, min(1.0, length_confidence))
            
            # 基于术语匹配的置信度
            terminology_confidence = self.terminology_manager.calculate_terminology_confidence(
                source_text, translated_text, source_lang, target_lang
            )
            
            # 综合置信度
            overall_confidence = (length_confidence * 0.3 + terminology_confidence * 0.7)
            
            return round(overall_confidence, 3)
            
        except Exception:
            return 0.5  # 默认置信度
    
    def _calculate_quality_metrics(self, source_text: str, translated_text: str) -> Dict[str, float]:
        """计算质量指标"""
        return {
            "length_ratio": len(translated_text) / len(source_text) if source_text else 0,
            "word_count": len(translated_text.split()),
            "character_count": len(translated_text),
            "sentence_count": translated_text.count('.') + translated_text.count('!') + translated_text.count('?')
        }
    
    def batch_translate(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """批量翻译"""
        results = []
        
        for i, request in enumerate(requests):
            try:
                result = self.translate(request)
                results.append(result)
                
                # 定期清理GPU内存
                if (i + 1) % 10 == 0:
                    self._cleanup_memory()
                    
            except Exception as e:
                logger.error(f"批量翻译失败 {i}: {e}")
                # 创建错误结果
                error_result = TranslationResult(
                    translated_text="",
                    confidence_score=0.0,
                    processing_time=0.0,
                    model_used="gemma3-12b-q4",
                    terminology_applied=[],
                    quality_metrics={}
                )
                results.append(error_result)
        
        return results
    
    def _cleanup_memory(self):
        """清理内存"""
        if self.device == "cuda":
            torch.cuda.empty_cache()
        gc.collect()
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            "model_name": "Gemma3-12B-Q4",
            "model_path": self.model_path,
            "device": self.device,
            "quantization": self.quantization,
            "max_length": self.max_length,
            "temperature": self.temperature,
            "model_loaded": self.model is not None
        }
```

## 🔧 Qwen3翻译引擎实现

### Qwen3-14B-Q4集成
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3翻译引擎实现

基于阿里巴巴Qwen3-14B-Q4模型的本地翻译引擎，
专门优化中文相关翻译任务。
"""

from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
import time
from typing import Dict, List

class Qwen3TranslationEngine(BaseTranslationEngine):
    """Qwen3翻译引擎
    
    基于Qwen3-14B-Q4模型，专门优化中文翻译质量，
    特别适合中文助眠内容的多语言翻译。
    """
    
    def __init__(
        self,
        model_path: str = "Qwen/Qwen2.5-14B-Instruct",
        device: str = "cuda",
        quantization: bool = True
    ):
        super().__init__(model_path, device)
        self.quantization = quantization
        self.terminology_manager = TerminologyManager()
        self.load_model()
    
    def load_model(self):
        """加载Qwen3模型"""
        try:
            logger.info(f"正在加载Qwen3模型: {self.model_path}")
            
            # Qwen3特殊配置
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # 量化配置
            if self.quantization:
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            else:
                quantization_config = None
            
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                quantization_config=quantization_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True
            )
            
            logger.info("Qwen3模型加载完成")
            
        except Exception as e:
            logger.error(f"Qwen3模型加载失败: {e}")
            raise TranslationError(f"模型加载失败: {e}")
    
    def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行翻译（Qwen3优化版本）"""
        start_time = time.time()
        
        try:
            # 构建Qwen3格式的对话
            messages = [
                {
                    "role": "system",
                    "content": self._build_system_prompt(
                        request.source_language,
                        request.target_language
                    )
                },
                {
                    "role": "user", 
                    "content": self._build_user_prompt(
                        request.text,
                        request.context
                    )
                }
            ]
            
            # 应用聊天模板
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            # 生成翻译
            translated_text = self._generate_with_qwen3(text)
            
            # 后处理
            final_text = self.terminology_manager.postprocess_text(
                translated_text,
                request.target_language
            )
            
            processing_time = time.time() - start_time
            
            return TranslationResult(
                translated_text=final_text,
                confidence_score=self._calculate_confidence(
                    request.text, final_text,
                    request.source_language, request.target_language
                ),
                processing_time=processing_time,
                model_used="qwen3-14b-q4",
                terminology_applied=self.terminology_manager.get_applied_terms(),
                quality_metrics=self._calculate_quality_metrics(request.text, final_text)
            )
            
        except Exception as e:
            logger.error(f"Qwen3翻译失败: {e}")
            raise TranslationError(f"翻译失败: {e}")
    
    def _build_system_prompt(self, source_lang: str, target_lang: str) -> str:
        """构建系统提示词（针对Qwen3优化）"""
        return f"""你是一个专业的翻译专家，专门从事睡眠和放松内容的翻译工作。

你的任务是将{source_lang}文本翻译成{target_lang}，需要：
1. 保持原文的舒缓和放松的语调
2. 准确翻译睡眠、冥想、ASMR相关的专业术语
3. 保持原文的情感表达和意境
4. 使用自然流畅的{target_lang}表达

重要术语对照：
- 冥想 → meditation
- 放松 → relaxation
- 助眠 → sleep aid
- 白噪音 → white noise
- 深度睡眠 → deep sleep
- 正念 → mindfulness
- 呼吸练习 → breathing exercise
- 渐进式肌肉放松 → progressive muscle relaxation"""
    
    def _build_user_prompt(self, text: str, context: Optional[str] = None) -> str:
        """构建用户提示词"""
        prompt = f"请翻译以下文本：\n\n{text}"
        
        if context:
            prompt = f"上下文：{context}\n\n" + prompt
        
        return prompt
    
    def _generate_with_qwen3(self, prompt: str) -> str:
        """使用Qwen3生成翻译"""
        try:
            model_inputs = self.tokenizer([prompt], return_tensors="pt").to(self.device)
            
            generated_ids = self.model.generate(
                **model_inputs,
                max_new_tokens=512,
                temperature=0.1,
                do_sample=True,
                top_p=0.9,
                repetition_penalty=1.1
            )
            
            generated_ids = [
                output_ids[len(input_ids):] 
                for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            
            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Qwen3生成失败: {e}")
            raise TranslationError(f"生成失败: {e}")
```

## 🎯 术语管理系统

### 专业术语库设计
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
术语管理系统

管理助眠内容的专业术语翻译，确保翻译一致性和专业性。
"""

import json
import re
from typing import Dict, List, Set, Tuple
from pathlib import Path
import logging

class TerminologyManager:
    """术语管理器"""
    
    def __init__(self, terminology_file: str = "terminology.json"):
        self.terminology_file = Path(terminology_file)
        self.terminology_db = {}
        self.applied_terms = []
        self.load_terminology()
    
    def load_terminology(self):
        """加载术语库"""
        try:
            if self.terminology_file.exists():
                with open(self.terminology_file, 'r', encoding='utf-8') as f:
                    self.terminology_db = json.load(f)
            else:
                # 创建默认术语库
                self.terminology_db = self._create_default_terminology()
                self.save_terminology()
                
            logger.info(f"术语库加载完成，共{len(self.terminology_db)}条术语")
            
        except Exception as e:
            logger.error(f"术语库加载失败: {e}")
            self.terminology_db = self._create_default_terminology()
    
    def _create_default_terminology(self) -> Dict:
        """创建默认术语库"""
        return {
            "sleep_terms": {
                "冥想": {
                    "en": "meditation",
                    "ja": "瞑想",
                    "ko": "명상",
                    "es": "meditación",
                    "fr": "méditation",
                    "de": "Meditation"
                },
                "放松": {
                    "en": "relaxation",
                    "ja": "リラックス",
                    "ko": "이완",
                    "es": "relajación", 
                    "fr": "relaxation",
                    "de": "Entspannung"
                },
                "助眠": {
                    "en": "sleep aid",
                    "ja": "睡眠補助",
                    "ko": "수면 보조",
                    "es": "ayuda para dormir",
                    "fr": "aide au sommeil",
                    "de": "Schlafhilfe"
                },
                "白噪音": {
                    "en": "white noise",
                    "ja": "ホワイトノイズ",
                    "ko": "백색소음",
                    "es": "ruido blanco",
                    "fr": "bruit blanc",
                    "de": "weißes Rauschen"
                },
                "深度睡眠": {
                    "en": "deep sleep",
                    "ja": "深い眠り",
                    "ko": "깊은 잠",
                    "es": "sueño profundo",
                    "fr": "sommeil profond",
                    "de": "Tiefschlaf"
                },
                "正念": {
                    "en": "mindfulness",
                    "ja": "マインドフルネス",
                    "ko": "마음챙김",
                    "es": "atención plena",
                    "fr": "pleine conscience",
                    "de": "Achtsamkeit"
                },
                "呼吸练习": {
                    "en": "breathing exercise",
                    "ja": "呼吸法",
                    "ko": "호흡 운동",
                    "es": "ejercicio de respiración",
                    "fr": "exercice de respiration",
                    "de": "Atemübung"
                },
                "渐进式肌肉放松": {
                    "en": "progressive muscle relaxation",
                    "ja": "漸進的筋弛緩法",
                    "ko": "점진적 근육 이완",
                    "es": "relajación muscular progresiva",
                    "fr": "relaxation musculaire progressive",
                    "de": "progressive Muskelentspannung"
                }
            },
            "asmr_terms": {
                "ASMR": {
                    "en": "ASMR",
                    "ja": "ASMR",
                    "ko": "ASMR",
                    "es": "ASMR",
                    "fr": "ASMR", 
                    "de": "ASMR"
                },
                "触发音": {
                    "en": "trigger sound",
                    "ja": "トリガー音",
                    "ko": "트리거 소리",
                    "es": "sonido disparador",
                    "fr": "son déclencheur",
                    "de": "Trigger-Geräusch"
                },
                "耳语": {
                    "en": "whisper",
                    "ja": "ささやき",
                    "ko": "속삭임",
                    "es": "susurro",
                    "fr": "chuchotement",
                    "de": "Flüstern"
                },
                "角色扮演": {
                    "en": "roleplay",
                    "ja": "ロールプレイ",
                    "ko": "역할극",
                    "es": "juego de roles",
                    "fr": "jeu de rôle",
                    "de": "Rollenspiel"
                }
            }
        }
    
    def preprocess_text(self, text: str, source_lang: str) -> str:
        """翻译前术语预处理"""
        self.applied_terms = []
        processed_text = text
        
        # 遍历所有术语类别
        for category, terms in self.terminology_db.items():
            for term_zh, translations in terms.items():
                if source_lang == "zh" and term_zh in processed_text:
                    # 用占位符替换术语
                    placeholder = f"[TERM_{len(self.applied_terms)}]"
                    processed_text = processed_text.replace(term_zh, placeholder)
                    self.applied_terms.append({
                        "original": term_zh,
                        "placeholder": placeholder,
                        "translations": translations
                    })
        
        return processed_text
    
    def postprocess_text(self, text: str, target_lang: str) -> str:
        """翻译后术语后处理"""
        processed_text = text
        
        # 替换占位符为目标语言术语
        for term_info in self.applied_terms:
            placeholder = term_info["placeholder"]
            if placeholder in processed_text:
                target_term = term_info["translations"].get(target_lang, term_info["original"])
                processed_text = processed_text.replace(placeholder, target_term)
        
        return processed_text
    
    def get_applied_terms(self) -> List[str]:
        """获取已应用的术语列表"""
        return [term["original"] for term in self.applied_terms]
    
    def calculate_terminology_confidence(
        self,
        source_text: str,
        translated_text: str,
        source_lang: str,
        target_lang: str
    ) -> float:
        """计算术语翻译置信度"""
        try:
            total_terms = 0
            correct_terms = 0
            
            # 检查术语翻译正确性
            for category, terms in self.terminology_db.items():
                for term_zh, translations in terms.items():
                    if source_lang == "zh" and term_zh in source_text:
                        total_terms += 1
                        target_term = translations.get(target_lang, "")
                        if target_term and target_term.lower() in translated_text.lower():
                            correct_terms += 1
            
            if total_terms == 0:
                return 1.0  # 没有术语时返回满分
            
            return correct_terms / total_terms
            
        except Exception:
            return 0.5  # 默认置信度
    
    def add_custom_term(
        self,
        category: str,
        term_zh: str,
        translations: Dict[str, str]
    ):
        """添加自定义术语"""
        if category not in self.terminology_db:
            self.terminology_db[category] = {}
        
        self.terminology_db[category][term_zh] = translations
        self.save_terminology()
    
    def save_terminology(self):
        """保存术语库"""
        try:
            with open(self.terminology_file, 'w', encoding='utf-8') as f:
                json.dump(self.terminology_db, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"术语库保存失败: {e}")
```

## 🔄 翻译引擎管理器

### 多引擎协调系统
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译引擎管理器

协调多个翻译引擎，提供负载均衡、故障转移和质量优化功能。
"""

import asyncio
import time
from typing import Dict, List, Optional
from enum import Enum
import logging

class EngineType(Enum):
    """引擎类型枚举"""
    GEMMA3 = "gemma3"
    QWEN3 = "qwen3"
    LLAMA3 = "llama3"

class TranslationEngineManager:
    """翻译引擎管理器"""
    
    def __init__(self):
        self.engines = {}
        self.engine_stats = {}
        self.load_balancer = LoadBalancer()
        self.quality_monitor = QualityMonitor()
    
    def register_engine(self, engine_type: EngineType, engine_instance):
        """注册翻译引擎"""
        self.engines[engine_type] = engine_instance
        self.engine_stats[engine_type] = {
            "total_requests": 0,
            "successful_requests": 0,
            "average_time": 0.0,
            "average_quality": 0.0,
            "last_used": None
        }
        logger.info(f"翻译引擎已注册: {engine_type.value}")
    
    def translate_with_fallback(
        self,
        request: TranslationRequest,
        preferred_engine: Optional[EngineType] = None
    ) -> TranslationResult:
        """带故障转移的翻译"""
        
        # 选择引擎
        if preferred_engine and preferred_engine in self.engines:
            engine_order = [preferred_engine] + [e for e in self.engines.keys() if e != preferred_engine]
        else:
            engine_order = self.load_balancer.get_optimal_engine_order(
                self.engines.keys(),
                self.engine_stats
            )
        
        last_error = None
        
        # 尝试每个引擎
        for engine_type in engine_order:
            try:
                engine = self.engines[engine_type]
                start_time = time.time()
                
                result = engine.translate(request)
                
                # 更新统计信息
                self._update_engine_stats(engine_type, start_time, result, success=True)
                
                return result
                
            except Exception as e:
                logger.warning(f"引擎 {engine_type.value} 翻译失败: {e}")
                self._update_engine_stats(engine_type, start_time, None, success=False)
                last_error = e
                continue
        
        # 所有引擎都失败
        raise TranslationError(f"所有翻译引擎都失败，最后错误: {last_error}")
    
    async def batch_translate_optimized(
        self,
        requests: List[TranslationRequest]
    ) -> List[TranslationResult]:
        """优化的批量翻译"""
        
        # 根据引擎性能分配任务
        engine_assignments = self.load_balancer.distribute_tasks(
            requests,
            self.engines.keys(),
            self.engine_stats
        )
        
        # 并发执行
        tasks = []
        for engine_type, assigned_requests in engine_assignments.items():
            if assigned_requests:
                task = self._batch_translate_with_engine(engine_type, assigned_requests)
                tasks.append(task)
        
        # 等待所有任务完成
        results_groups = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并结果
        all_results = []
        for results_group in results_groups:
            if isinstance(results_group, list):
                all_results.extend(results_group)
            else:
                logger.error(f"批量翻译任务失败: {results_group}")
        
        return all_results
    
    async def _batch_translate_with_engine(
        self,
        engine_type: EngineType,
        requests: List[TranslationRequest]
    ) -> List[TranslationResult]:
        """使用指定引擎进行批量翻译"""
        engine = self.engines[engine_type]
        return engine.batch_translate(requests)
    
    def _update_engine_stats(
        self,
        engine_type: EngineType,
        start_time: float,
        result: Optional[TranslationResult],
        success: bool
    ):
        """更新引擎统计信息"""
        stats = self.engine_stats[engine_type]
        
        stats["total_requests"] += 1
        stats["last_used"] = time.time()
        
        if success and result:
            stats["successful_requests"] += 1
            
            # 更新平均处理时间
            processing_time = time.time() - start_time
            if stats["average_time"] == 0:
                stats["average_time"] = processing_time
            else:
                stats["average_time"] = (stats["average_time"] + processing_time) / 2
            
            # 更新平均质量
            if stats["average_quality"] == 0:
                stats["average_quality"] = result.confidence_score
            else:
                stats["average_quality"] = (stats["average_quality"] + result.confidence_score) / 2
    
    def get_engine_status(self) -> Dict:
        """获取引擎状态"""
        status = {}
        for engine_type, stats in self.engine_stats.items():
            success_rate = (
                stats["successful_requests"] / stats["total_requests"]
                if stats["total_requests"] > 0 else 0
            )
            
            status[engine_type.value] = {
                "total_requests": stats["total_requests"],
                "success_rate": success_rate,
                "average_time": stats["average_time"],
                "average_quality": stats["average_quality"],
                "last_used": stats["last_used"]
            }
        
        return status

class LoadBalancer:
    """负载均衡器"""
    
    def get_optimal_engine_order(
        self,
        available_engines: List[EngineType],
        engine_stats: Dict
    ) -> List[EngineType]:
        """获取最优引擎顺序"""
        
        # 计算每个引擎的综合评分
        engine_scores = {}
        for engine_type in available_engines:
            stats = engine_stats[engine_type]
            
            # 成功率权重
            success_rate = (
                stats["successful_requests"] / stats["total_requests"]
                if stats["total_requests"] > 0 else 1.0
            )
            
            # 速度权重（时间越短分数越高）
            speed_score = 1.0 / (stats["average_time"] + 0.1)
            
            # 质量权重
            quality_score = stats["average_quality"]
            
            # 综合评分
            total_score = (success_rate * 0.4 + speed_score * 0.3 + quality_score * 0.3)
            engine_scores[engine_type] = total_score
        
        # 按评分排序
        sorted_engines = sorted(
            available_engines,
            key=lambda x: engine_scores[x],
            reverse=True
        )
        
        return sorted_engines
    
    def distribute_tasks(
        self,
        requests: List[TranslationRequest],
        available_engines: List[EngineType],
        engine_stats: Dict
    ) -> Dict[EngineType, List[TranslationRequest]]:
        """分配任务到不同引擎"""
        
        # 简单的轮询分配策略
        assignments = {engine: [] for engine in available_engines}
        
        for i, request in enumerate(requests):
            engine_index = i % len(available_engines)
            engine = list(available_engines)[engine_index]
            assignments[engine].append(request)
        
        return assignments

class QualityMonitor:
    """质量监控器"""
    
    def __init__(self):
        self.quality_history = []
    
    def record_quality(self, result: TranslationResult):
        """记录翻译质量"""
        self.quality_history.append({
            "timestamp": time.time(),
            "confidence": result.confidence_score,
            "model": result.model_used,
            "processing_time": result.processing_time
        })
        
        # 保持历史记录在合理范围内
        if len(self.quality_history) > 1000:
            self.quality_history = self.quality_history[-1000:]
    
    def get_quality_report(self) -> Dict:
        """获取质量报告"""
        if not self.quality_history:
            return {}
        
        recent_records = self.quality_history[-100:]  # 最近100条记录
        
        avg_confidence = sum(r["confidence"] for r in recent_records) / len(recent_records)
        avg_time = sum(r["processing_time"] for r in recent_records) / len(recent_records)
        
        return {
            "average_confidence": avg_confidence,
            "average_processing_time": avg_time,
            "total_translations": len(self.quality_history),
            "recent_translations": len(recent_records)
        }
```

这个翻译引擎知识文档提供了完整的本地LLM翻译解决方案，包括Gemma3和Qwen3的具体实现、术语管理系统和多引擎协调机制，为Voice-came全栈开发工程师提供了详细的技术指导。 