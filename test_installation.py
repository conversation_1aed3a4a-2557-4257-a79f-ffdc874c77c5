#!/usr/bin/env python3
"""
Voice-came 系统环境验证脚本

这个脚本会测试所有核心依赖是否正确安装
"""

import sys
import platform
from pathlib import Path

def print_banner():
    """打印测试横幅"""
    print("=" * 60)
    print("  Voice-came 语音翻译系统 - 环境验证")
    print("=" * 60)
    print()

def test_python_version():
    """测试Python版本"""
    print("检查Python环境...")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if 3.8 <= version[:2] <= (3, 11):
        print("✅ Python版本符合要求 (3.8-3.11)")
        return True
    else:
        print("❌ Python版本不符合要求，需要3.8-3.11")
        return False

def test_system_info():
    """显示系统信息"""
    print("\n系统信息...")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python路径: {sys.executable}")
    return True

def test_basic_imports():
    """测试基础模块导入"""
    print("\n测试基础Python模块...")
    
    basic_modules = ['os', 'sys', 'pathlib', 'json', 'subprocess']
    
    success = True
    for module in basic_modules:
        try:
            __import__(module)
            print(f"✅ {module}: OK")
        except ImportError as e:
            print(f"❌ {module}: 导入失败 - {e}")
            success = False
    
    return success

def test_core_dependencies():
    """测试核心依赖"""
    print("\n测试核心AI依赖...")
    
    # PyTorch
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            print(f"✅ CUDA可用: {device_count}个设备")
        else:
            print("ℹ️  CUDA不可用，将使用CPU")
        
        pytorch_success = True
    except ImportError as e:
        print(f"❌ PyTorch: 未安装")
        pytorch_success = False
    
    # Transformers
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
        transformers_success = True
    except ImportError as e:
        print(f"❌ Transformers: 未安装")
        transformers_success = False
    
    return pytorch_success and transformers_success

def test_audio_libraries():
    """测试音频处理库"""
    print("\n测试音频处理库...")
    
    audio_libs = ['librosa', 'soundfile', 'torchaudio']
    
    success = True
    for lib in audio_libs:
        try:
            module = __import__(lib)
            if hasattr(module, '__version__'):
                print(f"✅ {lib}: v{module.__version__}")
            else:
                print(f"✅ {lib}: OK")
        except ImportError as e:
            print(f"❌ {lib}: 未安装")
            success = False
    
    return success

def test_whisperx():
    """测试WhisperX"""
    print("\n测试WhisperX...")
    
    try:
        import whisperx
        print("✅ WhisperX: 导入成功")
        return True
    except ImportError as e:
        print(f"❌ WhisperX: 未安装")
        return False

def test_project_structure():
    """测试项目结构"""
    print("\n检查项目结构...")
    
    required_dirs = [
        'src', 'src/voice_came', 'tests', 'docs', 'configs', 'scripts'
    ]
    
    required_files = [
        'README.md', 'LICENSE', 'requirements.txt', 'setup.py'
    ]
    
    # 检查目录
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    # 检查文件
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_dirs or missing_files:
        if missing_dirs:
            print(f"❌ 缺少目录: {', '.join(missing_dirs)}")
        if missing_files:
            print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("✅ 项目结构完整")
        return True

def test_voice_came_imports():
    """测试Voice-came模块导入"""
    print("\n测试Voice-came模块...")
    
    try:
        # 测试主模块导入
        sys.path.insert(0, 'src')
        import voice_came
        print("✅ voice_came主模块导入成功")
        
        # 测试子模块导入
        from voice_came import speech_recognition
        from voice_came import translation  
        from voice_came import speech_synthesis
        from voice_came import utils
        
        print("✅ 所有voice_came子模块导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ Voice-came模块导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print_banner()
    
    # 运行所有测试
    results = []
    
    results.append(test_python_version())
    results.append(test_system_info())
    results.append(test_basic_imports())
    results.append(test_core_dependencies())
    results.append(test_audio_libraries())
    results.append(test_whisperx())
    results.append(test_project_structure())
    results.append(test_voice_came_imports())
    
    # 生成报告
    print("\n" + "=" * 60)
    print("  测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results if result)
    
    print(f"\n总测试项: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！Voice-came环境搭建成功！")
        return True
    else:
        print("\n⚠️  部分测试失败，请检查安装")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1) 