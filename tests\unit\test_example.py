#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例模块全面单元测试

测试example.py中的所有函数和类，确保100%覆盖率
"""

import sys
import os
import pytest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from voice_came.example import add, multiply, divide, Calculator


class TestAddFunction:
    """测试add函数"""
    
    def test_add_positive_numbers(self):
        """测试正数相加"""
        assert add(2, 3) == 5
        assert add(10, 15) == 25
        assert add(100, 200) == 300
    
    def test_add_negative_numbers(self):
        """测试负数相加"""
        assert add(-2, -3) == -5
        assert add(-10, -15) == -25
    
    def test_add_mixed_numbers(self):
        """测试正负数混合相加"""
        assert add(5, -3) == 2
        assert add(-5, 3) == -2
        assert add(10, -10) == 0
    
    def test_add_zero(self):
        """测试零相加"""
        assert add(0, 0) == 0
        assert add(5, 0) == 5
        assert add(0, 5) == 5
    
    def test_add_large_numbers(self):
        """测试大数相加"""
        assert add(1000000, 2000000) == 3000000
        assert add(-1000000, -2000000) == -3000000


class TestMultiplyFunction:
    """测试multiply函数"""
    
    def test_multiply_positive_numbers(self):
        """测试正数相乘"""
        assert multiply(2, 3) == 6
        assert multiply(5, 4) == 20
        assert multiply(10, 10) == 100
    
    def test_multiply_negative_numbers(self):
        """测试负数相乘"""
        assert multiply(-2, -3) == 6
        assert multiply(-5, -4) == 20
    
    def test_multiply_mixed_numbers(self):
        """测试正负数混合相乘"""
        assert multiply(5, -3) == -15
        assert multiply(-5, 3) == -15
    
    def test_multiply_by_zero(self):
        """测试乘以零"""
        assert multiply(5, 0) == 0
        assert multiply(0, 5) == 0
        assert multiply(0, 0) == 0
        assert multiply(-5, 0) == 0
    
    def test_multiply_by_one(self):
        """测试乘以一"""
        assert multiply(5, 1) == 5
        assert multiply(1, 5) == 5
        assert multiply(-5, 1) == -5
        assert multiply(1, -5) == -5


class TestDivideFunction:
    """测试divide函数"""
    
    def test_divide_positive_numbers(self):
        """测试正数相除"""
        assert divide(6, 2) == 3.0
        assert divide(10, 5) == 2.0
        assert divide(15, 3) == 5.0
    
    def test_divide_negative_numbers(self):
        """测试负数相除"""
        assert divide(-6, -2) == 3.0
        assert divide(-10, -5) == 2.0
    
    def test_divide_mixed_numbers(self):
        """测试正负数混合相除"""
        assert divide(6, -2) == -3.0
        assert divide(-6, 2) == -3.0
    
    def test_divide_result_float(self):
        """测试除法结果为小数"""
        assert divide(5, 2) == 2.5
        assert divide(7, 4) == 1.75
        assert divide(1, 3) == pytest.approx(0.3333333333333333)
    
    def test_divide_by_zero_raises_error(self):
        """测试除零错误"""
        with pytest.raises(ValueError) as exc_info:
            divide(5, 0)
        
        assert str(exc_info.value) == "Cannot divide by zero"
        
        with pytest.raises(ValueError) as exc_info:
            divide(-5, 0)
        
        assert str(exc_info.value) == "Cannot divide by zero"
        
        with pytest.raises(ValueError) as exc_info:
            divide(0, 0)
        
        assert str(exc_info.value) == "Cannot divide by zero"
    
    def test_divide_zero_by_number(self):
        """测试零除以数字"""
        assert divide(0, 5) == 0.0
        assert divide(0, -5) == 0.0
        assert divide(0, 1) == 0.0


class TestCalculatorClass:
    """测试Calculator类"""
    
    def test_calculator_initialization(self):
        """测试计算器初始化"""
        calc = Calculator()
        assert calc.history == []
        assert isinstance(calc.history, list)
    
    def test_calculator_add_operation(self):
        """测试计算器加法操作"""
        calc = Calculator()
        result = calc.calculate("add", 2, 3)
        
        assert result == 5
        assert len(calc.history) == 1
        assert calc.history[0] == "2 add 3 = 5"
    
    def test_calculator_multiply_operation(self):
        """测试计算器乘法操作"""
        calc = Calculator()
        result = calc.calculate("multiply", 4, 5)
        
        assert result == 20
        assert len(calc.history) == 1
        assert calc.history[0] == "4 multiply 5 = 20"
    
    def test_calculator_divide_operation(self):
        """测试计算器除法操作"""
        calc = Calculator()
        result = calc.calculate("divide", 10, 2)
        
        assert result == 5.0
        assert len(calc.history) == 1
        assert calc.history[0] == "10 divide 2 = 5.0"
    
    def test_calculator_divide_by_zero_in_calculate(self):
        """测试计算器中的除零错误"""
        calc = Calculator()
        
        with pytest.raises(ValueError) as exc_info:
            calc.calculate("divide", 5, 0)
        
        assert str(exc_info.value) == "Cannot divide by zero"
        # 错误时历史记录不应该添加
        assert len(calc.history) == 0
    
    def test_calculator_unknown_operation(self):
        """测试计算器未知操作"""
        calc = Calculator()
        
        with pytest.raises(ValueError) as exc_info:
            calc.calculate("subtract", 5, 3)
        
        assert str(exc_info.value) == "Unknown operation: subtract"
        
        with pytest.raises(ValueError) as exc_info:
            calc.calculate("invalid", 1, 1)
        
        assert str(exc_info.value) == "Unknown operation: invalid"
        
        # 错误时历史记录不应该添加
        assert len(calc.history) == 0
    
    def test_calculator_multiple_operations(self):
        """测试计算器多次操作"""
        calc = Calculator()
        
        # 执行多个操作
        result1 = calc.calculate("add", 2, 3)
        result2 = calc.calculate("multiply", 4, 5)
        result3 = calc.calculate("divide", 10, 2)
        
        assert result1 == 5
        assert result2 == 20
        assert result3 == 5.0
        
        # 检查历史记录
        assert len(calc.history) == 3
        assert calc.history[0] == "2 add 3 = 5"
        assert calc.history[1] == "4 multiply 5 = 20"
        assert calc.history[2] == "10 divide 2 = 5.0"
    
    def test_get_history_returns_copy(self):
        """测试get_history返回副本"""
        calc = Calculator()
        
        # 执行一些操作
        calc.calculate("add", 1, 2)
        calc.calculate("multiply", 3, 4)
        
        # 获取历史记录
        history1 = calc.get_history()
        history2 = calc.get_history()
        
        # 应该是不同的对象（副本）
        assert history1 is not history2
        assert history1 is not calc.history
        
        # 但内容应该相同
        assert history1 == history2
        assert history1 == calc.history
        
        # 修改返回的历史记录不应该影响原始记录
        history1.append("fake entry")
        assert len(calc.history) == 2
        assert len(history1) == 3
        assert len(calc.get_history()) == 2
    
    def test_calculator_empty_history(self):
        """测试空历史记录"""
        calc = Calculator()
        history = calc.get_history()
        
        assert history == []
        assert len(history) == 0
        assert isinstance(history, list)
    
    def test_calculator_edge_cases(self):
        """测试计算器边界情况"""
        calc = Calculator()
        
        # 测试零值计算
        result1 = calc.calculate("add", 0, 0)
        result2 = calc.calculate("multiply", 0, 5)
        result3 = calc.calculate("divide", 0, 5)
        
        assert result1 == 0
        assert result2 == 0
        assert result3 == 0.0
        
        assert len(calc.history) == 3
        assert calc.history[0] == "0 add 0 = 0"
        assert calc.history[1] == "0 multiply 5 = 0"
        assert calc.history[2] == "0 divide 5 = 0.0"
    
    def test_calculator_negative_numbers(self):
        """测试计算器负数操作"""
        calc = Calculator()
        
        result1 = calc.calculate("add", -5, -3)
        result2 = calc.calculate("multiply", -4, 2)
        result3 = calc.calculate("divide", -10, -2)
        
        assert result1 == -8
        assert result2 == -8
        assert result3 == 5.0
        
        assert len(calc.history) == 3
        assert calc.history[0] == "-5 add -3 = -8"
        assert calc.history[1] == "-4 multiply 2 = -8"
        assert calc.history[2] == "-10 divide -2 = 5.0"


class TestModuleIntegration:
    """测试模块集成"""
    
    def test_all_functions_accessible(self):
        """测试所有函数都可以访问"""
        # 测试函数可以直接调用
        assert callable(add)
        assert callable(multiply)
        assert callable(divide)
        
        # 测试类可以实例化
        calc = Calculator()
        assert isinstance(calc, Calculator)
    
    def test_function_type_hints(self):
        """测试函数类型提示"""
        # 这些测试主要验证函数可以正常工作
        # 在真实项目中，可以使用mypy等工具验证类型
        
        result_add = add(1, 2)
        assert isinstance(result_add, int)
        
        result_multiply = multiply(2, 3)
        assert isinstance(result_multiply, int)
        
        result_divide = divide(6, 2)
        assert isinstance(result_divide, float)
    
    def test_calculator_integration_with_functions(self):
        """测试计算器与独立函数的集成"""
        calc = Calculator()
        
        # 直接调用函数
        direct_add = add(3, 4)
        direct_multiply = multiply(5, 6)
        direct_divide = divide(12, 3)
        
        # 通过计算器调用
        calc_add = calc.calculate("add", 3, 4)
        calc_multiply = calc.calculate("multiply", 5, 6)
        calc_divide = calc.calculate("divide", 12, 3)
        
        # 结果应该相同
        assert direct_add == calc_add
        assert direct_multiply == calc_multiply
        assert direct_divide == calc_divide


class TestModuleDocstring:
    """测试模块文档字符串"""
    
    def test_module_has_docstring(self):
        """测试模块有文档字符串"""
        import voice_came.example as example_module
        
        assert example_module.__doc__ is not None
        assert "示例模块" in example_module.__doc__
        assert "测试覆盖率" in example_module.__doc__


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 