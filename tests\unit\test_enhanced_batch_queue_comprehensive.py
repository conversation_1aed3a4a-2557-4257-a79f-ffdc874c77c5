#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
enhanced_batch_queue.py 全面测试覆盖

专门为enhanced_batch_queue.py中未覆盖的功能编写测试用例
目标：将覆盖率从15.27%提升至90%+

重点测试模块：
1. QueueJob类的高级功能
2. QueueMetrics类的完整功能  
3. PerformanceMonitor类
4. ErrorRecoveryManager类
5. EnhancedBatchQueue的核心方法
"""

import pytest
import time
import json
import threading
import tempfile
import statistics
import gc
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from collections import deque

from voice_came.core.enhanced_batch_queue import (
    QueueJob, JobState, JobPriority,
    QueueMetrics, PerformanceMonitor, ErrorRecoveryManager,
    EnhancedBatchQueue, QueueManager
)


class TestQueueJobAdvanced:
    """QueueJob类高级功能测试"""
    
    @pytest.fixture
    def sample_job(self, tmp_path):
        """创建示例任务"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        return QueueJob(
            id="test_job",
            file_path=test_file,
            priority=JobPriority.NORMAL,
            max_retries=3,
            retry_delay=1.0,
            estimated_duration=30.0
        )
    
    def test_job_initialization_with_kwargs(self, tmp_path):
        """测试任务初始化包含kwargs"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob(
            id="kwargs_test",
            file_path=test_file,
            priority=JobPriority.HIGH,
            custom_param="test_value",
            another_param=123
        )
        
        assert job.id == "kwargs_test"
        assert job.priority == JobPriority.HIGH
        assert hasattr(job, 'custom_param')
        assert job.custom_param == "test_value"
        assert hasattr(job, 'another_param')
        assert job.another_param == 123
    
    def test_job_priority_comparison(self, tmp_path):
        """测试任务优先级比较（__lt__方法）"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        
        # 创建不同优先级的任务
        high_job = QueueJob("high", test_file, priority=JobPriority.HIGH)
        normal_job = QueueJob("normal", test_file, priority=JobPriority.NORMAL)
        low_job = QueueJob("low", test_file, priority=JobPriority.LOW)
        
        # 测试优先级比较
        assert high_job < normal_job
        assert normal_job < low_job
        assert high_job < low_job
        
        # 相同优先级按创建时间排序
        time.sleep(0.001)  # 确保不同的创建时间
        same_priority_job = QueueJob("same", test_file, priority=JobPriority.HIGH)
        assert high_job < same_priority_job
    
    def test_job_can_retry(self, sample_job):
        """测试任务重试判断逻辑"""
        # 初始状态可以重试
        assert sample_job.can_retry() is True
        
        # 增加重试次数
        sample_job.retry_count = 1
        assert sample_job.can_retry() is True
        
        sample_job.retry_count = 2
        assert sample_job.can_retry() is True
        
        # 达到最大重试次数
        sample_job.retry_count = 3
        assert sample_job.can_retry() is False
        
        # 超过最大重试次数
        sample_job.retry_count = 4
        assert sample_job.can_retry() is False
    
    def test_job_reset_for_retry(self, sample_job):
        """测试任务重试重置逻辑"""
        # 设置初始状态
        sample_job.status = JobState.FAILED
        sample_job.started_at = time.time()
        sample_job.progress = 50.0
        sample_job.error_message = "Test error"
        sample_job.retry_count = 1
        
        # 重置重试
        sample_job.reset_for_retry()
        
        assert sample_job.status == JobState.RETRYING
        assert sample_job.retry_count == 2
        assert sample_job.started_at is None
        assert sample_job.progress == 0.0
        assert sample_job.error_message == ""
    
    def test_job_estimated_remaining_time(self, sample_job):
        """测试任务估计剩余时间计算"""
        # 初始状态（未开始）
        remaining = sample_job.get_estimated_remaining_time()
        assert remaining == 30.0  # estimated_duration
        
        # 设置开始时间和进度
        sample_job.started_at = time.time()
        sample_job.progress = 0.0
        remaining = sample_job.get_estimated_remaining_time()
        assert remaining == 30.0
        
        # 50%进度
        sample_job.progress = 50.0
        time.sleep(0.1)
        remaining = sample_job.get_estimated_remaining_time()
        assert remaining > 0
        assert remaining < 30.0
        
        # 100%进度
        sample_job.progress = 100.0
        remaining = sample_job.get_estimated_remaining_time()
        assert remaining == 0


class TestQueueMetrics:
    """QueueMetrics类测试"""
    
    @pytest.fixture
    def sample_metrics(self):
        """创建示例指标"""
        return QueueMetrics()
    
    @pytest.fixture
    def sample_jobs_dict(self, tmp_path):
        """创建示例任务字典"""
        jobs = {}
        for i, status in enumerate([JobState.PENDING, JobState.RUNNING, 
                                   JobState.COMPLETED, JobState.FAILED, 
                                   JobState.CANCELLED, JobState.RETRYING]):
            test_file = tmp_path / f"test_{i}.mp4"
            test_file.write_bytes(b"test data")
            job = QueueJob(f"job_{i}", test_file)
            job.status = status
            if status == JobState.COMPLETED:
                job.processing_time = 10.0 + i
            jobs[f"job_{i}"] = job
        return jobs
    
    def test_metrics_initialization(self, sample_metrics):
        """测试指标初始化"""
        assert sample_metrics.total_jobs == 0
        assert sample_metrics.pending_jobs == 0
        assert sample_metrics.running_jobs == 0
        assert sample_metrics.completed_jobs == 0
        assert sample_metrics.failed_jobs == 0
        assert sample_metrics.cancelled_jobs == 0
        assert sample_metrics.retrying_jobs == 0
        assert sample_metrics.average_processing_time == 0.0
        assert sample_metrics.success_rate == 0.0
    
    def test_metrics_update_from_jobs(self, sample_metrics, sample_jobs_dict):
        """测试从任务字典更新指标"""
        # 设置队列开始时间
        sample_metrics.queue_start_time = time.time() - 3600  # 1小时前
        
        sample_metrics.update_from_jobs(sample_jobs_dict)
        
        assert sample_metrics.total_jobs == 6
        assert sample_metrics.pending_jobs == 1
        assert sample_metrics.running_jobs == 1
        assert sample_metrics.completed_jobs == 1
        assert sample_metrics.failed_jobs == 1
        assert sample_metrics.cancelled_jobs == 1
        assert sample_metrics.retrying_jobs == 1
        
        # 测试成功率计算
        # 完成任务数 / (完成 + 失败 + 取消) = 1 / (1+1+1) = 1/3
        assert abs(sample_metrics.success_rate - (1/3)) < 0.01
        
        # 测试平均处理时间
        assert sample_metrics.average_processing_time == 12.0  # job_2的处理时间
        
        # 测试吞吐量
        assert sample_metrics.throughput_per_hour == 1.0  # 1个完成任务/小时
        
        # 测试时间戳更新
        assert sample_metrics.last_update_time > 0


class TestPerformanceMonitor:
    """PerformanceMonitor类测试"""
    
    @pytest.fixture
    def monitor(self):
        """创建性能监控器"""
        return PerformanceMonitor(max_history_size=100)
    
    @pytest.fixture
    def completed_job(self, tmp_path):
        """创建已完成的任务"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("completed_job", test_file)
        job.status = JobState.COMPLETED
        job.processing_time = 15.5
        job.memory_usage = 128.0
        return job
    
    def test_monitor_initialization(self, monitor):
        """测试监控器初始化"""
        assert monitor.max_history_size == 100
        assert len(monitor.performance_history) == 0
        assert len(monitor.memory_samples) == 0
        assert monitor.start_time > 0
    
    def test_record_job_completion(self, monitor, completed_job):
        """测试记录任务完成"""
        monitor.record_job_completion(completed_job)
        
        assert len(monitor.performance_history) == 1
        
        record = monitor.performance_history[0]
        assert record['job_id'] == "completed_job"
        assert record['processing_time'] == 15.5
        assert record['memory_usage'] == 128.0
        assert record['timestamp'] > 0
    
    def test_record_memory_usage(self, monitor):
        """测试记录内存使用"""
        with patch('psutil.virtual_memory') as mock_memory:
            mock_memory.return_value.percent = 75.5
            
            monitor.record_memory_usage()
            
            assert len(monitor.memory_samples) == 1
            assert monitor.memory_samples[0]['usage_percent'] == 75.5
            assert monitor.memory_samples[0]['timestamp'] > 0
    
    def test_performance_summary(self, monitor, tmp_path):
        """测试性能摘要生成"""
        # 添加多个任务记录
        for i in range(5):
            test_file = tmp_path / f"test_{i}.mp4"
            test_file.write_bytes(b"test data")
            job = QueueJob(f"job_{i}", test_file)
            job.processing_time = 10.0 + i * 2
            job.memory_usage = 100.0 + i * 10
            monitor.record_job_completion(job)
        
        # 添加内存记录
        with patch('psutil.virtual_memory') as mock_memory:
            mock_memory.return_value.percent = 80.0
            monitor.record_memory_usage()
        
        summary = monitor.get_performance_summary()
        
        assert summary['total_jobs_processed'] == 5
        assert summary['average_processing_time'] == 14.0  # (10+12+14+16+18)/5
        assert summary['median_processing_time'] == 14.0
        assert summary['average_memory_usage'] == 120.0  # (100+110+120+130+140)/5
        assert summary['peak_memory_usage'] == 140.0
        assert summary['current_memory_percent'] == 80.0
        assert summary['uptime_hours'] > 0
        assert summary['jobs_per_hour'] > 0


class TestErrorRecoveryManager:
    """ErrorRecoveryManager类测试"""
    
    @pytest.fixture
    def recovery_manager(self):
        """创建错误恢复管理器"""
        return ErrorRecoveryManager()
    
    @pytest.fixture
    def sample_job(self, tmp_path):
        """创建示例任务"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        return QueueJob("error_job", test_file)
    
    def test_manager_initialization(self, recovery_manager):
        """测试管理器初始化"""
        assert len(recovery_manager.error_handlers) > 0
        assert len(recovery_manager.error_counts) == 0
        assert len(recovery_manager.recovery_stats) == 0
    
    def test_handle_file_not_found_error(self, recovery_manager, sample_job):
        """测试处理文件不存在错误"""
        error = FileNotFoundError("File not found")
        
        result = recovery_manager.handle_job_error(sample_job, error)
        
        assert result is False  # 文件不存在不应该重试
        assert sample_job.error_message == "文件不存在，无法处理"
        assert "FileNotFoundError" in recovery_manager.error_counts
        assert recovery_manager.error_counts["FileNotFoundError"] == 1
    
    def test_handle_permission_error(self, recovery_manager, sample_job):
        """测试处理权限错误"""
        error = PermissionError("Permission denied")
        
        result = recovery_manager.handle_job_error(sample_job, error)
        
        assert result is False  # 权限错误不应该重试
        assert "权限不足" in sample_job.error_message
        assert "PermissionError" in recovery_manager.error_counts
    
    def test_handle_memory_error(self, recovery_manager, sample_job):
        """测试处理内存错误"""
        error = MemoryError("Out of memory")
        
        result = recovery_manager.handle_job_error(sample_job, error)
        
        assert result is True  # 内存错误应该重试
        assert "内存不足" in sample_job.error_message
        assert "MemoryError" in recovery_manager.error_counts
    
    def test_handle_timeout_error(self, recovery_manager, sample_job):
        """测试处理超时错误"""
        error = TimeoutError("Operation timed out")
        
        result = recovery_manager.handle_job_error(sample_job, error)
        
        assert result is True  # 超时错误应该重试
        assert "处理超时" in sample_job.error_message
        assert "TimeoutError" in recovery_manager.error_counts
    
    def test_handle_default_error(self, recovery_manager, sample_job):
        """测试处理默认错误"""
        error = RuntimeError("Unknown error")
        
        result = recovery_manager.handle_job_error(sample_job, error)
        
        assert result is True  # 默认错误应该重试
        assert "Unknown error" in sample_job.error_message
        assert "RuntimeError" in recovery_manager.error_counts
    
    def test_get_error_statistics(self, recovery_manager, sample_job, tmp_path):
        """测试获取错误统计"""
        # 模拟多种错误
        errors = [
            FileNotFoundError("File not found"),
            MemoryError("Out of memory"),
            MemoryError("Out of memory again"),
            TimeoutError("Timeout"),
        ]
        
        for error in errors:
            test_file = tmp_path / f"test_{len(recovery_manager.error_counts)}.mp4"
            test_file.write_bytes(b"test data")
            job = QueueJob(f"job_{len(recovery_manager.error_counts)}", test_file)
            recovery_manager.handle_job_error(job, error)
        
        stats = recovery_manager.get_error_statistics()
        
        assert stats['total_errors'] == 4
        assert stats['error_types']['FileNotFoundError'] == 1
        assert stats['error_types']['MemoryError'] == 2
        assert stats['error_types']['TimeoutError'] == 1
        assert stats['recovery_attempts'] >= 0
        assert stats['successful_recoveries'] >= 0


class TestEnhancedBatchQueueCore:
    """EnhancedBatchQueue核心功能测试"""
    
    @pytest.fixture
    def enhanced_queue(self, tmp_path):
        """创建增强队列"""
        state_file = tmp_path / "test_queue.json"
        return EnhancedBatchQueue(
            max_concurrent=3,
            state_file=state_file,
            enable_monitoring=True,
            cleanup_interval=10
        )
    
    @pytest.fixture
    def test_jobs(self, tmp_path):
        """创建测试任务"""
        jobs = []
        for i in range(5):
            test_file = tmp_path / f"test_{i}.mp4"
            test_file.write_bytes(b"test data" * 100)
            job = QueueJob(f"job_{i}", test_file, priority=i % 3)
            jobs.append(job)
        return jobs
    
    def test_queue_initialization(self, enhanced_queue):
        """测试队列初始化"""
        assert enhanced_queue.max_concurrent == 3
        assert enhanced_queue.state_file is not None
        assert enhanced_queue.enable_monitoring is True
        assert enhanced_queue.cleanup_interval == 10
        assert enhanced_queue._running is False
        assert enhanced_queue._paused is False
        assert len(enhanced_queue._jobs) == 0
        assert len(enhanced_queue._pending_queue) == 0
        assert len(enhanced_queue._running_jobs) == 0
    
    def test_add_job_with_priority_queue(self, enhanced_queue, test_jobs):
        """测试添加任务到优先级队列"""
        for job in test_jobs:
            job_id = enhanced_queue.add_job(job)
            assert job_id == job.id
        
        assert enhanced_queue.size() == 5
        assert len(enhanced_queue._pending_queue) == 5
        
        # 验证优先级队列排序
        queue_copy = list(enhanced_queue._pending_queue)
        queue_copy.sort()
        priorities = [job.priority for job in queue_copy]
        assert priorities == sorted(priorities)
    
    def test_remove_job_from_different_states(self, enhanced_queue, test_jobs):
        """测试从不同状态移除任务"""
        job = test_jobs[0]
        enhanced_queue.add_job(job)
        
        # 从pending状态移除
        removed = enhanced_queue.remove_job(job.id)
        assert removed.id == job.id
        assert enhanced_queue.size() == 0
        
        # 重新添加并开始处理
        enhanced_queue.add_job(job)
        enhanced_queue.start()
        time.sleep(0.1)
        
        # 从running状态移除
        if job.status == JobState.RUNNING:
            removed = enhanced_queue.remove_job(job.id)
            assert removed.id == job.id
        
        enhanced_queue.stop()
    
    def test_queue_metrics_updates(self, enhanced_queue, test_jobs):
        """测试队列指标更新"""
        # 添加任务
        for job in test_jobs:
            enhanced_queue.add_job(job)
        
        metrics = enhanced_queue.get_metrics()
        assert metrics.total_jobs == 5
        assert metrics.pending_jobs == 5
        assert metrics.running_jobs == 0
        assert metrics.completed_jobs == 0
        
        # 开始处理
        enhanced_queue.start()
        time.sleep(0.2)
        
        metrics = enhanced_queue.get_metrics()
        assert metrics.running_jobs > 0
        
        enhanced_queue.stop()
    
    def test_performance_summary(self, enhanced_queue, test_jobs):
        """测试性能摘要"""
        for job in test_jobs:
            enhanced_queue.add_job(job)
        
        enhanced_queue.start()
        time.sleep(0.5)
        enhanced_queue.stop()
        
        summary = enhanced_queue.get_performance_summary()
        assert 'queue_metrics' in summary
        assert 'performance_monitor' in summary
        assert 'memory_usage' in summary
        assert 'uptime' in summary
    
    def test_pause_and_resume(self, enhanced_queue, test_jobs):
        """测试暂停和恢复功能"""
        for job in test_jobs:
            enhanced_queue.add_job(job)
        
        enhanced_queue.start()
        assert enhanced_queue._running is True
        assert enhanced_queue._paused is False
        
        # 暂停
        enhanced_queue.pause()
        assert enhanced_queue._paused is True
        
        time.sleep(0.1)
        initial_running = enhanced_queue.get_running_count()
        
        # 恢复
        enhanced_queue.resume()
        assert enhanced_queue._paused is False
        
        time.sleep(0.1)
        after_resume_running = enhanced_queue.get_running_count()
        assert after_resume_running >= initial_running
        
        enhanced_queue.stop()
    
    def test_stop_with_completion_wait(self, enhanced_queue, test_jobs):
        """测试等待完成的停止"""
        for job in test_jobs[:2]:  # 少量任务
            enhanced_queue.add_job(job)
        
        enhanced_queue.start()
        time.sleep(0.1)
        
        # 等待完成的停止
        enhanced_queue.stop(wait_for_completion=True)
        
        assert enhanced_queue._running is False
        # 验证所有任务都完成了
        metrics = enhanced_queue.get_metrics()
        assert metrics.running_jobs == 0
    
    def test_state_persistence_advanced(self, enhanced_queue, test_jobs):
        """测试高级状态持久化"""
        # 添加任务并设置不同状态
        for i, job in enumerate(test_jobs):
            enhanced_queue.add_job(job)
            if i % 2 == 0:
                job.status = JobState.COMPLETED
                job.processing_time = 10.0 + i
        
        # 保存状态
        enhanced_queue.save_state()
        assert enhanced_queue.state_file.exists()
        
        # 验证保存的内容
        with open(enhanced_queue.state_file, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        assert 'jobs' in state_data
        assert 'metadata' in state_data
        assert len(state_data['jobs']) == 5
        assert state_data['metadata']['total_jobs'] == 5
        
        # 创建新队列并加载
        new_queue = EnhancedBatchQueue(
            state_file=enhanced_queue.state_file,
            enable_monitoring=True
        )
        
        assert new_queue.size() == 5
        
        # 验证任务状态正确加载
        for job_id, job_data in state_data['jobs'].items():
            loaded_job = new_queue._jobs[job_id]
            assert loaded_job.status == job_data['status']
    
    def test_cleanup_completed_jobs(self, enhanced_queue, test_jobs):
        """测试清理已完成任务"""
        # 添加任务并模拟完成
        for job in test_jobs:
            enhanced_queue.add_job(job)
            job.status = JobState.COMPLETED
            job.completed_at = time.time()
        
        assert enhanced_queue.size() == 5
        
        # 清理已完成任务
        enhanced_queue._cleanup_completed_jobs()
        
        # 验证清理效果（具体行为取决于实现）
        metrics = enhanced_queue.get_metrics()
        assert metrics.total_jobs <= 5  # 可能被清理
    
    def test_memory_usage_monitoring(self, enhanced_queue):
        """测试内存使用监控"""
        with patch.object(enhanced_queue, '_get_current_memory_usage', return_value=256.5):
            memory_usage = enhanced_queue._get_current_memory_usage()
            assert memory_usage == 256.5


class TestQueueManager:
    """QueueManager类测试"""
    
    @pytest.fixture
    def queue_manager(self):
        """创建队列管理器"""
        return QueueManager()
    
    def test_manager_initialization(self, queue_manager):
        """测试管理器初始化"""
        assert len(queue_manager.queues) == 0
    
    def test_create_queue(self, queue_manager):
        """测试创建队列"""
        queue = queue_manager.create_queue("test_queue", max_concurrent=5)
        
        assert queue is not None
        assert queue.max_concurrent == 5
        assert "test_queue" in queue_manager.queues
        assert queue_manager.queues["test_queue"] == queue
    
    def test_create_duplicate_queue_raises_error(self, queue_manager):
        """测试创建重复队列抛出错误"""
        queue_manager.create_queue("duplicate")
        
        with pytest.raises(ValueError, match="队列名称已存在"):
            queue_manager.create_queue("duplicate")
    
    def test_get_queue(self, queue_manager):
        """测试获取队列"""
        created_queue = queue_manager.create_queue("get_test")
        retrieved_queue = queue_manager.get_queue("get_test")
        
        assert retrieved_queue == created_queue
        
        # 获取不存在的队列
        assert queue_manager.get_queue("nonexistent") is None
    
    def test_remove_queue(self, queue_manager):
        """测试移除队列"""
        queue = queue_manager.create_queue("remove_test")
        assert "remove_test" in queue_manager.queues
        
        queue_manager.remove_queue("remove_test")
        assert "remove_test" not in queue_manager.queues
        assert queue._running is False  # 确保队列被停止
    
    def test_remove_nonexistent_queue_raises_error(self, queue_manager):
        """测试移除不存在队列抛出错误"""
        with pytest.raises(KeyError, match="队列不存在"):
            queue_manager.remove_queue("nonexistent")
    
    def test_global_metrics(self, queue_manager, tmp_path):
        """测试全局指标"""
        # 创建多个队列并添加任务
        queue1 = queue_manager.create_queue("queue1")
        queue2 = queue_manager.create_queue("queue2")
        
        # 添加测试任务
        for i in range(3):
            test_file = tmp_path / f"test1_{i}.mp4"
            test_file.write_bytes(b"test data")
            job = QueueJob(f"q1_job_{i}", test_file)
            queue1.add_job(job)
        
        for i in range(2):
            test_file = tmp_path / f"test2_{i}.mp4"
            test_file.write_bytes(b"test data")
            job = QueueJob(f"q2_job_{i}", test_file)
            queue2.add_job(job)
        
        global_metrics = queue_manager.get_global_metrics()
        
        assert global_metrics['total_queues'] == 2
        assert global_metrics['total_jobs'] == 5
        assert global_metrics['total_pending'] == 5
        assert global_metrics['total_running'] == 0
        assert global_metrics['total_completed'] == 0
        assert 'queues' in global_metrics
        assert len(global_metrics['queues']) == 2 