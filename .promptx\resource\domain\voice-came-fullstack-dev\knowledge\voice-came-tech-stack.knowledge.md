# Voice-came技术栈知识体系

## 🏗️ 核心架构

### VoiceTransl基础架构
Voice-came基于成熟的VoiceTransl架构进行扩展，继承其稳定性和可扩展性：

```python
# VoiceTransl核心架构组件
voice_came/
├── core/                    # 核心业务逻辑层
│   ├── base_engine.py      # 引擎抽象基类
│   ├── whisperx_engine.py  # WhisperX引擎实现
│   ├── batch_processor.py  # 批量处理器
│   └── translation_manager.py # 翻译管理器
├── engines/                # 引擎实现层
│   ├── whisperx/           # WhisperX语音识别
│   └── translation/        # 翻译引擎
├── utils/                  # 工具函数层
└── config/                 # 配置管理层
```

### 数据流架构
```mermaid
flowchart TD
    A[视频文件输入] --> B[音频提取]
    B --> C[WhisperX语音识别]
    C --> D[文本分段处理]
    D --> E[术语预处理]
    E --> F[本地LLM翻译]
    F --> G[术语后处理]
    G --> H[质量检查]
    H --> I[多格式导出]
```

## 🎤 语音处理技术栈

### WhisperX集成
**版本要求**: WhisperX 3.1.1+
**核心特性**:
- 高精度语音识别（准确率≥95%）
- 精确时间戳对齐
- 多语言支持（中、英、日、韩等）
- GPU加速支持

```python
# WhisperX集成示例
import whisperx

class WhisperXEngine:
    def __init__(self, model_size="large-v2", device="cuda"):
        self.model = whisperx.load_model(model_size, device)
        self.align_model = whisperx.load_align_model(language_code="zh", device=device)
    
    def transcribe(self, audio_path: str) -> dict:
        """转录音频文件"""
        audio = whisperx.load_audio(audio_path)
        result = self.model.transcribe(audio, batch_size=16)
        
        # 时间戳对齐
        result = whisperx.align(result["segments"], self.align_model, audio)
        return result
```

### 音频处理工具链
- **librosa**: 音频分析和特征提取
- **soundfile**: 音频文件读写
- **pydub**: 音频格式转换
- **ffmpeg-python**: 视频音频处理

```python
# 音频处理工具示例
import librosa
import soundfile as sf
from pydub import AudioSegment

def extract_audio_from_video(video_path: str, output_path: str):
    """从视频中提取音频"""
    audio = AudioSegment.from_file(video_path)
    audio.export(output_path, format="wav")

def preprocess_audio(audio_path: str) -> np.ndarray:
    """音频预处理"""
    audio, sr = librosa.load(audio_path, sr=16000)
    # 降噪、标准化等处理
    return audio
```

## 🌐 翻译引擎技术栈

### 本地LLM模型
**主要模型**:
- **Gemma3-12B-Q4**: Google开源模型，量化版本
- **Qwen3**: 阿里巴巴开源模型
- **模型大小**: 5-10GB（量化后）

```python
# 本地翻译引擎集成
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

class LocalTranslator:
    def __init__(self, model_name="gemma3-12b-q4"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto"
        )
    
    def translate(self, text: str, source_lang: str, target_lang: str) -> str:
        """翻译文本"""
        prompt = f"Translate from {source_lang} to {target_lang}: {text}"
        inputs = self.tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(**inputs, max_length=512)
        
        return self.tokenizer.decode(outputs[0], skip_special_tokens=True)
```

### 术语管理系统
**核心功能**:
- 100词助眠术语库
- 强制术语替换
- 一致性检查
- 自定义术语支持

```python
# 术语管理示例
class TerminologyManager:
    def __init__(self, terminology_file: str):
        self.terminology = self.load_terminology(terminology_file)
    
    def preprocess_text(self, text: str, source_lang: str) -> str:
        """翻译前术语预处理"""
        for term, translation in self.terminology.items():
            if source_lang in term:
                text = text.replace(term[source_lang], f"[TERM_{term['id']}]")
        return text
    
    def postprocess_text(self, text: str, target_lang: str) -> str:
        """翻译后术语后处理"""
        for term in self.terminology.values():
            if target_lang in term:
                text = text.replace(f"[TERM_{term['id']}]", term[target_lang])
        return text
```

## 💻 后端技术栈

### Python核心依赖
```python
# requirements.txt核心依赖
torch>=2.0.0              # PyTorch深度学习框架
transformers>=4.35.0      # Transformer模型库
whisperx>=3.1.1          # WhisperX语音识别
librosa>=0.10.1          # 音频处理
soundfile>=0.12.1        # 音频文件操作
pydub>=0.25.1            # 音频格式转换
opencv-python>=4.8.1     # 计算机视觉
ffmpeg-python>=0.2.0     # FFmpeg绑定
numpy>=1.24.0            # 数值计算
pandas>=2.0.0            # 数据处理
```

### 批量处理系统
**核心组件**:
- 任务队列管理
- 并发控制
- 进度跟踪
- 错误恢复

```python
# 批量处理系统架构
import asyncio
from concurrent.futures import ThreadPoolExecutor
from queue import Queue

class BatchProcessor:
    def __init__(self, max_workers=3):
        self.task_queue = Queue()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.progress_tracker = ProgressTracker()
    
    async def process_batch(self, file_paths: List[str]):
        """批量处理文件"""
        tasks = []
        for file_path in file_paths:
            task = self.executor.submit(self.process_single_file, file_path)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
    
    def process_single_file(self, file_path: str) -> ProcessResult:
        """处理单个文件"""
        try:
            # 语音识别
            transcription = self.whisperx_engine.transcribe(file_path)
            
            # 翻译处理
            translations = {}
            for target_lang in self.target_languages:
                translation = self.translator.translate(
                    transcription['text'], 
                    'zh', 
                    target_lang
                )
                translations[target_lang] = translation
            
            return ProcessResult(
                file_path=file_path,
                transcription=transcription,
                translations=translations,
                status="success"
            )
        except Exception as e:
            return ProcessResult(
                file_path=file_path,
                error=str(e),
                status="failed"
            )
```

## 🖥️ 前端技术栈

### 桌面应用框架
**主要选择**: PyQt6 / PySide6 或 Tkinter
**核心特性**:
- 跨平台支持
- 现代化UI设计
- 拖拽文件支持
- 实时进度显示

```python
# PyQt6桌面应用示例
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                            QWidget, QPushButton, QProgressBar, QTextEdit)
from PyQt6.QtCore import QThread, pyqtSignal

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Voice-came 语音翻译系统")
        self.setGeometry(100, 100, 800, 600)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 文件选择按钮
        self.file_button = QPushButton("选择视频文件")
        self.file_button.clicked.connect(self.select_files)
        layout.addWidget(self.file_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 日志显示
        self.log_text = QTextEdit()
        layout.addWidget(self.log_text)
    
    def select_files(self):
        """选择文件"""
        from PyQt6.QtWidgets import QFileDialog
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择视频文件", "", 
            "视频文件 (*.mp4 *.avi *.mov *.mkv)"
        )
        if files:
            self.start_processing(files)
    
    def start_processing(self, files):
        """开始处理"""
        self.worker = ProcessingWorker(files)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.log_updated.connect(self.update_log)
        self.worker.start()

class ProcessingWorker(QThread):
    progress_updated = pyqtSignal(int)
    log_updated = pyqtSignal(str)
    
    def __init__(self, files):
        super().__init__()
        self.files = files
    
    def run(self):
        """后台处理任务"""
        for i, file_path in enumerate(self.files):
            self.log_updated.emit(f"正在处理: {file_path}")
            
            # 调用后端处理逻辑
            result = self.process_file(file_path)
            
            # 更新进度
            progress = int((i + 1) / len(self.files) * 100)
            self.progress_updated.emit(progress)
            
            self.log_updated.emit(f"完成处理: {file_path}")
```

## 🔧 开发工具链

### 代码质量工具
```bash
# 代码格式化和检查
black .                    # 代码格式化
isort .                    # 导入排序
flake8 .                   # 代码风格检查
mypy .                     # 类型检查
bandit -r .                # 安全检查
```

### 测试框架
```python
# pytest测试配置
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --cov=voice_came
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=90
    -v
markers =
    integration: 集成测试
    slow: 慢速测试
    gpu: 需要GPU的测试
```

### 性能监控
```python
# 性能监控工具
import psutil
import time
from memory_profiler import profile

class PerformanceMonitor:
    def __init__(self):
        self.start_time = None
        self.start_memory = None
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.start_memory = psutil.virtual_memory().used
    
    def get_metrics(self) -> dict:
        """获取性能指标"""
        current_time = time.time()
        current_memory = psutil.virtual_memory().used
        
        return {
            "elapsed_time": current_time - self.start_time,
            "memory_usage": current_memory - self.start_memory,
            "cpu_percent": psutil.cpu_percent(),
            "gpu_memory": self.get_gpu_memory_usage()
        }
    
    def get_gpu_memory_usage(self) -> float:
        """获取GPU内存使用情况"""
        try:
            import torch
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / 1024**3  # GB
        except ImportError:
            pass
        return 0.0
```

## 🚀 部署技术栈

### 跨平台打包
```python
# PyInstaller配置
# voice_came.spec
a = Analysis(
    ['src/voice_came/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('models/', 'models/'),
        ('configs/', 'configs/'),
    ],
    hiddenimports=[
        'whisperx',
        'torch',
        'transformers',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='voice-came',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
```

### Docker容器化
```dockerfile
# Dockerfile
FROM python:3.8-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY configs/ ./configs/

# 设置环境变量
ENV PYTHONPATH=/app/src

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "src/voice_came/main.py"]
```

## 📊 性能优化技术

### GPU加速优化
```python
# GPU内存优化
import torch

class GPUOptimizer:
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    def optimize_memory(self):
        """优化GPU内存使用"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
    
    def get_optimal_batch_size(self, model_size: int) -> int:
        """根据GPU内存计算最优批次大小"""
        if not torch.cuda.is_available():
            return 1
        
        gpu_memory = torch.cuda.get_device_properties(0).total_memory
        available_memory = gpu_memory * 0.8  # 保留20%内存
        
        # 根据模型大小和可用内存计算批次大小
        estimated_batch_size = int(available_memory / (model_size * 4))  # 4字节/参数
        return max(1, min(estimated_batch_size, 16))
```

### 并发处理优化
```python
# 异步并发处理
import asyncio
import aiofiles
from concurrent.futures import ProcessPoolExecutor

class ConcurrentProcessor:
    def __init__(self, max_workers=None):
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
    
    async def process_files_concurrently(self, file_paths: List[str]) -> List[dict]:
        """并发处理多个文件"""
        loop = asyncio.get_event_loop()
        
        tasks = []
        for file_path in file_paths:
            task = loop.run_in_executor(
                self.executor, 
                self.process_single_file, 
                file_path
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
    
    def process_single_file(self, file_path: str) -> dict:
        """处理单个文件（在独立进程中运行）"""
        # 文件处理逻辑
        pass
```

这个技术栈知识体系涵盖了Voice-came项目的所有核心技术组件，为全栈开发工程师提供了完整的技术参考和实现指导。 