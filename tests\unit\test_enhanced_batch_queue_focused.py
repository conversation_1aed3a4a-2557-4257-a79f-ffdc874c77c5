#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
enhanced_batch_queue.py 聚焦测试

专门针对未覆盖的核心功能编写简化测试
确保关键代码路径被覆盖
"""

import pytest
import time
import json
from pathlib import Path
from unittest.mock import Mock, patch

from voice_came.core.enhanced_batch_queue import (
    QueueJob, JobState, JobPriority,
    QueueMetrics, PerformanceMonitor, ErrorRecoveryManager,
    EnhancedBatchQueue, QueueManager
)


class TestQueueJobCore:
    """QueueJob核心功能测试"""
    
    def test_job_with_kwargs(self, tmp_path):
        """测试任务初始化包含kwargs"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob(
            id="test_job", 
            file_path=test_file,
            custom_param="value",
            estimated_duration=45.0
        )
        
        assert job.custom_param == "value"
        assert job.estimated_duration == 45.0
        assert hasattr(job, 'custom_param')
    
    def test_job_priority_comparison(self, tmp_path):
        """测试优先级比较"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        
        high_job = QueueJob("high", test_file, priority=JobPriority.HIGH)
        normal_job = QueueJob("normal", test_file, priority=JobPriority.NORMAL)
        
        assert high_job < normal_job
    
    def test_job_retry_logic(self, tmp_path):
        """测试重试逻辑"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob("retry_test", test_file, max_retries=2)
        
        assert job.can_retry() is True
        
        job.retry_count = 2
        assert job.can_retry() is False
        
        job.reset_for_retry()
        assert job.retry_count == 3
        assert job.status == JobState.RETRYING
    
    def test_estimated_remaining_time(self, tmp_path):
        """测试估计剩余时间"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        
        job = QueueJob("time_test", test_file, estimated_duration=60.0)
        
        # 初始状态
        remaining = job.get_estimated_remaining_time()
        assert remaining == 60.0
        
        # 设置进度
        job.started_at = time.time()
        job.progress = 50.0
        time.sleep(0.1)
        
        remaining = job.get_estimated_remaining_time()
        assert remaining > 0 and remaining < 60.0


class TestQueueMetricsCore:
    """QueueMetrics核心测试"""
    
    def test_metrics_update(self, tmp_path):
        """测试指标更新"""
        metrics = QueueMetrics()
        
        # 创建测试任务
        jobs = {}
        for i, status in enumerate([JobState.PENDING, JobState.RUNNING, JobState.COMPLETED]):
            test_file = tmp_path / f"test_{i}.mp4"
            test_file.write_bytes(b"test data")
            job = QueueJob(f"job_{i}", test_file)
            job.status = status
            if status == JobState.COMPLETED:
                job.processing_time = 10.0
            jobs[f"job_{i}"] = job
        
        metrics.queue_start_time = time.time() - 3600  # 1小时前
        metrics.update_from_jobs(jobs)
        
        assert metrics.total_jobs == 3
        assert metrics.pending_jobs == 1
        assert metrics.running_jobs == 1
        assert metrics.completed_jobs == 1


class TestPerformanceMonitorCore:
    """PerformanceMonitor核心测试"""
    
    def test_record_job_completion(self, tmp_path):
        """测试记录任务完成"""
        monitor = PerformanceMonitor()
        
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("completed_job", test_file)
        job.processing_time = 15.0
        job.memory_usage = 100.0
        
        monitor.record_job_completion(job)
        
        assert len(monitor.performance_history) == 1
        record = monitor.performance_history[0]
        assert record['job_id'] == "completed_job"
        assert record['processing_time'] == 15.0
    
    def test_record_memory_usage(self):
        """测试记录内存使用"""
        monitor = PerformanceMonitor()
        
        with patch('psutil.virtual_memory') as mock_memory:
            mock_memory.return_value.percent = 75.0
            
            monitor.record_memory_usage()
            
            assert len(monitor.memory_samples) == 1
            assert monitor.memory_samples[0]['usage_percent'] == 75.0
    
    def test_performance_summary(self, tmp_path):
        """测试性能摘要"""
        monitor = PerformanceMonitor()
        
        # 添加一些性能记录
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("summary_job", test_file)
        job.processing_time = 20.0
        job.memory_usage = 150.0
        
        monitor.record_job_completion(job)
        
        summary = monitor.get_performance_summary()
        
        assert summary['total_jobs_processed'] == 1
        assert summary['average_processing_time'] == 20.0


class TestErrorRecoveryCore:
    """ErrorRecovery核心测试"""
    
    def test_handle_file_not_found(self, tmp_path):
        """测试文件不存在错误处理"""
        recovery = ErrorRecoveryManager()
        
        test_file = tmp_path / "nonexistent.mp4"
        job = QueueJob("file_error_job", test_file)
        error = FileNotFoundError("File not found")
        
        should_retry = recovery.handle_job_error(job, error)
        
        assert should_retry is False
        assert "文件不存在" in job.error_message
    
    def test_handle_memory_error(self, tmp_path):
        """测试内存错误处理"""
        recovery = ErrorRecoveryManager()
        
        test_file = tmp_path / "memory_test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("memory_error_job", test_file)
        error = MemoryError("Out of memory")
        
        should_retry = recovery.handle_job_error(job, error)
        
        assert should_retry is True
        assert "内存不足" in job.error_message
    
    def test_error_statistics(self, tmp_path):
        """测试错误统计"""
        recovery = ErrorRecoveryManager()
        
        test_file = tmp_path / "stats_test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("stats_job", test_file)
        
        # 触发不同类型的错误
        recovery.handle_job_error(job, FileNotFoundError("Not found"))
        recovery.handle_job_error(job, MemoryError("No memory"))
        
        stats = recovery.get_error_statistics()
        
        assert stats['total_errors'] == 2
        assert 'FileNotFoundError' in stats['error_types']
        assert 'MemoryError' in stats['error_types']


class TestEnhancedBatchQueueCore:
    """EnhancedBatchQueue核心测试"""
    
    def test_queue_initialization(self, tmp_path):
        """测试队列初始化"""
        state_file = tmp_path / "test_queue.json"
        queue = EnhancedBatchQueue(
            max_concurrent=4,
            state_file=state_file,
            enable_monitoring=True
        )
        
        assert queue.max_concurrent == 4
        assert queue.state_file == state_file
        assert queue.enable_monitoring is True
    
    def test_add_remove_jobs(self, tmp_path):
        """测试添加和移除任务"""
        queue = EnhancedBatchQueue()
        
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("test_job", test_file)
        
        # 添加任务
        job_id = queue.add_job(job)
        assert job_id == "test_job"
        assert queue.size() == 1
        
        # 移除任务
        removed_job = queue.remove_job("test_job")
        assert removed_job.id == "test_job"
        assert queue.size() == 0
    
    def test_queue_pause_resume(self, tmp_path):
        """测试队列暂停和恢复"""
        queue = EnhancedBatchQueue()
        
        test_file = tmp_path / "pause_test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("pause_job", test_file)
        queue.add_job(job)
        
        queue.start()
        assert queue._running is True
        
        queue.pause()
        assert queue._paused is True
        
        queue.resume()
        assert queue._paused is False
        
        queue.stop()
        assert queue._running is False
    
    def test_state_persistence(self, tmp_path):
        """测试状态持久化"""
        state_file = tmp_path / "persistence_test.json"
        queue = EnhancedBatchQueue(state_file=state_file)
        
        test_file = tmp_path / "persist_test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("persist_job", test_file)
        queue.add_job(job)
        
        # 保存状态
        queue.save_state()
        assert state_file.exists()
        
        # 验证保存内容
        with open(state_file, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        assert "jobs" in state_data
        assert "metadata" in state_data
        assert len(state_data["jobs"]) == 1
    
    def test_memory_monitoring(self):
        """测试内存监控"""
        queue = EnhancedBatchQueue()
        
        with patch('psutil.virtual_memory') as mock_memory:
            mock_memory.return_value.percent = 85.0
            
            usage = queue._get_current_memory_usage()
            assert usage == 85.0
    
    def test_destructor(self):
        """测试析构函数"""
        queue = EnhancedBatchQueue()
        queue.start()
        
        # 模拟析构
        queue.__del__()
        
        assert queue._running is False


class TestQueueManagerCore:
    """QueueManager核心测试"""
    
    def test_create_and_manage_queues(self):
        """测试创建和管理队列"""
        manager = QueueManager()
        
        # 创建队列
        queue1 = manager.create_queue("queue1", max_concurrent=2)
        assert queue1.max_concurrent == 2
        assert "queue1" in manager.queues
        
        # 获取队列
        retrieved_queue = manager.get_queue("queue1")
        assert retrieved_queue == queue1
        
        # 移除队列
        manager.remove_queue("queue1")
        assert "queue1" not in manager.queues
    
    def test_global_metrics(self, tmp_path):
        """测试全局指标"""
        manager = QueueManager()
        
        # 创建队列并添加任务
        queue = manager.create_queue("test_queue")
        
        test_file = tmp_path / "metrics_test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("metrics_job", test_file)
        queue.add_job(job)
        
        metrics = manager.get_global_metrics()
        
        assert metrics['total_queues'] == 1
        assert metrics['total_jobs'] == 1
        assert metrics['total_pending'] == 1


class TestEdgeCases:
    """边界情况测试"""
    
    def test_save_state_without_file(self):
        """测试无状态文件保存"""
        queue = EnhancedBatchQueue(state_file=None)
        queue.save_state()  # 应该安全执行
        
        assert queue.state_file is None
    
    def test_load_corrupted_state_file(self, tmp_path):
        """测试加载损坏状态文件"""
        state_file = tmp_path / "corrupted.json"
        
        with open(state_file, 'w') as f:
            f.write("{ invalid json")
        
        queue = EnhancedBatchQueue(state_file=state_file)
        queue._load_state_from_file()  # 应该不崩溃
        
        assert queue.size() == 0
    
    def test_cleanup_old_jobs(self, tmp_path):
        """测试清理旧任务"""
        queue = EnhancedBatchQueue()
        
        test_file = tmp_path / "cleanup_test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("cleanup_job", test_file)
        job.status = JobState.COMPLETED
        job.completed_at = time.time() - 7200  # 2小时前
        queue.add_job(job)
        
        initial_size = queue.size()
        queue._cleanup_completed_jobs()
        
        # 清理后大小可能发生变化
        assert queue.size() <= initial_size
    
    def test_unknown_error_handling(self, tmp_path):
        """测试未知错误处理"""
        recovery = ErrorRecoveryManager()
        
        class CustomError(Exception):
            pass
        
        test_file = tmp_path / "unknown_error_test.mp4"
        test_file.write_bytes(b"test data")
        job = QueueJob("unknown_error_job", test_file)
        error = CustomError("Unknown error type")
        
        should_retry = recovery.handle_job_error(job, error)
        
        # 未知错误默认重试
        assert should_retry is True
        assert "Unknown error type" in job.error_message 