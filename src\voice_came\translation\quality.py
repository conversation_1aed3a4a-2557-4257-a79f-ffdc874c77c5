#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4.3: 翻译质量评估模块 (TDD-Green最小实现)

针对助眠内容优化翻译质量，保持舒缓语调。
"""

import re
from typing import Dict, List, Any
import logging

from .models import QualityScore

logger = logging.getLogger(__name__)


class SleepContentQualityAssessor:
    """助眠内容翻译质量评估器 - TDD Green最小实现"""
    
    def __init__(self):
        # TDD Green: 最小实现，基本初始化
        self.calming_words = ["gentle", "soft", "peaceful", "calm", "soothing"]
        self.aggressive_words = ["quickly", "hard", "fast", "urgent"]
    
    def assess_terminology_consistency(self, source_text: str, translation: str, 
                                     source_lang: str, target_lang: str) -> float:
        """评估术语一致性"""
        # TDD Green: 最小实现，基本评估
        consistency_score = 0.0
        
        # 检查基本术语映射
        if source_lang == "zh" and target_lang == "en":
            if "冥想" in source_text and "meditation" in translation:
                consistency_score += 0.3
            if "放松" in source_text and "relaxation" in translation:
                consistency_score += 0.3
            if "助眠" in source_text and "sleep aid" in translation:
                consistency_score += 0.4
        
        return min(consistency_score, 1.0)
    
    def assess_calming_tone(self, text: str) -> float:
        """评估舒缓语调"""
        # TDD Green: 最小实现，基于关键词评估
        calming_count = sum(1 for word in self.calming_words if word in text.lower())
        aggressive_count = sum(1 for word in self.aggressive_words if word in text.lower())
        
        # 简单评分算法
        if aggressive_count > 0:
            return max(0.0, 0.5 - aggressive_count * 0.2)
        
        return min(1.0, 0.5 + calming_count * 0.1)
    
    def assess_translation_quality(self, source_text: str, translation: str, 
                                 target_lang: str) -> QualityScore:
        """评估整体翻译质量"""
        # TDD Green: 最小实现，返回基本质量评分
        terminology_consistency = self.assess_terminology_consistency(
            source_text, translation, "zh", target_lang
        )
        
        tone_appropriateness = self.assess_calming_tone(translation)
        
        # 简单的流畅度评估（基于长度比例）
        fluency = min(1.0, len(translation) / max(len(source_text), 1) * 0.8)
        
        # 准确性评估（简化版）
        accuracy = 0.8  # 固定值，实际实现中会更复杂
        
        # 计算总分
        overall_score = (
            terminology_consistency * 0.3 +
            tone_appropriateness * 0.3 +
            fluency * 0.2 +
            accuracy * 0.2
        )
        
        return QualityScore(
            overall_score=overall_score,
            fluency=fluency,
            accuracy=accuracy,
            terminology_consistency=terminology_consistency,
            details={
                "tone_appropriateness": tone_appropriateness,
                "source_length": len(source_text),
                "translation_length": len(translation)
            }
        )
