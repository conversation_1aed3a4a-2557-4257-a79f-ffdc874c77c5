#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhisperX集成演示

演示WhisperX引擎的基本功能，验证TDD实现
"""

import os
import sys
import tempfile
import numpy as np
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

try:
    from voice_came.speech_recognition.whisperx_engine import WhisperXEng<PERSON>, WhisperXConfig
    from voice_came.utils.logger import get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


def create_test_audio():
    """创建测试音频文件"""
    try:
        import soundfile as sf
        
        # 生成1秒的正弦波音频（1kHz）
        sample_rate = 16000
        duration = 1.0
        samples = int(sample_rate * duration)
        
        # 生成正弦波
        t = np.linspace(0, duration, samples, False)
        audio_data = np.sin(2 * np.pi * 1000 * t) * 0.5  # 1kHz正弦波
        
        # 保存为临时WAV文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        sf.write(temp_file.name, audio_data, sample_rate)
        
        return temp_file.name
        
    except ImportError:
        print("警告: soundfile未安装，使用空音频文件")
        # 创建空音频文件（仅用于测试）
        temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        return temp_file.name


def demo_basic_functionality():
    """演示基本功能"""
    logger = get_logger(__name__)
    
    print("🎯 WhisperX集成演示")
    print("=" * 50)
    
    # 创建配置
    config = WhisperXConfig(
        model_name="tiny",  # 使用最小模型
        device="cpu",       # 强制使用CPU
        compute_type="float32",
        language="zh",
        word_timestamps=True
    )
    
    print(f"📋 配置信息:")
    print(f"   模型: {config.model_name}")
    print(f"   设备: {config.device}")
    print(f"   语言: {config.language}")
    print()
    
    try:
        # 初始化引擎
        print("🔧 初始化WhisperX引擎...")
        engine = WhisperXEngine(config)
        print("✅ 引擎初始化成功")
        
        # 检查模型兼容性
        print("\n🔍 检查模型兼容性...")
        for model in ["tiny", "base", "unsupported"]:
            compatible = engine.check_model_compatibility(model)
            status = "✅" if compatible else "❌"
            print(f"   {model}: {status}")
        
        # 加载模型（这里会实际下载模型，可能需要时间）
        print("\n📥 加载模型...")
        try:
            success = engine.load_model()
            if success:
                print("✅ 模型加载成功")
                
                # 模型健康检查
                health = engine.check_model_health()
                print(f"   健康状态: {'✅ 正常' if health else '❌ 异常'}")
                
                # 模型预热
                warmup = engine.warm_up_model()
                print(f"   预热状态: {'✅ 完成' if warmup else '❌ 失败'}")
                
            else:
                print("❌ 模型加载失败")
                return
                
        except Exception as e:
            print(f"❌ 模型加载异常: {e}")
            return
        
        # 加载对齐模型
        print("\n🎯 加载对齐模型...")
        try:
            align_success = engine.load_align_model()
            if align_success:
                print("✅ 对齐模型加载成功")
            else:
                print("⚠️  对齐模型加载失败（可能缺少语言模型）")
        except Exception as e:
            print(f"⚠️  对齐模型加载异常: {e}")
        
        # 创建测试音频
        print("\n🎵 创建测试音频...")
        audio_file = create_test_audio()
        print(f"✅ 测试音频创建: {audio_file}")
        
        # 转录音频
        print("\n🎙️  开始转录...")
        try:
            def progress_callback(progress):
                print(f"   进度: {progress:.1%}")
            
            result = engine.transcribe(audio_file, progress_callback=progress_callback)
            print("✅ 转录完成")
            print(f"   结果: {result}")
            
        except Exception as e:
            print(f"❌ 转录失败: {e}")
        
        # 测试不同输出格式
        print("\n📄 测试输出格式...")
        try:
            # JSON格式
            json_output = engine.transcribe_to_json(audio_file)
            print(f"✅ JSON格式: {len(json_output)} 字符")
            
            # SRT格式
            srt_output = engine.transcribe_to_srt(audio_file)
            print(f"✅ SRT格式: {len(srt_output)} 字符")
            
            # VTT格式
            vtt_output = engine.transcribe_to_vtt(audio_file)
            print(f"✅ VTT格式: {len(vtt_output)} 字符")
            
        except Exception as e:
            print(f"❌ 格式转换失败: {e}")
        
        # 清理
        print("\n🧹 清理资源...")
        cache_cleared = engine.clear_model_cache()
        print(f"   缓存清理: {'✅ 完成' if cache_cleared else '❌ 失败'}")
        
        # 删除临时文件
        try:
            os.unlink(audio_file)
            print(f"   临时文件清理: ✅ 完成")
        except:
            print(f"   临时文件清理: ⚠️  部分失败")
        
        print("\n🎉 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def demo_batch_processing():
    """演示批量处理"""
    print("\n🔄 批量处理演示")
    print("-" * 30)
    
    config = WhisperXConfig(model_name="tiny", device="cpu")
    engine = WhisperXEngine(config)
    
    # 创建多个测试文件
    audio_files = []
    for i in range(3):
        audio_file = create_test_audio()
        audio_files.append(audio_file)
    
    print(f"📁 创建了 {len(audio_files)} 个测试文件")
    
    try:
        # 批量转录
        results = engine.transcribe_batch(audio_files)
        print(f"✅ 批量转录完成: {len(results)} 个结果")
        
        for i, result in enumerate(results):
            if "error" in result:
                print(f"   文件 {i+1}: ❌ {result['error']}")
            else:
                print(f"   文件 {i+1}: ✅ 转录成功")
    
    except Exception as e:
        print(f"❌ 批量处理失败: {e}")
    
    # 清理文件
    for audio_file in audio_files:
        try:
            os.unlink(audio_file)
        except:
            pass


if __name__ == "__main__":
    demo_basic_functionality()
    demo_batch_processing() 