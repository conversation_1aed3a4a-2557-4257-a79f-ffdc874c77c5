# 测试环境配置文件
# 这个文件包含测试环境的配置参数

# 环境标识
environment: test
testing: true

# 日志配置
logging:
  level: DEBUG
  file: logs/test.log
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 模型配置（测试用）
whisperx:
  model_size: tiny  # 使用最小模型加快测试
  device: cpu
  language: auto
  compute_type: int8

# 翻译模型配置（测试用）
translation:
  model: mock  # 使用模拟模型
  target_language: zh
  max_length: 512

# 音频配置
audio:
  sample_rate: 16000
  chunk_length: 10  # 短音频块用于测试
  formats: [wav, mp3, flac]

# 数据库配置（测试用）
database:
  url: "sqlite:///:memory:"
  echo: false

# 缓存配置
cache:
  type: simple
  ttl: 300
  max_size: 100

# API配置（测试用）
api:
  host: localhost
  port: 8000
  debug: true
  cors_enabled: true

# 文件路径配置
paths:
  temp_dir: ./temp_test
  output_dir: ./output_test
  model_cache_dir: ./models_test
  log_dir: ./logs_test

# 测试特定配置
test:
  mock_models: true
  skip_slow_tests: true
  cleanup_files: true
  max_test_duration: 300  # 最大测试时间（秒） 