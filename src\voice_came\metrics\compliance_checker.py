"""
TDD实践合规性检查器

检查TDD流程是否遵循测试先行原则
"""

import git
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
import re


class ComplianceChecker:
    """TDD实践合规性检查器"""
    
    def __init__(self, repo_path: str = "."):
        self.repo_path = Path(repo_path)
        self.logger = logging.getLogger(__name__)
        
        try:
            self.repo = git.Repo(self.repo_path)
        except Exception as e:
            self.logger.warning(f"无法初始化Git仓库: {e}")
            self.repo = None
        
        # 合规性标准
        self.compliance_rules = {
            'test_first_ratio': 0.8,        # 测试先行比例应>=80%
            'commit_message_pattern': r'(test|TDD|测试)',  # 提交信息包含测试关键词
            'test_file_ratio': 0.3,         # 测试文件占比>=30%
            'coverage_threshold': 0.9        # 覆盖率阈值>=90%
        }
    
    def check_tdd_compliance(self, days: int = 30) -> float:
        """检查TDD合规性，返回合规评分(0-100)"""
        try:
            # 各项合规检查
            test_first_score = self._check_test_first_compliance(days)
            commit_pattern_score = self._check_commit_patterns(days)
            file_structure_score = self._check_file_structure()
            coverage_compliance_score = self._check_coverage_compliance()
            
            # 计算加权平均分
            weights = {
                'test_first': 0.4,
                'commit_pattern': 0.2,
                'file_structure': 0.2,
                'coverage': 0.2
            }
            
            total_score = (
                test_first_score * weights['test_first'] +
                commit_pattern_score * weights['commit_pattern'] +
                file_structure_score * weights['file_structure'] +
                coverage_compliance_score * weights['coverage']
            )
            
            self.logger.info(f"TDD合规性评分: {total_score:.1f}")
            return total_score
            
        except Exception as e:
            self.logger.error(f"检查TDD合规性时出错: {e}")
            return 0.0
    
    def _check_test_first_compliance(self, days: int) -> float:
        """检查测试先行合规性"""
        if not self.repo:
            return 50.0
        
        try:
            # 获取最近N天的提交
            since_date = datetime.now() - timedelta(days=days)
            commits = list(self.repo.iter_commits(since=since_date))
            
            if not commits:
                return 100.0  # 没有提交，认为合规
            
            test_first_commits = 0
            total_commits = len(commits)
            
            for commit in commits:
                if self._is_test_first_commit(commit):
                    test_first_commits += 1
            
            test_first_ratio = test_first_commits / total_commits
            
            # 转换为0-100分数
            if test_first_ratio >= self.compliance_rules['test_first_ratio']:
                return 100.0
            else:
                return (test_first_ratio / self.compliance_rules['test_first_ratio']) * 100
                
        except Exception as e:
            self.logger.error(f"检查测试先行合规性时出错: {e}")
            return 50.0
    
    def _is_test_first_commit(self, commit) -> bool:
        """判断提交是否遵循测试先行"""
        try:
            # 检查提交信息
            message = commit.message.lower()
            if any(keyword in message for keyword in ['test', 'tdd', '测试']):
                return True
            
            # 检查提交的文件变更
            test_files_changed = 0
            impl_files_changed = 0
            
            for item in commit.stats.files:
                if 'test' in item.lower() or item.endswith('_test.py'):
                    test_files_changed += 1
                elif item.endswith('.py') and 'test' not in item.lower():
                    impl_files_changed += 1
            
            # 如果测试文件变更比例较高，认为遵循测试先行
            if test_files_changed > 0 and impl_files_changed > 0:
                return test_files_changed >= impl_files_changed
            elif test_files_changed > 0:
                return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"分析提交 {commit.hexsha} 时出错: {e}")
            return False
    
    def _check_commit_patterns(self, days: int) -> float:
        """检查提交信息模式"""
        if not self.repo:
            return 50.0
        
        try:
            since_date = datetime.now() - timedelta(days=days)
            commits = list(self.repo.iter_commits(since=since_date))
            
            if not commits:
                return 100.0
            
            pattern_matches = 0
            pattern = re.compile(self.compliance_rules['commit_message_pattern'], re.IGNORECASE)
            
            for commit in commits:
                if pattern.search(commit.message):
                    pattern_matches += 1
            
            match_ratio = pattern_matches / len(commits)
            return match_ratio * 100
            
        except Exception as e:
            self.logger.error(f"检查提交模式时出错: {e}")
            return 50.0
    
    def _check_file_structure(self) -> float:
        """检查文件结构合规性"""
        try:
            src_dir = self.repo_path / "src"
            tests_dir = self.repo_path / "tests"
            
            if not src_dir.exists():
                return 50.0
            
            # 统计源代码文件和测试文件
            src_files = list(src_dir.rglob("*.py"))
            
            # 移除__init__.py文件
            src_files = [f for f in src_files if f.name != "__init__.py"]
            
            # 统计测试文件
            test_files = []
            
            # 从tests目录统计
            if tests_dir.exists():
                test_files.extend(tests_dir.rglob("test_*.py"))
                test_files.extend(tests_dir.rglob("*_test.py"))
            
            # 从src目录统计内联测试
            test_files.extend(src_dir.rglob("test_*.py"))
            test_files.extend(src_dir.rglob("*_test.py"))
            
            if not src_files:
                return 100.0
            
            test_ratio = len(test_files) / len(src_files)
            
            # 转换为分数
            if test_ratio >= self.compliance_rules['test_file_ratio']:
                return 100.0
            else:
                return (test_ratio / self.compliance_rules['test_file_ratio']) * 100
                
        except Exception as e:
            self.logger.error(f"检查文件结构时出错: {e}")
            return 50.0
    
    def _check_coverage_compliance(self) -> float:
        """检查覆盖率合规性"""
        try:
            # 运行覆盖率检查
            result = subprocess.run([
                "pytest", "--cov=src/voice_came", "--cov-report=json:coverage.json", "--tb=no", "-q"
            ], capture_output=True, text=True, cwd=self.repo_path)
            
            coverage_file = self.repo_path / "coverage.json"
            
            if not coverage_file.exists():
                return 50.0  # 没有覆盖率数据
            
            import json
            with open(coverage_file, 'r') as f:
                coverage_data = json.load(f)
            
            total_coverage = coverage_data.get('totals', {}).get('percent_covered', 0) / 100
            
            # 转换为分数
            if total_coverage >= self.compliance_rules['coverage_threshold']:
                return 100.0
            else:
                return (total_coverage / self.compliance_rules['coverage_threshold']) * 100
                
        except Exception as e:
            self.logger.error(f"检查覆盖率合规性时出错: {e}")
            return 50.0
    
    def get_detailed_compliance_report(self, days: int = 30) -> Dict[str, Any]:
        """获取详细的合规性报告"""
        try:
            report = {
                'overall_score': self.check_tdd_compliance(days),
                'test_first_analysis': self._get_test_first_details(days),
                'commit_pattern_analysis': self._get_commit_pattern_details(days),
                'file_structure_analysis': self._get_file_structure_details(),
                'coverage_analysis': self._get_coverage_details(),
                'violations': self._get_compliance_violations(days),
                'recommendations': self._generate_compliance_recommendations(days)
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成合规性报告时出错: {e}")
            return {'error': '无法生成合规性报告'}
    
    def _get_test_first_details(self, days: int) -> Dict[str, Any]:
        """获取测试先行详细信息"""
        if not self.repo:
            return {'error': '无Git仓库信息'}
        
        try:
            since_date = datetime.now() - timedelta(days=days)
            commits = list(self.repo.iter_commits(since=since_date))
            
            test_first_commits = []
            non_compliant_commits = []
            
            for commit in commits:
                if self._is_test_first_commit(commit):
                    test_first_commits.append({
                        'sha': commit.hexsha[:8],
                        'message': commit.message.strip(),
                        'date': commit.committed_datetime.isoformat()
                    })
                else:
                    non_compliant_commits.append({
                        'sha': commit.hexsha[:8],
                        'message': commit.message.strip(),
                        'date': commit.committed_datetime.isoformat()
                    })
            
            return {
                'total_commits': len(commits),
                'test_first_commits': len(test_first_commits),
                'compliance_ratio': len(test_first_commits) / len(commits) if commits else 0,
                'compliant_commits': test_first_commits[:5],  # 最近5个合规提交
                'non_compliant_commits': non_compliant_commits[:5]  # 最近5个不合规提交
            }
            
        except Exception as e:
            self.logger.error(f"获取测试先行详情时出错: {e}")
            return {'error': '无法获取测试先行详情'}
    
    def _get_commit_pattern_details(self, days: int) -> Dict[str, Any]:
        """获取提交模式详细信息"""
        # 简化实现
        return {
            'pattern_matches': 0,
            'total_commits': 0,
            'match_ratio': 0.0
        }
    
    def _get_file_structure_details(self) -> Dict[str, Any]:
        """获取文件结构详细信息"""
        try:
            src_dir = self.repo_path / "src"
            tests_dir = self.repo_path / "tests"
            
            src_files = list(src_dir.rglob("*.py")) if src_dir.exists() else []
            test_files = list(tests_dir.rglob("*.py")) if tests_dir.exists() else []
            
            return {
                'source_files': len(src_files),
                'test_files': len(test_files),
                'test_ratio': len(test_files) / len(src_files) if src_files else 0,
                'has_test_directory': tests_dir.exists()
            }
            
        except Exception as e:
            self.logger.error(f"获取文件结构详情时出错: {e}")
            return {'error': '无法获取文件结构详情'}
    
    def _get_coverage_details(self) -> Dict[str, Any]:
        """获取覆盖率详细信息"""
        # 简化实现
        return {
            'current_coverage': 0.0,
            'target_coverage': self.compliance_rules['coverage_threshold'],
            'coverage_gap': 0.0
        }
    
    def _get_compliance_violations(self, days: int) -> List[Dict[str, Any]]:
        """获取合规性违规信息"""
        violations = []
        
        try:
            # 检查测试先行违规
            test_first_score = self._check_test_first_compliance(days)
            if test_first_score < 80:
                violations.append({
                    'type': 'test_first',
                    'severity': 'high' if test_first_score < 50 else 'medium',
                    'message': f'测试先行比例仅{test_first_score:.1f}%，低于标准',
                    'recommendation': '提高测试先行开发比例'
                })
            
            # 检查覆盖率违规
            coverage_score = self._check_coverage_compliance()
            if coverage_score < 90:
                violations.append({
                    'type': 'coverage',
                    'severity': 'high' if coverage_score < 70 else 'medium',
                    'message': f'测试覆盖率{coverage_score:.1f}%，低于90%标准',
                    'recommendation': '增加测试用例提高覆盖率'
                })
            
        except Exception as e:
            self.logger.error(f"获取违规信息时出错: {e}")
        
        return violations
    
    def _generate_compliance_recommendations(self, days: int) -> List[str]:
        """生成合规性改进建议"""
        recommendations = []
        
        try:
            score = self.check_tdd_compliance(days)
            
            if score < 80:
                recommendations.append("TDD合规性需要改进，建议加强团队培训")
            
            # 根据具体得分给出建议
            test_first_score = self._check_test_first_compliance(days)
            if test_first_score < 80:
                recommendations.append("建议严格遵循测试先行原则，先写测试再实现功能")
            
            coverage_score = self._check_coverage_compliance()
            if coverage_score < 90:
                recommendations.append("建议增加测试用例，提高代码覆盖率到90%以上")
            
            file_structure_score = self._check_file_structure()
            if file_structure_score < 80:
                recommendations.append("建议为每个源文件创建对应的测试文件")
            
        except Exception as e:
            self.logger.error(f"生成合规性建议时出错: {e}")
            recommendations.append("无法分析合规性，请检查项目配置")
        
        return recommendations 