"""
测试覆盖率监控器

实时监控测试覆盖率，提供覆盖率统计和分析功能
"""

import subprocess
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
import json


class CoverageMonitor:
    """测试覆盖率监控器"""
    
    def __init__(self, src_dir: str = "src/voice_came"):
        self.src_dir = Path(src_dir)
        self.coverage_file = Path("coverage.xml")
        self.coverage_html_dir = Path("htmlcov")
        self.logger = logging.getLogger(__name__)
        
        # 覆盖率目标
        self.coverage_targets = {
            "overall": 0.90,  # 整体覆盖率目标90%
            "modules": {
                "core": 0.95,      # 核心模块95%
                "ui": 0.85,        # UI模块85%
                "utils": 0.90,     # 工具模块90%
                "tests": 0.80      # 测试模块80%
            }
        }
    
    def run_coverage_analysis(self) -> Dict[str, Any]:
        """运行覆盖率分析"""
        try:
            # 运行测试并生成覆盖率报告
            result = subprocess.run([
                "pytest", 
                "--cov=src/voice_came",
                "--cov-report=xml:coverage.xml",
                "--cov-report=html:htmlcov",
                "--cov-report=term-missing",
                "--cov-fail-under=90"
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                self.logger.warning(f"覆盖率测试警告: {result.stderr}")
            
            # 解析覆盖率报告
            return self._parse_coverage_report()
            
        except Exception as e:
            self.logger.error(f"运行覆盖率分析时出错: {e}")
            return self._get_default_coverage_data()
    
    def get_current_coverage(self) -> Dict[str, Any]:
        """获取当前覆盖率数据"""
        if self.coverage_file.exists():
            return self._parse_coverage_report()
        else:
            # 如果没有覆盖率文件，运行分析
            return self.run_coverage_analysis()
    
    def _parse_coverage_report(self) -> Dict[str, Any]:
        """解析XML覆盖率报告"""
        try:
            if not self.coverage_file.exists():
                return self._get_default_coverage_data()
            
            tree = ET.parse(self.coverage_file)
            root = tree.getroot()
            
            # 获取整体覆盖率
            overall_coverage = float(root.attrib.get('line-rate', 0.0))
            
            # 获取模块级覆盖率
            modules_coverage = {}
            packages = root.findall('.//package')
            
            for package in packages:
                package_name = package.attrib.get('name', '').replace('src.voice_came.', '')
                if package_name:
                    package_coverage = float(package.attrib.get('line-rate', 0.0))
                    modules_coverage[package_name] = package_coverage
            
            # 获取测试统计
            test_stats = self._get_test_statistics()
            
            coverage_data = {
                'coverage_percentage': overall_coverage,
                'modules_coverage': modules_coverage,
                'total_tests': test_stats.get('total', 0),
                'passed_tests': test_stats.get('passed', 0),
                'failed_tests': test_stats.get('failed', 0),
                'pass_rate': test_stats.get('pass_rate', 0.0),
                'coverage_targets': self.coverage_targets,
                'target_met': overall_coverage >= self.coverage_targets['overall'],
                'missing_coverage': max(0, self.coverage_targets['overall'] - overall_coverage),
                'detailed_coverage': self._get_detailed_coverage_info()
            }
            
            return coverage_data
            
        except Exception as e:
            self.logger.error(f"解析覆盖率报告时出错: {e}")
            return self._get_default_coverage_data()
    
    def _get_test_statistics(self) -> Dict[str, Any]:
        """获取测试统计信息"""
        try:
            # 运行pytest获取测试统计
            result = subprocess.run([
                "pytest", "--collect-only", "-q"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                # 解析测试数量
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'test' in line and 'collected' in line:
                        import re
                        match = re.search(r'(\d+) tests? collected', line)
                        if match:
                            total_tests = int(match.group(1))
                            
                            # 运行测试获取通过率
                            test_result = subprocess.run([
                                "pytest", "--tb=no", "-q"
                            ], capture_output=True, text=True)
                            
                            # 解析测试结果
                            passed = 0
                            failed = 0
                            
                            if test_result.returncode == 0:
                                # 全部通过
                                passed = total_tests
                                failed = 0
                            else:
                                # 有失败的测试
                                result_lines = test_result.stdout.split('\n')
                                for line in result_lines:
                                    if 'passed' in line or 'failed' in line:
                                        pass_match = re.search(r'(\d+) passed', line)
                                        fail_match = re.search(r'(\d+) failed', line)
                                        if pass_match:
                                            passed = int(pass_match.group(1))
                                        if fail_match:
                                            failed = int(fail_match.group(1))
                            
                            pass_rate = passed / total_tests if total_tests > 0 else 0.0
                            
                            return {
                                'total': total_tests,
                                'passed': passed,
                                'failed': failed,
                                'pass_rate': pass_rate
                            }
        except Exception as e:
            self.logger.error(f"获取测试统计时出错: {e}")
        
        return {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'pass_rate': 0.0
        }
    
    def _get_detailed_coverage_info(self) -> Dict[str, Any]:
        """获取详细的覆盖率信息"""
        detailed_info = {
            'uncovered_lines': [],
            'partially_covered_files': [],
            'fully_covered_files': []
        }
        
        try:
            if not self.coverage_file.exists():
                return detailed_info
            
            tree = ET.parse(self.coverage_file)
            root = tree.getroot()
            
            # 分析每个文件的覆盖率
            classes = root.findall('.//class')
            
            for cls in classes:
                filename = cls.attrib.get('filename', '')
                if filename.startswith('src/voice_came'):
                    line_rate = float(cls.attrib.get('line-rate', 0.0))
                    
                    if line_rate == 1.0:
                        detailed_info['fully_covered_files'].append(filename)
                    elif line_rate > 0.0:
                        detailed_info['partially_covered_files'].append({
                            'filename': filename,
                            'coverage': line_rate
                        })
                    
                    # 获取未覆盖的行
                    lines = cls.findall('.//line')
                    uncovered_lines = []
                    
                    for line in lines:
                        if line.attrib.get('hits', '0') == '0':
                            uncovered_lines.append(int(line.attrib.get('number', 0)))
                    
                    if uncovered_lines:
                        detailed_info['uncovered_lines'].append({
                            'filename': filename,
                            'lines': uncovered_lines
                        })
        
        except Exception as e:
            self.logger.error(f"获取详细覆盖率信息时出错: {e}")
        
        return detailed_info
    
    def _get_default_coverage_data(self) -> Dict[str, Any]:
        """获取默认覆盖率数据"""
        return {
            'coverage_percentage': 0.0,
            'modules_coverage': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'pass_rate': 0.0,
            'coverage_targets': self.coverage_targets,
            'target_met': False,
            'missing_coverage': self.coverage_targets['overall'],
            'detailed_coverage': {
                'uncovered_lines': [],
                'partially_covered_files': [],
                'fully_covered_files': []
            }
        }
    
    def get_coverage_trend(self, history_file: str = "metrics/coverage_history.json") -> Dict[str, Any]:
        """获取覆盖率趋势"""
        try:
            history_path = Path(history_file)
            if not history_path.exists():
                return {"error": "无历史数据"}
            
            with open(history_path, 'r', encoding='utf-8') as f:
                history_data = json.load(f)
            
            # 计算趋势
            if len(history_data) >= 2:
                recent_data = history_data[-7:]  # 最近7次记录
                trend = {
                    'improving': recent_data[-1]['coverage'] > recent_data[0]['coverage'],
                    'change_rate': recent_data[-1]['coverage'] - recent_data[0]['coverage'],
                    'average_coverage': sum(d['coverage'] for d in recent_data) / len(recent_data)
                }
                return trend
            
        except Exception as e:
            self.logger.error(f"获取覆盖率趋势时出错: {e}")
        
        return {"error": "趋势分析失败"}
    
    def generate_coverage_recommendations(self, coverage_data: Dict[str, Any]) -> List[str]:
        """生成覆盖率改进建议"""
        recommendations = []
        
        current_coverage = coverage_data.get('coverage_percentage', 0.0)
        target_coverage = self.coverage_targets['overall']
        
        if current_coverage < target_coverage:
            gap = target_coverage - current_coverage
            recommendations.append(f"需要将覆盖率提升 {gap:.1%} 以达到目标")
        
        # 分析模块覆盖率
        modules_coverage = coverage_data.get('modules_coverage', {})
        for module, coverage in modules_coverage.items():
            module_target = self.coverage_targets['modules'].get(module, target_coverage)
            if coverage < module_target:
                recommendations.append(f"{module} 模块覆盖率偏低，建议增加测试")
        
        # 分析未覆盖的文件
        detailed_coverage = coverage_data.get('detailed_coverage', {})
        partial_files = detailed_coverage.get('partially_covered_files', [])
        
        if partial_files:
            low_coverage_files = [f for f in partial_files if f['coverage'] < 0.8]
            if low_coverage_files:
                recommendations.append("建议为低覆盖率文件增加测试用例")
        
        return recommendations 