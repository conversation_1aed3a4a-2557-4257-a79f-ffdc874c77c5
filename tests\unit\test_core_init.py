#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Core模块初始化文件测试
"""

import sys
import os
import pytest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

import voice_came.core as core_module


class TestCoreModuleImports:
    """测试core模块的导入功能"""
    
    def test_module_has_all_exports(self):
        """测试模块包含所有预期的导出"""
        expected_exports = [
            "FileValidator",
            "ValidationResult", 
            "BatchQueue",
            "ProcessingJob",
            "JobStatus",
            "FileProcessor",
        ]
        
        # 检查__all__列表
        assert hasattr(core_module, '__all__')
        assert core_module.__all__ == expected_exports
        
        # 检查每个导出是否真正可用
        for export_name in expected_exports:
            assert hasattr(core_module, export_name)
            exported_item = getattr(core_module, export_name)
            assert exported_item is not None
    
    def test_version_exists(self):
        """测试版本号存在"""
        assert hasattr(core_module, '__version__')
        assert core_module.__version__ == '0.1.0'
        assert isinstance(core_module.__version__, str)
    
    def test_author_exists(self):
        """测试作者信息存在"""
        assert hasattr(core_module, '__author__')
        assert core_module.__author__ == 'Voice-came Team'
        assert isinstance(core_module.__author__, str)
    
    def test_module_docstring(self):
        """测试模块文档字符串"""
        assert core_module.__doc__ is not None
        assert "核心模块" in core_module.__doc__
        assert "文件验证" in core_module.__doc__
        assert "批量处理队列" in core_module.__doc__
    
    def test_individual_imports(self):
        """测试各个组件可以单独导入"""
        from voice_came.core import FileValidator
        from voice_came.core import ValidationResult
        from voice_came.core import BatchQueue
        from voice_came.core import ProcessingJob
        from voice_came.core import JobStatus
        from voice_came.core import FileProcessor
        
        # 检查导入的对象不为空
        assert FileValidator is not None
        assert ValidationResult is not None
        assert BatchQueue is not None
        assert ProcessingJob is not None
        assert JobStatus is not None
        assert FileProcessor is not None
    
    def test_class_types(self):
        """测试导入的类型"""
        # FileValidator 应该是一个类
        assert isinstance(core_module.FileValidator, type)
        
        # ValidationResult 应该是一个类或者namedtuple等
        assert core_module.ValidationResult is not None
        
        # BatchQueue 应该是一个类
        assert isinstance(core_module.BatchQueue, type)
        
        # ProcessingJob 应该是一个类或者namedtuple等
        assert core_module.ProcessingJob is not None
        
        # JobStatus 可能是枚举或类
        assert core_module.JobStatus is not None
        
        # FileProcessor 应该是一个类
        assert isinstance(core_module.FileProcessor, type)
    
    def test_star_import(self):
        """测试星号导入功能"""
        # 创建一个新的命名空间来测试星号导入
        namespace = {}
        exec("from voice_came.core import *", namespace)
        
        # 检查所有预期的导出都被导入
        expected_exports = [
            "FileValidator",
            "ValidationResult", 
            "BatchQueue",
            "ProcessingJob",
            "JobStatus",
            "FileProcessor",
        ]
        
        for export_name in expected_exports:
            assert export_name in namespace
            assert namespace[export_name] is not None
    
    def test_module_attributes(self):
        """测试模块属性"""
        # 检查模块名
        assert core_module.__name__ == 'voice_came.core'
        
        # 检查包路径
        assert hasattr(core_module, '__path__')
        
        # 检查文件路径
        assert hasattr(core_module, '__file__')
        assert core_module.__file__.endswith('__init__.py')


class TestCoreModuleClasses:
    """测试core模块中各个类的基本属性"""
    
    def test_file_validator_instantiation(self):
        """测试FileValidator实例化"""
        validator = core_module.FileValidator()
        assert validator is not None
        
        # 检查基本方法存在
        assert hasattr(validator, 'validate_file')
        assert callable(validator.validate_file)
    
    def test_batch_queue_instantiation(self):
        """测试BatchQueue实例化"""
        queue = core_module.BatchQueue()
        assert queue is not None
        
        # 检查基本方法存在
        assert hasattr(queue, 'add_job')
        assert callable(queue.add_job)
    
    def test_file_processor_instantiation(self):
        """测试FileProcessor实例化"""
        # FileProcessor是抽象类，无法直接实例化
        # 检查它是一个类
        assert isinstance(core_module.FileProcessor, type)
        
        # 检查基本方法存在
        assert hasattr(core_module.FileProcessor, 'process_file')
        
        # 检查是否有抽象方法
        assert hasattr(core_module.FileProcessor, '__abstractmethods__')
    
    def test_validation_result_properties(self):
        """测试ValidationResult属性"""
        # ValidationResult可能是namedtuple或dataclass
        validation_result = core_module.ValidationResult
        assert validation_result is not None
        
        # 如果是类，应该可以实例化
        if isinstance(validation_result, type):
            try:
                # 尝试创建实例，可能需要参数
                pass
            except TypeError:
                # 如果需要参数，这是正常的
                pass
    
    def test_processing_job_properties(self):
        """测试ProcessingJob属性"""
        processing_job = core_module.ProcessingJob
        assert processing_job is not None
        
        # 如果是类，应该可以检查其属性
        if isinstance(processing_job, type):
            try:
                # 尝试创建实例，可能需要参数
                pass
            except TypeError:
                # 如果需要参数，这是正常的
                pass
    
    def test_job_status_properties(self):
        """测试JobStatus属性"""
        job_status = core_module.JobStatus
        assert job_status is not None
        
        # JobStatus可能是枚举类
        if hasattr(job_status, '__members__'):
            # 如果是枚举，检查成员
            assert len(job_status.__members__) > 0


class TestCoreModuleEdgeCases:
    """测试core模块的边界情况"""
    
    def test_module_reload(self):
        """测试模块重新加载"""
        import importlib
        
        # 重新加载模块
        reloaded_module = importlib.reload(core_module)
        
        # 检查重新加载后的模块
        assert reloaded_module is not None
        assert hasattr(reloaded_module, '__all__')
        assert hasattr(reloaded_module, '__version__')
        assert hasattr(reloaded_module, '__author__')
    
    def test_imports_consistency(self):
        """测试导入的一致性"""
        for export_name in core_module.__all__:
            # 每个导出都应该存在于模块中
            assert hasattr(core_module, export_name)
            
            # 获取的对象应该不为空
            exported_item = getattr(core_module, export_name)
            assert exported_item is not None
    
    def test_no_extra_public_exports(self):
        """测试没有额外的公共导出"""
        # 获取所有公共属性（不以下划线开头的）
        public_attrs = [attr for attr in dir(core_module) 
                       if not attr.startswith('_')]
        
        # 过滤掉已知的模块属性
        known_attrs = set(core_module.__all__ + ['__version__', '__author__'])
        
        # 检查是否有未在__all__中声明的公共属性
        extra_attrs = set(public_attrs) - known_attrs
        
        # 应该没有意外的额外公共属性
        # 允许一些常见的模块属性和模块文件名
        allowed_extra = {'batch_queue', 'file_validator', 'file_processor'}
        
        unexpected_attrs = extra_attrs - allowed_extra
        assert len(unexpected_attrs) == 0, f"发现未预期的公共属性: {unexpected_attrs}"
    
    def test_module_metadata(self):
        """测试模块元数据"""
        # 版本号应该是字符串且格式合理
        version = core_module.__version__
        assert isinstance(version, str)
        assert len(version.split('.')) >= 2  # 至少有主版本号和次版本号
        
        # 作者信息应该是字符串
        author = core_module.__author__
        assert isinstance(author, str)
        assert len(author) > 0
        
        # 文档字符串应该存在且有意义
        doc = core_module.__doc__
        assert isinstance(doc, str)
        assert len(doc) > 10  # 应该有实质内容
    
    def test_module_structure(self):
        """测试模块结构完整性"""
        # 检查模块的基本结构
        assert hasattr(core_module, '__name__')
        assert hasattr(core_module, '__file__')
        assert hasattr(core_module, '__package__')
        
        # 包名应该正确
        assert core_module.__package__ == 'voice_came.core'


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 