# WhisperX集成专业知识

## 🎯 WhisperX核心概念

### 与Whisper的区别
WhisperX是Whisper的增强版本，主要改进：
- **精确时间戳对齐**：提供单词级别的时间戳
- **说话人分离**：支持多说话人识别
- **批量处理优化**：更高效的批量音频处理
- **VAD集成**：集成语音活动检测

### 核心组件架构
```python
# WhisperX核心组件
whisperx_pipeline = {
    "transcription": "语音转文字",
    "alignment": "时间戳对齐", 
    "diarization": "说话人分离",
    "vad": "语音活动检测"
}
```

## 🔧 技术集成方案

### 基础集成代码
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhisperX引擎集成模块

该模块负责将WhisperX集成到Voice-came系统中，
提供高精度语音识别和时间戳对齐功能。
"""

import os
import torch
import whisperx
import gc
from typing import Dict, List, Optional, Union
from pathlib import Path
import logging

from voice_came.core.base_engine import BaseEngine
from voice_came.exceptions import WhisperXError
from voice_came.utils.audio_utils import AudioProcessor
from voice_came.utils.logger import get_logger

logger = get_logger(__name__)

class WhisperXEngine(BaseEngine):
    """WhisperX语音识别引擎
    
    集成WhisperX提供高精度语音识别、时间戳对齐和说话人分离功能。
    支持GPU加速和批量处理。
    """
    
    def __init__(
        self,
        model_size: str = "large-v2",
        device: str = "cuda",
        compute_type: str = "float16",
        batch_size: int = 16,
        language: str = "zh"
    ):
        """初始化WhisperX引擎
        
        Args:
            model_size: 模型大小 (tiny, base, small, medium, large, large-v2)
            device: 计算设备 (cuda, cpu)
            compute_type: 计算精度 (float16, float32)
            batch_size: 批处理大小
            language: 主要语言代码
        """
        super().__init__()
        
        self.model_size = model_size
        self.device = device
        self.compute_type = compute_type
        self.batch_size = batch_size
        self.language = language
        
        # 模型实例
        self.model = None
        self.align_model = None
        self.align_metadata = None
        self.diarize_model = None
        
        # 音频处理器
        self.audio_processor = AudioProcessor()
        
        # 初始化模型
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化WhisperX模型"""
        try:
            logger.info(f"正在加载WhisperX模型: {self.model_size}")
            
            # 加载转录模型
            self.model = whisperx.load_model(
                self.model_size, 
                self.device, 
                compute_type=self.compute_type
            )
            
            # 加载对齐模型
            self.align_model, self.align_metadata = whisperx.load_align_model(
                language_code=self.language, 
                device=self.device
            )
            
            logger.info("WhisperX模型加载完成")
            
        except Exception as e:
            logger.error(f"WhisperX模型加载失败: {e}")
            raise WhisperXError(f"模型初始化失败: {e}")
    
    def transcribe_audio(
        self, 
        audio_path: Union[str, Path],
        language: Optional[str] = None,
        enable_vad: bool = True
    ) -> Dict:
        """转录音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 指定语言（可选）
            enable_vad: 是否启用语音活动检测
            
        Returns:
            Dict: 转录结果，包含文本和时间戳信息
            
        Raises:
            WhisperXError: 转录过程中的错误
        """
        try:
            audio_path = Path(audio_path)
            if not audio_path.exists():
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
            logger.info(f"开始转录音频: {audio_path}")
            
            # 加载音频
            audio = whisperx.load_audio(str(audio_path))
            
            # 语音转录
            result = self.model.transcribe(
                audio, 
                batch_size=self.batch_size,
                language=language or self.language
            )
            
            # 时间戳对齐
            if self.align_model:
                result = whisperx.align(
                    result["segments"], 
                    self.align_model, 
                    self.align_metadata, 
                    audio, 
                    self.device, 
                    return_char_alignments=False
                )
            
            # 清理GPU内存
            if self.device == "cuda":
                torch.cuda.empty_cache()
                gc.collect()
            
            logger.info(f"音频转录完成: {audio_path}")
            return result
            
        except Exception as e:
            logger.error(f"音频转录失败 {audio_path}: {e}")
            raise WhisperXError(f"转录失败: {e}")
    
    def batch_transcribe(
        self, 
        audio_paths: List[Union[str, Path]],
        progress_callback: Optional[callable] = None
    ) -> List[Dict]:
        """批量转录音频文件
        
        Args:
            audio_paths: 音频文件路径列表
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict]: 转录结果列表
        """
        results = []
        total_files = len(audio_paths)
        
        for i, audio_path in enumerate(audio_paths):
            try:
                result = self.transcribe_audio(audio_path)
                results.append({
                    "file_path": str(audio_path),
                    "result": result,
                    "status": "success"
                })
                
                # 进度回调
                if progress_callback:
                    progress = (i + 1) / total_files * 100
                    progress_callback(progress, f"已完成 {i+1}/{total_files}")
                    
            except Exception as e:
                logger.error(f"批量转录失败 {audio_path}: {e}")
                results.append({
                    "file_path": str(audio_path),
                    "error": str(e),
                    "status": "failed"
                })
        
        return results
    
    def diarize_speakers(
        self, 
        audio_path: Union[str, Path],
        min_speakers: int = 1,
        max_speakers: int = 10
    ) -> Dict:
        """说话人分离
        
        Args:
            audio_path: 音频文件路径
            min_speakers: 最少说话人数
            max_speakers: 最多说话人数
            
        Returns:
            Dict: 说话人分离结果
        """
        try:
            if not self.diarize_model:
                # 延迟加载说话人分离模型
                self.diarize_model = whisperx.DiarizationPipeline(
                    use_auth_token="YOUR_HF_TOKEN",  # 需要Hugging Face token
                    device=self.device
                )
            
            audio = whisperx.load_audio(str(audio_path))
            
            diarize_segments = self.diarize_model(
                audio,
                min_speakers=min_speakers,
                max_speakers=max_speakers
            )
            
            return diarize_segments
            
        except Exception as e:
            logger.error(f"说话人分离失败 {audio_path}: {e}")
            raise WhisperXError(f"说话人分离失败: {e}")
    
    def extract_segments(
        self, 
        transcription_result: Dict,
        min_duration: float = 1.0,
        max_duration: float = 30.0
    ) -> List[Dict]:
        """提取有效语音段落
        
        Args:
            transcription_result: 转录结果
            min_duration: 最小段落时长（秒）
            max_duration: 最大段落时长（秒）
            
        Returns:
            List[Dict]: 有效段落列表
        """
        segments = []
        
        for segment in transcription_result.get("segments", []):
            duration = segment.get("end", 0) - segment.get("start", 0)
            
            # 过滤段落长度
            if min_duration <= duration <= max_duration:
                # 过滤空白或无意义内容
                text = segment.get("text", "").strip()
                if text and len(text) > 3:
                    segments.append({
                        "start": segment.get("start"),
                        "end": segment.get("end"),
                        "text": text,
                        "duration": duration,
                        "confidence": segment.get("avg_logprob", 0)
                    })
        
        return segments
    
    def optimize_for_asmr(self, audio_path: Union[str, Path]) -> Dict:
        """ASMR音频优化处理
        
        针对ASMR低音量、长时间音频的特殊处理
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            Dict: 优化后的转录结果
        """
        try:
            # ASMR特殊参数
            asmr_config = {
                "batch_size": 8,  # 减小批次大小
                "beam_size": 1,   # 减少beam搜索
                "temperature": 0.0,  # 降低随机性
                "compression_ratio_threshold": 2.4,  # 调整压缩比阈值
                "logprob_threshold": -1.0,  # 调整概率阈值
                "no_speech_threshold": 0.6   # 调整静音检测阈值
            }
            
            audio = whisperx.load_audio(str(audio_path))
            
            # 音频预处理（针对ASMR）
            audio = self.audio_processor.normalize_for_asmr(audio)
            
            # 使用ASMR优化参数转录
            result = self.model.transcribe(
                audio,
                **asmr_config,
                language=self.language
            )
            
            # 后处理：过滤低置信度片段
            filtered_segments = []
            for segment in result.get("segments", []):
                if segment.get("avg_logprob", -2.0) > -1.5:  # 置信度阈值
                    filtered_segments.append(segment)
            
            result["segments"] = filtered_segments
            
            return result
            
        except Exception as e:
            logger.error(f"ASMR音频处理失败 {audio_path}: {e}")
            raise WhisperXError(f"ASMR处理失败: {e}")
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            "model_size": self.model_size,
            "device": self.device,
            "compute_type": self.compute_type,
            "batch_size": self.batch_size,
            "language": self.language,
            "model_loaded": self.model is not None,
            "align_model_loaded": self.align_model is not None
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.model:
                del self.model
            if self.align_model:
                del self.align_model
            if self.diarize_model:
                del self.diarize_model
            
            if self.device == "cuda":
                torch.cuda.empty_cache()
            
            gc.collect()
            logger.info("WhisperX资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
```

## 🎛️ 高级配置选项

### 模型选择策略
```python
MODEL_CONFIGS = {
    "tiny": {
        "size": "39MB",
        "speed": "32x",
        "accuracy": "低",
        "use_case": "快速测试"
    },
    "base": {
        "size": "74MB", 
        "speed": "16x",
        "accuracy": "中等",
        "use_case": "平衡性能"
    },
    "small": {
        "size": "244MB",
        "speed": "6x", 
        "accuracy": "良好",
        "use_case": "一般应用"
    },
    "medium": {
        "size": "769MB",
        "speed": "2x",
        "accuracy": "很好", 
        "use_case": "高质量需求"
    },
    "large": {
        "size": "1550MB",
        "speed": "1x",
        "accuracy": "最佳",
        "use_case": "最高质量"
    },
    "large-v2": {
        "size": "1550MB",
        "speed": "1x", 
        "accuracy": "最佳+",
        "use_case": "生产环境推荐"
    }
}

def select_optimal_model(
    audio_duration: float,
    quality_requirement: str,
    available_memory: float
) -> str:
    """根据需求选择最优模型"""
    if available_memory < 2:  # GB
        return "tiny" if quality_requirement == "fast" else "base"
    elif available_memory < 4:
        return "small" if quality_requirement == "balanced" else "medium"
    else:
        return "large-v2" if quality_requirement == "best" else "large"
```

### GPU内存优化
```python
class GPUMemoryManager:
    """GPU内存管理器"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    def get_available_memory(self) -> float:
        """获取可用GPU内存（GB）"""
        if not torch.cuda.is_available():
            return 0.0
        
        total_memory = torch.cuda.get_device_properties(0).total_memory
        allocated_memory = torch.cuda.memory_allocated(0)
        available_memory = (total_memory - allocated_memory) / 1024**3
        
        return available_memory
    
    def optimize_batch_size(self, model_size: str, audio_length: float) -> int:
        """根据GPU内存优化批次大小"""
        available_memory = self.get_available_memory()
        
        # 模型内存需求估算（GB）
        model_memory_requirements = {
            "tiny": 0.5,
            "base": 1.0,
            "small": 2.0,
            "medium": 3.0,
            "large": 4.0,
            "large-v2": 4.5
        }
        
        model_memory = model_memory_requirements.get(model_size, 4.0)
        
        # 计算最优批次大小
        if available_memory < model_memory + 1:
            return 1  # 最小批次
        elif available_memory < model_memory + 3:
            return 4
        elif available_memory < model_memory + 6:
            return 8
        else:
            return 16
    
    def clear_cache(self):
        """清理GPU缓存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
```

## 🔍 错误处理和调试

### 常见错误处理
```python
class WhisperXErrorHandler:
    """WhisperX错误处理器"""
    
    @staticmethod
    def handle_model_loading_error(error: Exception) -> str:
        """处理模型加载错误"""
        error_msg = str(error).lower()
        
        if "out of memory" in error_msg:
            return "GPU内存不足，请尝试使用更小的模型或减少批次大小"
        elif "model not found" in error_msg:
            return "模型文件未找到，请检查模型路径或重新下载"
        elif "cuda" in error_msg and "not available" in error_msg:
            return "CUDA不可用，将自动切换到CPU模式"
        else:
            return f"模型加载失败: {error}"
    
    @staticmethod
    def handle_transcription_error(error: Exception, audio_path: str) -> str:
        """处理转录错误"""
        error_msg = str(error).lower()
        
        if "audio file" in error_msg:
            return f"音频文件格式不支持或损坏: {audio_path}"
        elif "timeout" in error_msg:
            return f"转录超时，音频文件可能过长: {audio_path}"
        elif "memory" in error_msg:
            return f"内存不足，请尝试分段处理: {audio_path}"
        else:
            return f"转录失败: {error}"

# 使用装饰器进行错误处理
def handle_whisperx_errors(func):
    """WhisperX错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if "model" in func.__name__:
                error_msg = WhisperXErrorHandler.handle_model_loading_error(e)
            elif "transcribe" in func.__name__:
                audio_path = args[1] if len(args) > 1 else "unknown"
                error_msg = WhisperXErrorHandler.handle_transcription_error(e, audio_path)
            else:
                error_msg = f"操作失败: {e}"
            
            logger.error(error_msg)
            raise WhisperXError(error_msg)
    
    return wrapper
```

## 📊 性能监控和优化

### 性能指标监控
```python
import time
import psutil
from dataclasses import dataclass

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    processing_time: float
    gpu_memory_used: float
    cpu_usage: float
    accuracy_score: float
    throughput: float  # 音频时长/处理时长

class WhisperXPerformanceMonitor:
    """WhisperX性能监控器"""
    
    def __init__(self):
        self.metrics_history = []
    
    def monitor_transcription(self, func):
        """监控转录性能的装饰器"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = self._get_gpu_memory()
            start_cpu = psutil.cpu_percent()
            
            result = func(*args, **kwargs)
            
            end_time = time.time()
            end_memory = self._get_gpu_memory()
            end_cpu = psutil.cpu_percent()
            
            # 计算性能指标
            processing_time = end_time - start_time
            memory_used = end_memory - start_memory
            cpu_usage = (start_cpu + end_cpu) / 2
            
            # 估算音频时长（如果可获取）
            audio_duration = self._estimate_audio_duration(args, kwargs)
            throughput = audio_duration / processing_time if processing_time > 0 else 0
            
            metrics = PerformanceMetrics(
                processing_time=processing_time,
                gpu_memory_used=memory_used,
                cpu_usage=cpu_usage,
                accuracy_score=self._estimate_accuracy(result),
                throughput=throughput
            )
            
            self.metrics_history.append(metrics)
            self._log_performance(metrics)
            
            return result
        
        return wrapper
    
    def _get_gpu_memory(self) -> float:
        """获取GPU内存使用量（GB）"""
        try:
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / 1024**3
        except:
            pass
        return 0.0
    
    def _estimate_audio_duration(self, args, kwargs) -> float:
        """估算音频时长"""
        # 这里可以根据实际情况实现音频时长获取
        return 60.0  # 默认60秒
    
    def _estimate_accuracy(self, result: dict) -> float:
        """估算识别准确率"""
        if not result or "segments" not in result:
            return 0.0
        
        # 基于平均对数概率估算准确率
        total_logprob = 0
        segment_count = 0
        
        for segment in result["segments"]:
            if "avg_logprob" in segment:
                total_logprob += segment["avg_logprob"]
                segment_count += 1
        
        if segment_count == 0:
            return 0.0
        
        avg_logprob = total_logprob / segment_count
        # 将对数概率转换为0-1的准确率估算
        accuracy = max(0, min(1, (avg_logprob + 2) / 2))
        
        return accuracy
    
    def _log_performance(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        logger.info(
            f"性能指标 - "
            f"处理时间: {metrics.processing_time:.2f}s, "
            f"GPU内存: {metrics.gpu_memory_used:.2f}GB, "
            f"CPU使用率: {metrics.cpu_usage:.1f}%, "
            f"准确率估算: {metrics.accuracy_score:.2f}, "
            f"吞吐量: {metrics.throughput:.2f}x"
        )
    
    def get_average_metrics(self) -> PerformanceMetrics:
        """获取平均性能指标"""
        if not self.metrics_history:
            return PerformanceMetrics(0, 0, 0, 0, 0)
        
        count = len(self.metrics_history)
        return PerformanceMetrics(
            processing_time=sum(m.processing_time for m in self.metrics_history) / count,
            gpu_memory_used=sum(m.gpu_memory_used for m in self.metrics_history) / count,
            cpu_usage=sum(m.cpu_usage for m in self.metrics_history) / count,
            accuracy_score=sum(m.accuracy_score for m in self.metrics_history) / count,
            throughput=sum(m.throughput for m in self.metrics_history) / count
        )
```

这个WhisperX集成知识文档提供了完整的技术实现方案，涵盖了从基础集成到高级优化的所有方面，为Voice-came全栈开发工程师提供了详细的技术指导。 