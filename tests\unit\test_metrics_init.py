#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Metrics模块初始化文件测试
"""

import sys
import os
import pytest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

import voice_came.metrics as metrics_module


class TestMetricsModuleImports:
    """测试metrics模块的导入功能"""
    
    def test_module_has_all_exports(self):
        """测试模块包含所有预期的导出"""
        expected_exports = [
            'TDDMetricsCollector',
            'CoverageMonitor', 
            'CycleTimeTracker',
            'QualityAnalyzer',
            'ComplianceChecker',
            'PerformanceTracker',
            'MetricsReporter'
        ]
        
        # 检查__all__列表
        assert hasattr(metrics_module, '__all__')
        assert metrics_module.__all__ == expected_exports
        
        # 检查每个导出是否真正可用
        for export_name in expected_exports:
            assert hasattr(metrics_module, export_name)
            exported_class = getattr(metrics_module, export_name)
            assert exported_class is not None
            # 应该是一个类
            assert isinstance(exported_class, type)
    
    def test_version_exists(self):
        """测试版本号存在"""
        assert hasattr(metrics_module, '__version__')
        assert metrics_module.__version__ == '1.0.0'
        assert isinstance(metrics_module.__version__, str)
    
    def test_module_docstring(self):
        """测试模块文档字符串"""
        assert metrics_module.__doc__ is not None
        assert "TDD度量体系模块" in metrics_module.__doc__
        assert "测试覆盖率监控" in metrics_module.__doc__
        assert "Red-Green-Refactor" in metrics_module.__doc__
    
    def test_individual_class_imports(self):
        """测试各个类可以单独导入"""
        from voice_came.metrics import TDDMetricsCollector
        from voice_came.metrics import CoverageMonitor
        from voice_came.metrics import CycleTimeTracker
        from voice_came.metrics import QualityAnalyzer
        from voice_came.metrics import ComplianceChecker
        from voice_came.metrics import PerformanceTracker
        from voice_came.metrics import MetricsReporter
        
        # 检查导入的类确实是类型
        assert isinstance(TDDMetricsCollector, type)
        assert isinstance(CoverageMonitor, type)
        assert isinstance(CycleTimeTracker, type)
        assert isinstance(QualityAnalyzer, type)
        assert isinstance(ComplianceChecker, type)
        assert isinstance(PerformanceTracker, type)
        assert isinstance(MetricsReporter, type)
    
    def test_all_classes_instantiable(self):
        """测试所有类都可以实例化（使用默认参数）"""
        # TDDMetricsCollector
        collector = metrics_module.TDDMetricsCollector()
        assert collector is not None
        
        # CoverageMonitor  
        coverage_monitor = metrics_module.CoverageMonitor()
        assert coverage_monitor is not None
        
        # CycleTimeTracker
        cycle_tracker = metrics_module.CycleTimeTracker()
        assert cycle_tracker is not None
        
        # QualityAnalyzer
        quality_analyzer = metrics_module.QualityAnalyzer()
        assert quality_analyzer is not None
        
        # ComplianceChecker
        compliance_checker = metrics_module.ComplianceChecker()
        assert compliance_checker is not None
        
        # PerformanceTracker
        performance_tracker = metrics_module.PerformanceTracker()
        assert performance_tracker is not None
        
        # MetricsReporter
        metrics_reporter = metrics_module.MetricsReporter()
        assert metrics_reporter is not None
    
    def test_star_import(self):
        """测试星号导入功能"""
        # 创建一个新的命名空间来测试星号导入
        namespace = {}
        exec("from voice_came.metrics import *", namespace)
        
        # 检查所有预期的类都被导入
        expected_exports = [
            'TDDMetricsCollector',
            'CoverageMonitor', 
            'CycleTimeTracker',
            'QualityAnalyzer',
            'ComplianceChecker',
            'PerformanceTracker',
            'MetricsReporter'
        ]
        
        for export_name in expected_exports:
            assert export_name in namespace
            assert isinstance(namespace[export_name], type)
    
    def test_module_attributes(self):
        """测试模块属性"""
        # 检查模块名
        assert metrics_module.__name__ == 'voice_came.metrics'
        
        # 检查包路径
        assert hasattr(metrics_module, '__path__')
        
        # 检查文件路径
        assert hasattr(metrics_module, '__file__')
        assert metrics_module.__file__.endswith('__init__.py')


class TestMetricsModuleClasses:
    """测试metrics模块中各个类的基本属性"""
    
    def test_tdd_metrics_collector_properties(self):
        """测试TDDMetricsCollector类属性"""
        collector = metrics_module.TDDMetricsCollector()
        
        # 检查基本属性存在
        assert hasattr(collector, 'metrics_dir')
        assert hasattr(collector, 'metrics_file')
        assert hasattr(collector, 'collect_current_metrics')
        
        # 检查方法是可调用的
        assert callable(collector.collect_current_metrics)
    
    def test_coverage_monitor_properties(self):
        """测试CoverageMonitor类属性"""
        monitor = metrics_module.CoverageMonitor()
        
        # 检查基本属性存在
        assert hasattr(monitor, 'src_dir')
        assert hasattr(monitor, 'coverage_file')
        assert hasattr(monitor, 'get_current_coverage')
        
        # 检查方法是可调用的
        assert callable(monitor.get_current_coverage)
    
    def test_cycle_time_tracker_properties(self):
        """测试CycleTimeTracker类属性"""
        tracker = metrics_module.CycleTimeTracker()
        
        # 检查基本属性存在
        assert hasattr(tracker, 'get_cycle_statistics')
        
        # 检查方法是可调用的
        assert callable(tracker.get_cycle_statistics)
    
    def test_quality_analyzer_properties(self):
        """测试QualityAnalyzer类属性"""
        analyzer = metrics_module.QualityAnalyzer()
        
        # 检查基本属性存在
        assert hasattr(analyzer, 'src_dir')
        assert hasattr(analyzer, 'analyze_code_quality')
        
        # 检查方法是可调用的
        assert callable(analyzer.analyze_code_quality)
    
    def test_compliance_checker_properties(self):
        """测试ComplianceChecker类属性"""
        checker = metrics_module.ComplianceChecker()
        
        # 检查基本属性存在
        assert hasattr(checker, 'check_tdd_compliance')
        
        # 检查方法是可调用的
        assert callable(checker.check_tdd_compliance)
    
    def test_performance_tracker_properties(self):
        """测试PerformanceTracker类属性"""
        tracker = metrics_module.PerformanceTracker()
        
        # 检查基本属性存在
        assert hasattr(tracker, 'get_performance_score')
        
        # 检查方法是可调用的
        assert callable(tracker.get_performance_score)
    
    def test_metrics_reporter_properties(self):
        """测试MetricsReporter类属性"""
        reporter = metrics_module.MetricsReporter()
        
        # 检查基本属性存在
        assert hasattr(reporter, 'generate_daily_report')
        
        # 检查方法是可调用的
        assert callable(reporter.generate_daily_report)


class TestMetricsModuleEdgeCases:
    """测试metrics模块的边界情况"""
    
    def test_module_reload(self):
        """测试模块重新加载"""
        import importlib
        
        # 重新加载模块
        reloaded_module = importlib.reload(metrics_module)
        
        # 检查重新加载后的模块
        assert reloaded_module is not None
        assert hasattr(reloaded_module, '__all__')
        assert hasattr(reloaded_module, '__version__')
    
    def test_class_inheritance(self):
        """测试类继承关系"""
        # 所有的metrics类都应该是object的子类
        for class_name in metrics_module.__all__:
            cls = getattr(metrics_module, class_name)
            assert issubclass(cls, object)
    
    def test_class_names_consistency(self):
        """测试类名与__all__的一致性"""
        for class_name in metrics_module.__all__:
            # 类名应该存在于模块中
            assert hasattr(metrics_module, class_name)
            
            # 获取的对象应该是一个类
            cls = getattr(metrics_module, class_name)
            assert isinstance(cls, type)
            
            # 类的名称应该与__all__中的名称一致
            assert cls.__name__ == class_name
    
    def test_no_extra_exports(self):
        """测试没有额外的导出"""
        # 获取所有公共属性（不以下划线开头的）
        public_attrs = [attr for attr in dir(metrics_module) 
                       if not attr.startswith('_')]
        
        # 过滤掉已知的模块属性
        known_attrs = set(metrics_module.__all__ + ['__version__'])
        
        # 检查是否有未在__all__中声明的公共属性
        extra_attrs = set(public_attrs) - known_attrs
        
        # 应该没有额外的公共属性（或者只有预期的）
        # 允许模块文件名（不带下划线的模块名）
        allowed_extra = {
            'compliance_checker', 'cycle_time_tracker', 'performance_tracker',
            'quality_analyzer', 'coverage_monitor', 'metrics_reporter', 'tdd_metrics_collector'
        }
        
        unexpected_attrs = extra_attrs - allowed_extra
        assert len(unexpected_attrs) == 0, f"发现未预期的公共属性: {unexpected_attrs}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 