[build-system]
requires = ["setuptools>=64", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "voice-came"
version = "0.1.0"
description = "Voice-came 语音翻译系统 - 基于WhisperX和Gemma3-12B-Q4"
readme = "README.md"
license = {file = "LICENSE"}
authors = [{name = "Voice-came Team"}]
requires-python = ">=3.8,<3.12"
dependencies = [
    "whisperx>=3.1.1",
    "torch>=2.0.0",
    "transformers>=4.30.0",
    "librosa>=0.10.0",
    "pydub>=0.25.1",
    "numpy>=1.24.0",
    "pyyaml>=6.0",
    "loguru>=0.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.1",
    "pytest-asyncio>=0.21.1",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.4.0",
    "isort>=5.12.0",
    "pre-commit>=3.3.0",
    "coverage>=7.2.0",
    "bandit>=1.7.5",
]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    ".venv",
    "venv",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "whisperx.*",
    "librosa.*",
    "pydub.*",
    "torch.*",
    "transformers.*",
]
ignore_missing_imports = true

[tool.bandit]
exclude_dirs = ["tests", "test_*.py", "*_test.py"]
skips = ["B101", "B601"]

[tool.coverage.run]
source = ["src/voice_came"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]
branch = true

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = false
fail_under = 90
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
] 