"""
代码质量分析器

分析代码质量指标，包括复杂度、重复率、技术债务等
"""

import subprocess
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
import ast
import radon.complexity as cc
import radon.metrics as metrics


class QualityAnalyzer:
    """代码质量分析器"""
    
    def __init__(self, src_dir: str = "src/voice_came"):
        self.src_dir = Path(src_dir)
        self.logger = logging.getLogger(__name__)
        
        # 质量标准阈值
        self.quality_thresholds = {
            'complexity_max': 10,        # 最大圈复杂度
            'maintainability_min': 60,   # 最小维护性指数
            'duplication_max': 0.1,      # 最大重复率 10%
            'tech_debt_hours_max': 8,    # 最大技术债务小时数
            'code_smell_max': 5          # 最大代码异味数量
        }
    
    def analyze_code_quality(self) -> float:
        """分析代码质量，返回综合评分(0-100)"""
        try:
            # 获取各项质量指标
            complexity_score = self._analyze_complexity()
            maintainability_score = self._analyze_maintainability()
            duplication_score = self._analyze_duplication()
            style_score = self._analyze_code_style()
            security_score = self._analyze_security()
            
            # 计算加权平均分
            weights = {
                'complexity': 0.25,
                'maintainability': 0.25,
                'duplication': 0.15,
                'style': 0.20,
                'security': 0.15
            }
            
            total_score = (
                complexity_score * weights['complexity'] +
                maintainability_score * weights['maintainability'] +
                duplication_score * weights['duplication'] +
                style_score * weights['style'] +
                security_score * weights['security']
            )
            
            self.logger.info(f"代码质量综合评分: {total_score:.1f}")
            return total_score
            
        except Exception as e:
            self.logger.error(f"分析代码质量时出错: {e}")
            return 0.0
    
    def _analyze_complexity(self) -> float:
        """分析代码复杂度"""
        try:
            if not self.src_dir.exists():
                return 100.0
            
            total_complexity = 0
            file_count = 0
            
            for py_file in self.src_dir.rglob("*.py"):
                if py_file.name.startswith('test_'):
                    continue
                    
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 计算圈复杂度
                    complexity_blocks = cc.cc_visit(content)
                    
                    for block in complexity_blocks:
                        total_complexity += block.complexity
                    
                    file_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"分析文件 {py_file} 复杂度时出错: {e}")
            
            if file_count == 0:
                return 100.0
            
            avg_complexity = total_complexity / file_count
            
            # 转换为0-100分数
            if avg_complexity <= self.quality_thresholds['complexity_max']:
                return 100.0
            else:
                # 复杂度超标，分数递减
                penalty = (avg_complexity - self.quality_thresholds['complexity_max']) * 10
                return max(0, 100 - penalty)
                
        except Exception as e:
            self.logger.error(f"分析复杂度时出错: {e}")
            return 50.0
    
    def _analyze_maintainability(self) -> float:
        """分析代码可维护性"""
        try:
            if not self.src_dir.exists():
                return 100.0
            
            total_mi = 0
            file_count = 0
            
            for py_file in self.src_dir.rglob("*.py"):
                if py_file.name.startswith('test_'):
                    continue
                    
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 计算可维护性指数
                    mi = metrics.mi_visit(content, multi=True)
                    total_mi += mi
                    file_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"分析文件 {py_file} 可维护性时出错: {e}")
            
            if file_count == 0:
                return 100.0
            
            avg_mi = total_mi / file_count
            
            # 转换为0-100分数
            if avg_mi >= self.quality_thresholds['maintainability_min']:
                return 100.0
            else:
                # 可维护性低于标准
                return (avg_mi / self.quality_thresholds['maintainability_min']) * 100
                
        except Exception as e:
            self.logger.error(f"分析可维护性时出错: {e}")
            return 50.0
    
    def _analyze_duplication(self) -> float:
        """分析代码重复率"""
        try:
            # 使用简单的行重复检测
            all_lines = []
            total_lines = 0
            
            for py_file in self.src_dir.rglob("*.py"):
                if py_file.name.startswith('test_'):
                    continue
                    
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    # 过滤空行和注释行
                    code_lines = [
                        line.strip() for line in lines 
                        if line.strip() and not line.strip().startswith('#')
                    ]
                    
                    all_lines.extend(code_lines)
                    total_lines += len(code_lines)
                    
                except Exception as e:
                    self.logger.warning(f"分析文件 {py_file} 重复率时出错: {e}")
            
            if total_lines == 0:
                return 100.0
            
            # 计算重复行数
            unique_lines = set(all_lines)
            duplicate_lines = total_lines - len(unique_lines)
            duplication_rate = duplicate_lines / total_lines
            
            # 转换为0-100分数
            if duplication_rate <= self.quality_thresholds['duplication_max']:
                return 100.0
            else:
                penalty = (duplication_rate - self.quality_thresholds['duplication_max']) * 500
                return max(0, 100 - penalty)
                
        except Exception as e:
            self.logger.error(f"分析重复率时出错: {e}")
            return 50.0
    
    def _analyze_code_style(self) -> float:
        """分析代码风格"""
        try:
            # 使用flake8检查代码风格
            result = subprocess.run([
                "flake8", str(self.src_dir), "--count", "--statistics"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                return 100.0  # 无风格问题
            
            # 解析错误数量
            error_count = 0
            lines = result.stdout.split('\n')
            
            for line in lines:
                if line.strip() and line[0].isdigit():
                    error_count += int(line.split()[0])
            
            # 计算代码行数
            total_lines = self._count_code_lines()
            
            if total_lines == 0:
                return 100.0
            
            # 计算错误率
            error_rate = error_count / total_lines
            
            # 转换为分数
            if error_rate <= 0.01:  # 错误率小于1%
                return 100.0
            else:
                penalty = error_rate * 1000
                return max(0, 100 - penalty)
                
        except Exception as e:
            self.logger.error(f"分析代码风格时出错: {e}")
            return 50.0
    
    def _analyze_security(self) -> float:
        """分析代码安全性"""
        try:
            # 使用bandit检查安全问题
            result = subprocess.run([
                "bandit", "-r", str(self.src_dir), "-f", "json"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                return 100.0  # 无安全问题
            
            try:
                bandit_output = json.loads(result.stdout)
                
                # 统计不同严重程度的问题
                high_severity = len([r for r in bandit_output.get('results', []) if r.get('issue_severity') == 'HIGH'])
                medium_severity = len([r for r in bandit_output.get('results', []) if r.get('issue_severity') == 'MEDIUM'])
                low_severity = len([r for r in bandit_output.get('results', []) if r.get('issue_severity') == 'LOW'])
                
                # 计算安全评分
                security_penalty = high_severity * 20 + medium_severity * 10 + low_severity * 5
                
                return max(0, 100 - security_penalty)
                
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回中等分数
                return 70.0
                
        except Exception as e:
            self.logger.error(f"分析安全性时出错: {e}")
            return 50.0
    
    def _count_code_lines(self) -> int:
        """统计代码行数"""
        total_lines = 0
        
        for py_file in self.src_dir.rglob("*.py"):
            if py_file.name.startswith('test_'):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 只计算非空非注释行
                code_lines = [
                    line for line in lines 
                    if line.strip() and not line.strip().startswith('#')
                ]
                
                total_lines += len(code_lines)
                
            except Exception as e:
                self.logger.warning(f"统计文件 {py_file} 行数时出错: {e}")
        
        return total_lines
    
    def get_detailed_quality_report(self) -> Dict[str, Any]:
        """获取详细的质量报告"""
        try:
            report = {
                'overall_score': self.analyze_code_quality(),
                'complexity_analysis': self._get_complexity_details(),
                'maintainability_analysis': self._get_maintainability_details(),
                'duplication_analysis': self._get_duplication_details(),
                'style_analysis': self._get_style_details(),
                'security_analysis': self._get_security_details(),
                'recommendations': self._generate_quality_recommendations()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成质量报告时出错: {e}")
            return {'error': '无法生成质量报告'}
    
    def _get_complexity_details(self) -> Dict[str, Any]:
        """获取复杂度详细信息"""
        try:
            complex_functions = []
            
            for py_file in self.src_dir.rglob("*.py"):
                if py_file.name.startswith('test_'):
                    continue
                    
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    complexity_blocks = cc.cc_visit(content)
                    
                    for block in complexity_blocks:
                        if block.complexity > self.quality_thresholds['complexity_max']:
                            complex_functions.append({
                                'file': str(py_file.relative_to(self.src_dir)),
                                'function': block.name,
                                'complexity': block.complexity,
                                'line': block.lineno
                            })
                            
                except Exception as e:
                    self.logger.warning(f"分析文件 {py_file} 复杂度详情时出错: {e}")
            
            return {
                'high_complexity_functions': complex_functions,
                'total_complex_functions': len(complex_functions)
            }
            
        except Exception as e:
            self.logger.error(f"获取复杂度详情时出错: {e}")
            return {}
    
    def _get_maintainability_details(self) -> Dict[str, Any]:
        """获取可维护性详细信息"""
        # 简化实现
        return {
            'low_maintainability_files': [],
            'average_maintainability': 70.0
        }
    
    def _get_duplication_details(self) -> Dict[str, Any]:
        """获取重复代码详细信息"""
        # 简化实现
        return {
            'duplicate_blocks': [],
            'duplication_percentage': 5.0
        }
    
    def _get_style_details(self) -> Dict[str, Any]:
        """获取代码风格详细信息"""
        # 简化实现
        return {
            'style_violations': [],
            'total_violations': 0
        }
    
    def _get_security_details(self) -> Dict[str, Any]:
        """获取安全问题详细信息"""
        # 简化实现
        return {
            'security_issues': [],
            'total_issues': 0
        }
    
    def _generate_quality_recommendations(self) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        try:
            score = self.analyze_code_quality()
            
            if score < 80:
                recommendations.append("代码质量需要改进，建议进行全面重构")
            
            # 根据具体问题生成建议
            complexity_score = self._analyze_complexity()
            if complexity_score < 70:
                recommendations.append("函数复杂度过高，建议分解复杂函数")
            
            style_score = self._analyze_code_style()
            if style_score < 80:
                recommendations.append("代码风格不规范，建议运行 black 和 isort 格式化代码")
            
            security_score = self._analyze_security()
            if security_score < 80:
                recommendations.append("存在安全风险，建议修复 bandit 发现的安全问题")
            
        except Exception as e:
            self.logger.error(f"生成质量建议时出错: {e}")
            recommendations.append("无法分析代码质量，请检查代码结构")
        
        return recommendations 