# Voice-came TDD开发指南

## 简介

本项目采用测试驱动开发（TDD）模式，确保代码质量和功能的正确性。本文档将指导您如何在Voice-came项目中进行TDD开发。

## TDD基础设施

### 已配置的工具

- **pytest**: 测试框架
- **pytest-cov**: 覆盖率检查
- **black**: 代码格式化
- **flake8**: 代码风格检查
- **mypy**: 类型检查
- **bandit**: 安全检查
- **pre-commit**: 提交前检查
- **GitHub Actions**: CI/CD流水线

### 项目结构

```
Voice-came/
├── src/voice_came/          # 主要源代码
├── tests/                   # 测试目录
│   ├── unit/               # 单元测试
│   ├── integration/        # 集成测试
│   ├── data/              # 测试数据
│   ├── conftest.py        # pytest配置
│   └── __init__.py
├── configs/test_env.yaml   # 测试环境配置
├── pytest.ini             # pytest配置
├── .coveragerc            # 覆盖率配置
├── .pre-commit-config.yaml # pre-commit配置
├── pyproject.toml         # 项目配置
└── Makefile              # 开发命令
```

## TDD工作流

### 1. 环境设置

```bash
# 安装依赖并设置开发环境
make setup

# 或者手动安装
pip install -r requirements.txt
pip install -e .[dev]
pre-commit install
```

### 2. TDD循环

#### 基本TDD循环（红-绿-重构）

1. **红色阶段**: 编写失败的测试
2. **绿色阶段**: 编写最少代码使测试通过
3. **重构阶段**: 优化代码同时保持测试通过

#### 快速TDD命令

```bash
# 完整TDD循环
make tdd-cycle

# 或单步执行
make test-fast    # 运行快速测试
make check        # 代码质量检查
make format       # 代码格式化
```

### 3. 测试命令

```bash
# 运行所有测试
make test

# 运行不同类型的测试
make test-unit          # 单元测试
make test-integration   # 集成测试
make test-fast         # 快速测试（跳过慢测试）
make test-slow         # 慢测试

# 生成覆盖率报告
make coverage
```

### 4. 代码质量检查

```bash
# 完整检查
make check

# 单项检查
make lint        # 代码风格
make type-check  # 类型检查
make security    # 安全检查
```

## 测试编写指南

### 测试分类

使用pytest标记来分类测试：

```python
import pytest

@pytest.mark.unit
def test_audio_processing():
    """单元测试"""
    pass

@pytest.mark.integration
def test_whisperx_integration():
    """集成测试"""
    pass

@pytest.mark.slow
def test_large_file_processing():
    """慢测试"""
    pass

@pytest.mark.api
def test_api_endpoint():
    """API测试"""
    pass

@pytest.mark.interface
def test_translation_interface():
    """接口合同测试"""
    pass
```

### 测试文件命名

- 单元测试: `tests/unit/test_<module_name>.py`
- 集成测试: `tests/integration/test_<feature_name>.py`
- 测试文件必须以`test_`开头或`_test.py`结尾

### 使用Fixtures

```python
def test_speech_recognition(mock_whisperx_model, sample_audio_file):
    """使用预定义的fixtures"""
    # mock_whisperx_model 和 sample_audio_file 在 conftest.py 中定义
    pass
```

### 测试示例

```python
import pytest
from voice_came.speech_recognition import SpeechRecognizer
from voice_came.translation.integration import TranslationIntegrator

class TestSpeechRecognizer:
    """语音识别器测试类"""
    
    @pytest.mark.unit
    def test_init(self):
        """测试初始化"""
        recognizer = SpeechRecognizer()
        assert recognizer is not None
    
    @pytest.mark.unit
    def test_preprocess_audio(self, sample_audio_file):
        """测试音频预处理"""
        recognizer = SpeechRecognizer()
        result = recognizer.preprocess_audio(sample_audio_file)
        assert result is not None
    
    @pytest.mark.integration
    def test_recognize_speech(self, mock_whisperx_model, sample_audio_file):
        """测试语音识别集成"""
        recognizer = SpeechRecognizer(model=mock_whisperx_model)
        result = recognizer.recognize(sample_audio_file)
        assert "text" in result

class TestTranslationIntegrator:
    """翻译集成器测试类"""
    
    @pytest.mark.unit
    def test_init(self):
        """测试翻译集成器初始化"""
        integrator = TranslationIntegrator()
        assert integrator is not None
    
    @pytest.mark.interface
    def test_voicetransl_integration(self, mock_voicetransl_adapter):
        """测试VoiceTransl集成接口"""
        integrator = TranslationIntegrator(adapter=mock_voicetransl_adapter)
        result = integrator.translate("Hello", "zh", "en")
        assert "translated_text" in result
    
    @pytest.mark.integration
    def test_local_model_integration(self, mock_local_model):
        """测试本地模型集成"""
        integrator = TranslationIntegrator(local_model=mock_local_model)
        result = integrator.translate_with_fallback("Hello", "zh", "en")
        assert result["success"] is True
```

## 覆盖率要求

- 目标覆盖率: **90%+**
- 分支覆盖率: 启用
- 覆盖率失败阈值: 90%

查看覆盖率报告：
```bash
make coverage
# 在浏览器中打开 htmlcov/index.html
```

## Pre-commit检查

每次提交前会自动运行：
- 代码格式化（black, isort）
- 代码风格检查（flake8）
- 类型检查（mypy）
- 安全检查（bandit）
- 基本测试

手动运行：
```bash
make pre-commit
```

## CI/CD流水线

GitHub Actions会在以下情况触发：
- Push到main/master/develop分支
- 创建Pull Request

流水线包括：
- 多Python版本测试（3.8-3.11）
- 多操作系统测试（Ubuntu, Windows, macOS）
- 代码质量检查
- 覆盖率报告上传到Codecov

## 最佳实践

### 1. 测试命名

```python
def test_should_return_text_when_audio_is_valid():
    """测试名称应该描述期望行为"""
    pass
```

### 2. 测试结构（AAA模式）

```python
def test_audio_processing():
    # Arrange - 准备
    audio_file = "test.wav"
    processor = AudioProcessor()
    
    # Act - 执行
    result = processor.process(audio_file)
    
    # Assert - 断言
    assert result.is_processed
    assert result.duration > 0
```

### 3. Mock外部依赖

```python
@pytest.mark.unit
def test_translation_service(mock_translation_model):
    """使用mock对象避免真实API调用"""
    translator = Translator(model=mock_translation_model)
    result = translator.translate("Hello")
    assert result == "你好"

@pytest.mark.interface
def test_voicetransl_adapter(mock_voicetransl_system):
    """测试VoiceTransl适配器接口"""
    from voice_came.translation.integration import VoiceTranslAdapter
    
    adapter = VoiceTranslAdapter(system=mock_voicetransl_system)
    result = adapter.execute_translation("test input", "zh", "en")
    
    # 验证接口合同
    assert "translated_text" in result
    assert "confidence_score" in result
    assert result["success"] is True
```

### 4. 测试数据管理

- 使用`tests/data/`目录存储测试数据
- 保持测试数据小而快
- 对于大文件，使用mock或生成临时数据

## 开发工作流建议

### 日常开发

1. 开始新功能前运行测试确保当前代码正常：
   ```bash
   make test-fast
   ```

2. 采用TDD循环开发新功能：
   - 写测试 → 运行测试（应该失败）
   - 写代码 → 运行测试（应该通过）
   - 重构 → 运行测试（保持通过）

3. 提交前运行完整检查：
   ```bash
   make tdd-cycle
   ```

### 调试测试

```bash
# 运行特定测试
pytest tests/unit/test_speech_recognition.py::test_init -v

# 调试模式
pytest --pdb tests/unit/test_speech_recognition.py

# 只运行失败的测试
pytest --lf
```

## 故障排除

### 常见问题

1. **覆盖率不足**：
   - 检查哪些代码未覆盖：`make coverage`
   - 添加相应测试用例

2. **测试运行慢**：
   - 使用`make test-fast`跳过慢测试
   - 为慢测试添加`@pytest.mark.slow`标记

3. **依赖问题**：
   - 重新安装依赖：`make install`
   - 检查Python版本兼容性

4. **Pre-commit失败**：
   - 运行`make format`自动修复格式问题
   - 手动修复其他质量问题

## 参考资料

- [pytest文档](https://docs.pytest.org/)
- [TDD最佳实践](https://testdriven.io/blog/modern-tdd/)
- [Python测试指南](https://docs.python-guide.org/writing/tests/)

---

如有问题，请查看项目Issues或联系开发团队。 