#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4.4: 翻译控制面板 (TDD-Green最小实现)

提供翻译功能的用户界面控制面板，包括文件选择、
设置配置和任务管理功能。
"""

import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

from ..translation.models import TranslationJob, TranslationConfig, AudioFile

logger = logging.getLogger(__name__)


@dataclass
class TranslationSettings:
    """翻译设置数据类"""
    source_language: str
    target_language: str
    quality_level: str
    terminology_enabled: bool
    batch_size: int


class TranslationControlPanel:
    """翻译控制面板 - TDD Green最小实现"""
    
    def __init__(self):
        self.audio_files: List[Dict[str, Any]] = []
        self.selected_files: List[str] = []
        self.translation_settings: Optional[TranslationSettings] = None
        self.current_job: Optional[TranslationJob] = None
        self.job_counter = 0
    
    def load_audio_files(self, audio_files: List[Dict[str, Any]]) -> None:
        """加载音频文件列表"""
        # TDD Green: 最小实现，存储文件列表
        self.audio_files = audio_files
        logger.info(f"Loaded {len(audio_files)} audio files")
    
    def get_selected_files(self) -> List[str]:
        """获取选中的文件"""
        # TDD Green: 最小实现，返回选中文件列表
        return self.selected_files
    
    def select_files(self, file_paths: List[str]) -> None:
        """选择文件进行翻译"""
        # TDD Green: 最小实现，设置选中文件
        self.selected_files = file_paths
        logger.info(f"Selected {len(file_paths)} files for translation")
    
    def configure_translation_settings(self, settings: Dict[str, Any]) -> None:
        """配置翻译设置"""
        # TDD Green: 最小实现，存储设置
        self.translation_settings = TranslationSettings(
            source_language=settings.get("source_language", "zh"),
            target_language=settings.get("target_language", "en"),
            quality_level=settings.get("quality_level", "high"),
            terminology_enabled=settings.get("terminology_enabled", True),
            batch_size=settings.get("batch_size", 5)
        )
        logger.info("Translation settings configured")
    
    def get_translation_settings(self) -> Dict[str, Any]:
        """获取当前翻译设置"""
        # TDD Green: 最小实现，返回设置字典
        if not self.translation_settings:
            return {}
        
        return {
            "source_language": self.translation_settings.source_language,
            "target_language": self.translation_settings.target_language,
            "quality_level": self.translation_settings.quality_level,
            "terminology_enabled": self.translation_settings.terminology_enabled,
            "batch_size": self.translation_settings.batch_size
        }
    
    def create_translation_job(self) -> TranslationJob:
        """创建翻译任务"""
        # TDD Green: 最小实现，创建基本任务对象
        if not self.translation_settings:
            raise ValueError("Translation settings not configured")
        
        if not self.selected_files:
            raise ValueError("No files selected for translation")
        
        # 转换文件信息
        audio_files = []
        for file_info in self.audio_files:
            if file_info["path"] in self.selected_files:
                audio_files.append(AudioFile(
                    file_path=file_info["path"],
                    filename=file_info["name"],
                    size=file_info.get("size", 0),
                    duration=file_info.get("duration")
                ))
        
        # 创建翻译配置
        config = TranslationConfig(
            source_language=self.translation_settings.source_language,
            target_language=self.translation_settings.target_language,
            terminology_enabled=self.translation_settings.terminology_enabled,
            quality_optimization=(self.translation_settings.quality_level == "high")
        )
        
        # 创建任务
        self.job_counter += 1
        job = TranslationJob(
            job_id=f"translation_job_{self.job_counter}",
            audio_files=audio_files,
            config=config,
            created_at="2025-01-16T10:00:00Z"
        )
        
        self.current_job = job
        return job
    
    def start_translation(self) -> Optional[str]:
        """启动翻译任务"""
        # TDD Green: 最小实现，返回任务ID
        if not self.current_job:
            try:
                self.current_job = self.create_translation_job()
            except ValueError as e:
                logger.error(f"Failed to create translation job: {e}")
                return None
        
        # 模拟启动翻译
        logger.info(f"Starting translation job: {self.current_job.job_id}")
        return self.current_job.job_id
    
    def stop_translation(self, job_id: str) -> bool:
        """停止翻译任务"""
        # TDD Green: 最小实现，模拟停止操作
        if self.current_job and self.current_job.job_id == job_id:
            logger.info(f"Stopping translation job: {job_id}")
            return True
        
        logger.warning(f"Job {job_id} not found or not active")
        return False
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        # TDD Green: 最小实现，返回基本状态
        if self.current_job and self.current_job.job_id == job_id:
            return {
                "job_id": job_id,
                "status": "running",
                "progress": 0.0,
                "files_processed": 0,
                "total_files": len(self.current_job.audio_files)
            }
        
        return {
            "job_id": job_id,
            "status": "not_found",
            "progress": 0.0,
            "files_processed": 0,
            "total_files": 0
        }
    
    def validate_settings(self) -> Dict[str, List[str]]:
        """验证翻译设置"""
        # TDD Green: 最小实现，基本验证
        errors = []
        warnings = []
        
        if not self.translation_settings:
            errors.append("Translation settings not configured")
        
        if not self.selected_files:
            errors.append("No files selected for translation")
        
        if self.translation_settings:
            if self.translation_settings.source_language == self.translation_settings.target_language:
                warnings.append("Source and target languages are the same")
        
        return {
            "errors": errors,
            "warnings": warnings
        }
    
    def reset_panel(self) -> None:
        """重置面板状态"""
        # TDD Green: 最小实现，清空状态
        self.audio_files = []
        self.selected_files = []
        self.translation_settings = None
        self.current_job = None
        logger.info("Translation panel reset")


class TranslationPanelController:
    """翻译面板控制器 - 协调UI和业务逻辑"""
    
    def __init__(self, panel: TranslationControlPanel):
        self.panel = panel
        self.translation_service = None  # 将在实际实现中注入
    
    async def handle_file_selection(self, file_paths: List[str]) -> Dict[str, Any]:
        """处理文件选择事件"""
        # TDD Green: 最小实现，异步处理文件选择
        try:
            # 模拟文件验证
            await asyncio.sleep(0.1)
            
            valid_files = []
            for path in file_paths:
                if path.endswith(('.mp3', '.wav', '.m4a')):
                    valid_files.append(path)
            
            self.panel.select_files(valid_files)
            
            return {
                "success": True,
                "selected_count": len(valid_files),
                "invalid_count": len(file_paths) - len(valid_files)
            }
        
        except Exception as e:
            logger.error(f"Error handling file selection: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def handle_translation_start(self) -> Dict[str, Any]:
        """处理翻译启动事件"""
        # TDD Green: 最小实现，异步启动翻译
        try:
            # 验证设置
            validation = self.panel.validate_settings()
            if validation["errors"]:
                return {
                    "success": False,
                    "errors": validation["errors"]
                }
            
            # 启动翻译
            job_id = self.panel.start_translation()
            if job_id:
                return {
                    "success": True,
                    "job_id": job_id,
                    "warnings": validation["warnings"]
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to start translation job"
                }
        
        except Exception as e:
            logger.error(f"Error starting translation: {e}")
            return {
                "success": False,
                "error": str(e)
            }
