"""
性能跟踪器

监控TDD开发过程中的性能指标
"""

import time
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging


class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self, data_dir: str = "metrics"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 性能基准
        self.performance_benchmarks = {
            'test_execution_time_max': 30.0,     # 测试执行最长30秒
            'memory_usage_max': 512 * 1024 * 1024,  # 最大内存使用512MB
            'cpu_usage_max': 80.0,               # 最大CPU使用率80%
            'coverage_generation_time_max': 60.0  # 覆盖率生成最长60秒
        }
    
    def get_performance_score(self) -> float:
        """获取性能综合评分(0-100)"""
        try:
            # 各项性能指标
            test_speed_score = self._measure_test_execution_performance()
            coverage_score = self._measure_coverage_performance()
            
            # 计算加权平均分
            total_score = (test_speed_score * 0.6 + coverage_score * 0.4)
            
            self.logger.info(f"性能综合评分: {total_score:.1f}")
            return total_score
            
        except Exception as e:
            self.logger.error(f"获取性能评分时出错: {e}")
            return 50.0
    
    def _measure_test_execution_performance(self) -> float:
        """测量测试执行性能"""
        try:
            start_time = time.time()
            
            # 运行快速测试
            result = subprocess.run([
                "python", "-m", "pytest", "-x", "--tb=no", "-q"
            ], capture_output=True, text=True, timeout=60)
            
            execution_time = time.time() - start_time
            
            # 转换为性能评分
            if execution_time <= self.performance_benchmarks['test_execution_time_max']:
                # 执行时间在基准内，根据速度给分
                speed_ratio = self.performance_benchmarks['test_execution_time_max'] / execution_time
                return min(100.0, 50 + speed_ratio * 10)
            else:
                # 执行时间超标，扣分
                penalty = (execution_time - self.performance_benchmarks['test_execution_time_max']) / self.performance_benchmarks['test_execution_time_max'] * 50
                return max(0, 50 - penalty)
                
        except subprocess.TimeoutExpired:
            self.logger.warning("测试执行超时")
            return 20.0
        except Exception as e:
            self.logger.error(f"测量测试执行性能时出错: {e}")
            return 50.0
    
    def _measure_coverage_performance(self) -> float:
        """测量覆盖率生成性能"""
        try:
            start_time = time.time()
            
            # 生成覆盖率报告
            result = subprocess.run([
                "python", "-m", "pytest", "--cov=src/voice_came", "--cov-report=term", "--tb=no", "-q"
            ], capture_output=True, text=True, timeout=120)
            
            coverage_time = time.time() - start_time
            
            # 转换为性能评分
            if coverage_time <= self.performance_benchmarks['coverage_generation_time_max']:
                # 生成时间在基准内
                speed_ratio = self.performance_benchmarks['coverage_generation_time_max'] / coverage_time
                return min(100.0, 50 + speed_ratio * 5)
            else:
                # 生成时间超标
                penalty = (coverage_time - self.performance_benchmarks['coverage_generation_time_max']) / self.performance_benchmarks['coverage_generation_time_max'] * 50
                return max(0, 50 - penalty)
                
        except subprocess.TimeoutExpired:
            self.logger.warning("覆盖率生成超时")
            return 20.0
        except Exception as e:
            self.logger.error(f"测量覆盖率性能时出错: {e}")
            return 50.0 