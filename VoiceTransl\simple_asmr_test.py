#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化ASMR测试脚本 - 基于现有工具链
避免NumPy版本兼容性问题
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def extract_audio_info(video_path):
    """使用ffmpeg提取音频信息"""
    try:
        # 使用ffprobe获取音频信息
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            import json
            info = json.loads(result.stdout)
            
            # 提取音频流信息
            audio_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'audio']
            
            if audio_streams:
                audio_stream = audio_streams[0]
                duration = float(info.get('format', {}).get('duration', 0))
                sample_rate = int(audio_stream.get('sample_rate', 0))
                channels = int(audio_stream.get('channels', 0))
                
                return {
                    'duration': duration,
                    'sample_rate': sample_rate,
                    'channels': channels,
                    'codec': audio_stream.get('codec_name', 'unknown')
                }
        
        return None
        
    except Exception as e:
        print(f"❌ 音频信息提取失败: {e}")
        return None

def test_whisper_recognition(video_path):
    """测试Whisper语音识别"""
    try:
        # 提取音频到临时文件
        temp_audio = "temp_audio.wav"
        
        # 使用ffmpeg提取音频
        extract_cmd = [
            'ffmpeg',
            '-i', video_path,
            '-vn',  # 不要视频
            '-acodec', 'pcm_s16le',  # 16位PCM
            '-ar', '16000',  # 16kHz采样率
            '-ac', '1',  # 单声道
            '-t', '30',  # 只处理前30秒
            '-y',  # 覆盖输出文件
            temp_audio
        ]
        
        print("🎵 提取音频中...")
        result = subprocess.run(extract_cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            print(f"❌ 音频提取失败: {result.stderr}")
            return None
        
        if not os.path.exists(temp_audio):
            print("❌ 临时音频文件未生成")
            return None
        
        # 检查whisper.cpp是否可用
        whisper_exe = "whisper/main.exe"
        if os.path.exists(whisper_exe):
            print("🎯 使用Whisper.cpp进行识别...")
            
            # 检查是否有模型文件
            model_files = [f for f in os.listdir("whisper") if f.endswith(".bin")]
            if not model_files:
                print("⚠️ 未找到Whisper模型文件")
                return {
                    'engine': 'whisper.cpp',
                    'status': 'no_model',
                    'transcription': '',
                    'processing_time': 0
                }
            
            # 使用最小的模型进行测试
            model_file = sorted(model_files)[0]  # 选择第一个模型
            
            start_time = time.time()
            
            whisper_cmd = [
                whisper_exe,
                '-m', f"whisper/{model_file}",
                '-f', temp_audio,
                '--output-txt'
            ]
            
            result = subprocess.run(whisper_cmd, capture_output=True, text=True, timeout=120)
            processing_time = time.time() - start_time
            
            # 清理临时文件
            if os.path.exists(temp_audio):
                os.remove(temp_audio)
            
            if result.returncode == 0:
                # 尝试读取输出文件
                txt_file = temp_audio.replace('.wav', '.txt')
                transcription = ""
                if os.path.exists(txt_file):
                    with open(txt_file, 'r', encoding='utf-8') as f:
                        transcription = f.read().strip()
                    os.remove(txt_file)
                
                return {
                    'engine': 'whisper.cpp',
                    'model': model_file,
                    'status': 'success',
                    'transcription': transcription,
                    'processing_time': processing_time
                }
            else:
                return {
                    'engine': 'whisper.cpp',
                    'status': 'failed',
                    'error': result.stderr,
                    'processing_time': processing_time
                }
        
        else:
            print("❌ Whisper.cpp不可用")
            return {
                'engine': 'none',
                'status': 'unavailable',
                'transcription': '',
                'processing_time': 0
            }
            
    except Exception as e:
        print(f"❌ Whisper识别测试失败: {e}")
        return None

def analyze_asmr_characteristics(audio_info, whisper_result):
    """基于可用信息分析ASMR特征"""
    asmr_score = 0
    analysis = {}
    
    if audio_info:
        # 时长分析
        duration = audio_info.get('duration', 0)
        analysis['duration'] = duration
        
        if duration > 300:  # 超过5分钟
            asmr_score += 20
            analysis['duration_score'] = "长时长内容，符合ASMR特征"
        elif duration > 60:  # 超过1分钟
            asmr_score += 10
            analysis['duration_score'] = "中等时长，可能为ASMR内容"
        
        # 采样率分析
        sample_rate = audio_info.get('sample_rate', 0)
        analysis['sample_rate'] = sample_rate
        
        if sample_rate >= 44100:
            asmr_score += 15
            analysis['quality_score'] = "高质量音频，适合ASMR"
        elif sample_rate >= 22050:
            asmr_score += 10
            analysis['quality_score'] = "中等质量音频"
    
    if whisper_result and whisper_result.get('status') == 'success':
        transcription = whisper_result.get('transcription', '').lower()
        
        # 关键词分析
        asmr_keywords = ['asmr', 'whisper', 'relax', 'sleep', 'calm', 'soft', 'gentle', 
                        '耳语', '放松', '睡眠', '轻柔', '舒缓', '冥想']
        
        found_keywords = [kw for kw in asmr_keywords if kw in transcription]
        
        if found_keywords:
            asmr_score += len(found_keywords) * 10
            analysis['keywords_found'] = found_keywords
        
        # 处理时间分析（间接反映音频复杂度）
        processing_time = whisper_result.get('processing_time', 0)
        if audio_info and audio_info.get('duration', 0) > 0:
            speed_ratio = audio_info['duration'] / max(processing_time, 0.1)
            analysis['processing_speed'] = f"{speed_ratio:.2f}x实时"
    
    # 综合评分
    analysis['asmr_probability'] = min(asmr_score / 100.0, 1.0)
    analysis['is_likely_asmr'] = asmr_score >= 30
    
    return analysis

def main():
    """主测试函数"""
    print("🎯 ASMR技术预研 - 简化测试")
    print("=" * 50)
    
    test_file = r"d:\AICODE\Voice-came\ASMR样本\ASMR.mp4"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📁 测试文件: {test_file}")
    print(f"📊 文件大小: {os.path.getsize(test_file) / (1024*1024):.2f} MB")
    
    # 1. 音频信息提取
    print("\n📋 1. 音频信息分析")
    print("-" * 30)
    audio_info = extract_audio_info(test_file)
    
    if audio_info:
        print(f"✅ 音频时长: {audio_info['duration']:.2f}秒")
        print(f"✅ 采样率: {audio_info['sample_rate']}Hz")
        print(f"✅ 声道数: {audio_info['channels']}")
        print(f"✅ 编码格式: {audio_info['codec']}")
    else:
        print("❌ 音频信息提取失败")
    
    # 2. 语音识别测试
    print("\n🎤 2. 语音识别测试")
    print("-" * 30)
    whisper_result = test_whisper_recognition(test_file)
    
    if whisper_result:
        print(f"✅ 识别引擎: {whisper_result.get('engine', 'unknown')}")
        print(f"✅ 处理状态: {whisper_result.get('status', 'unknown')}")
        print(f"✅ 处理时间: {whisper_result.get('processing_time', 0):.2f}秒")
        
        if whisper_result.get('transcription'):
            print(f"✅ 识别文本: {whisper_result['transcription'][:100]}...")
        
        if whisper_result.get('error'):
            print(f"⚠️ 错误信息: {whisper_result['error']}")
    else:
        print("❌ 语音识别测试失败")
    
    # 3. ASMR特征分析
    print("\n🔍 3. ASMR特征分析")
    print("-" * 30)
    analysis = analyze_asmr_characteristics(audio_info, whisper_result)
    
    print(f"✅ ASMR概率: {analysis.get('asmr_probability', 0):.2f}")
    print(f"✅ 可能为ASMR: {analysis.get('is_likely_asmr', False)}")
    
    if 'keywords_found' in analysis:
        print(f"✅ 发现关键词: {', '.join(analysis['keywords_found'])}")
    
    if 'processing_speed' in analysis:
        print(f"✅ 处理速度: {analysis['processing_speed']}")
    
    # 4. 技术预研总结
    print("\n📊 4. 技术预研总结")
    print("-" * 30)
    
    print("🎯 低音量语音识别: ", end="")
    if whisper_result and whisper_result.get('status') == 'success':
        print("✅ 验证通过")
    else:
        print("⚠️ 需要优化")
    
    print("🔊 音质无损处理: ", end="")
    if audio_info and audio_info.get('sample_rate', 0) >= 22050:
        print("✅ 支持高质量处理")
    else:
        print("⚠️ 需要确认")
    
    print("🎵 背景音分离: ", end="")
    if whisper_result and whisper_result.get('processing_time', 0) > 0:
        print("✅ 基础功能可用")
    else:
        print("⚠️ 待实现")
    
    print("⚡ 性能基准: ", end="")
    if whisper_result and 'processing_speed' in analysis:
        print(f"✅ {analysis['processing_speed']}")
    else:
        print("⚠️ 需要测试")

if __name__ == "__main__":
    main()