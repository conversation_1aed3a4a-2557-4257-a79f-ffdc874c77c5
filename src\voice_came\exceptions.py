#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice-came异常类定义

定义项目中使用的所有自定义异常类。
"""


class VoiceCameError(Exception):
    """Voice-came基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code


class FileValidationError(VoiceCameError):
    """文件验证相关异常"""
    pass


class WhisperXError(VoiceCameError):
    """WhisperX相关异常"""
    pass


class TranslationError(VoiceCameError):
    """翻译引擎相关异常"""
    pass


class BatchProcessingError(VoiceCameError):
    """批量处理相关异常"""
    pass


class ConfigurationError(VoiceCameError):
    """配置相关异常"""
    pass


class AudioProcessingError(VoiceCameError):
    """音频处理相关异常"""
    pass


class QueueError(VoiceCameError):
    """队列管理相关异常"""
    pass


class ProcessingError(VoiceCameError):
    """文件处理相关异常"""
    pass 