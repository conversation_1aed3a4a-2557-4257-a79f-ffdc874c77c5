#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhisperX模型加载测试

TDD-Red阶段：这些测试应该初始失败，直到实现对应功能
"""

import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock, call
from voice_came.speech_recognition.whisperx_engine import WhisperXEngine, WhisperXConfig


class TestWhisperXModelLoading:
    """WhisperX模型加载测试类"""
    
    @pytest.fixture
    def temp_model_dir(self):
        """创建临时模型目录"""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield tmpdir
    
    @pytest.fixture
    def basic_config(self):
        """基础配置"""
        return WhisperXConfig(
            model_name="tiny",
            device="cpu",
            compute_type="float32",
            language="zh"
        )
    
    @pytest.mark.tdd_red
    def test_whisperx_engine_initialization(self, basic_config):
        """测试WhisperX引擎初始化"""
        engine = WhisperXEngine(basic_config)
        assert engine is not None
        assert engine.config == basic_config
        assert hasattr(engine, 'model')
        assert hasattr(engine, 'align_model')
        assert hasattr(engine, 'align_metadata')
    
    @pytest.mark.tdd_red
    def test_load_model_success(self, basic_config):
        """测试模型加载成功"""
        engine = WhisperXEngine(basic_config)
        
        # 模拟成功加载
        with patch('whisperx.load_model') as mock_load:
            mock_model = MagicMock()
            mock_load.return_value = mock_model
            
            result = engine.load_model()
            
            assert result is True
            assert engine.model == mock_model
            mock_load.assert_called_once_with(
                "tiny", "cpu", compute_type="float32"
            )
    
    @pytest.mark.tdd_red
    def test_load_model_failure(self, basic_config):
        """测试模型加载失败"""
        engine = WhisperXEngine(basic_config)
        
        with patch('whisperx.load_model') as mock_load:
            mock_load.side_effect = Exception("模型加载失败")
            
            result = engine.load_model()
            
            assert result is False
            assert engine.model is None
    
    @pytest.mark.tdd_red
    def test_load_align_model_success(self, basic_config):
        """测试对齐模型加载成功"""
        engine = WhisperXEngine(basic_config)
        
        with patch('whisperx.load_align_model') as mock_load_align:
            mock_align_model = MagicMock()
            mock_metadata = {"language": "zh"}
            mock_load_align.return_value = (mock_align_model, mock_metadata)
            
            result = engine.load_align_model()
            
            assert result is True
            assert engine.align_model == mock_align_model
            assert engine.align_metadata == mock_metadata
            mock_load_align.assert_called_once_with(
                language_code="zh", device="cpu"
            )
    
    @pytest.mark.tdd_red
    def test_load_align_model_failure(self, basic_config):
        """测试对齐模型加载失败"""
        engine = WhisperXEngine(basic_config)
        
        with patch('whisperx.load_align_model') as mock_load_align:
            mock_load_align.side_effect = Exception("对齐模型加载失败")
            
            result = engine.load_align_model()
            
            assert result is False
            assert engine.align_model is None
            assert engine.align_metadata is None
    
    @pytest.mark.tdd_red
    def test_model_device_switching(self):
        """测试模型设备切换"""
        # CPU配置
        cpu_config = WhisperXConfig(model_name="tiny", device="cpu")
        cpu_engine = WhisperXEngine(cpu_config)
        
        # GPU配置
        gpu_config = WhisperXConfig(model_name="tiny", device="cuda")
        gpu_engine = WhisperXEngine(gpu_config)
        
        assert cpu_engine.config.device == "cpu"
        assert gpu_engine.config.device == "cuda"
    
    @pytest.mark.tdd_red
    def test_different_model_sizes(self):
        """测试不同模型大小加载"""
        model_sizes = ["tiny", "base", "small", "medium", "large-v2"]
        
        for size in model_sizes:
            config = WhisperXConfig(model_name=size, device="cpu")
            engine = WhisperXEngine(config)
            
            with patch('whisperx.load_model') as mock_load:
                mock_load.return_value = MagicMock()
                result = engine.load_model()
                
                assert result is True
                mock_load.assert_called_with(size, "cpu", compute_type="float32")
    
    @pytest.mark.tdd_red  
    def test_compute_type_validation(self):
        """测试计算类型验证"""
        valid_types = ["float16", "float32", "int8"]
        
        for compute_type in valid_types:
            config = WhisperXConfig(
                model_name="tiny", 
                device="cpu", 
                compute_type=compute_type
            )
            engine = WhisperXEngine(config)
            assert engine.config.compute_type == compute_type
    
    @pytest.mark.tdd_red
    def test_language_support(self):
        """测试多语言支持"""
        languages = ["zh", "en", "ja", "ko", "fr", "de", "es"]
        
        for lang in languages:
            config = WhisperXConfig(model_name="tiny", language=lang)
            engine = WhisperXEngine(config)
            
            with patch('whisperx.load_align_model') as mock_load_align:
                mock_load_align.return_value = (MagicMock(), {"language": lang})
                result = engine.load_align_model()
                
                assert result is True
                mock_load_align.assert_called_with(language_code=lang, device="cpu")


class TestWhisperXModelCache:
    """WhisperX模型缓存测试类"""
    
    @pytest.fixture
    def temp_model_dir(self):
        """创建临时模型目录"""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield tmpdir
    
    @pytest.mark.tdd_red
    def test_model_cache_directory(self, temp_model_dir):
        """测试模型缓存目录"""
        config = WhisperXConfig(
            model_name="tiny",
            model_cache_dir=temp_model_dir
        )
        engine = WhisperXEngine(config)
        
        assert engine.config.model_cache_dir == temp_model_dir
        assert os.path.exists(temp_model_dir)
    
    @pytest.mark.tdd_red
    def test_model_cache_reuse(self, temp_model_dir):
        """测试模型缓存重用"""
        config = WhisperXConfig(
            model_name="tiny",
            model_cache_dir=temp_model_dir
        )
        
        # 第一次加载
        engine1 = WhisperXEngine(config)
        with patch('whisperx.load_model') as mock_load:
            mock_model = MagicMock()
            mock_load.return_value = mock_model
            result1 = engine1.load_model()
            assert result1 is True
            assert mock_load.call_count == 1
        
        # 第二次加载应该使用缓存
        engine2 = WhisperXEngine(config)
        with patch('whisperx.load_model') as mock_load:
            # 模拟缓存命中，不调用load_model
            result2 = engine2.load_cached_model()
            assert result2 is True
    
    @pytest.mark.tdd_red
    def test_model_cache_cleanup(self, temp_model_dir):
        """测试模型缓存清理"""
        config = WhisperXConfig(
            model_name="tiny",
            model_cache_dir=temp_model_dir
        )
        engine = WhisperXEngine(config)
        
        # 清理缓存
        result = engine.clear_model_cache()
        assert result is True
    
    @pytest.mark.tdd_red
    def test_insufficient_memory_handling(self):
        """测试内存不足时的处理"""
        config = WhisperXConfig(model_name="large-v2", device="cuda")
        engine = WhisperXEngine(config)
        
        with patch('whisperx.load_model') as mock_load:
            mock_load.side_effect = RuntimeError("CUDA out of memory")
            
            result = engine.load_model()
            assert result is False
            # 应该自动降级到CPU模式
            assert engine.config.device == "cpu"


class TestWhisperXModelValidation:
    """WhisperX模型验证测试类"""
    
    @pytest.fixture
    def basic_config(self):
        """基础配置"""
        return WhisperXConfig(
            model_name="tiny",
            device="cpu",
            compute_type="float32",
            language="zh"
        )
    
    @pytest.mark.tdd_red
    def test_model_health_check(self, basic_config):
        """测试模型健康检查"""
        engine = WhisperXEngine(basic_config)
        
        with patch('whisperx.load_model') as mock_load:
            mock_model = MagicMock()
            mock_model.transcribe = MagicMock(return_value={"segments": []})
            mock_load.return_value = mock_model
            
            engine.load_model()
            health_status = engine.check_model_health()
            
            assert health_status is True
    
    @pytest.mark.tdd_red
    def test_model_version_compatibility(self, basic_config):
        """测试模型版本兼容性"""
        engine = WhisperXEngine(basic_config)
        
        # 模拟版本检查
        compatible = engine.check_model_compatibility("tiny")
        assert compatible is True
        
        # 模拟不兼容版本
        incompatible = engine.check_model_compatibility("unsupported_model")
        assert incompatible is False
    
    @pytest.mark.tdd_red
    def test_model_warming_up(self, basic_config):
        """测试模型预热"""
        engine = WhisperXEngine(basic_config)
        
        with patch('whisperx.load_model') as mock_load:
            mock_model = MagicMock()
            mock_load.return_value = mock_model
            
            engine.load_model()
            warmup_result = engine.warm_up_model()
            
            assert warmup_result is True


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"]) 