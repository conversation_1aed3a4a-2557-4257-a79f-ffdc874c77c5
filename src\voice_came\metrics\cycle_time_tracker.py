"""
Red-Green-Refactor循环时间统计器

跟踪和分析TDD开发循环的时间分布
"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging


class CycleTimeTracker:
    """Red-Green-Refactor循环时间统计器"""
    
    def __init__(self, data_dir: str = "metrics"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.cycles_file = self.data_dir / "cycle_times.json"
        
        self.logger = logging.getLogger(__name__)
        
        # 时间阈值设置
        self.time_thresholds = {
            'red_phase_max': 15 * 60,    # Red阶段最大15分钟
            'green_phase_max': 10 * 60,  # Green阶段最大10分钟
            'refactor_phase_max': 20 * 60, # Refactor阶段最大20分钟
            'total_cycle_max': 45 * 60   # 总循环时间最大45分钟
        }
    
    def get_cycle_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取循环时间统计"""
        try:
            # 模拟循环统计数据
            mock_cycles = self._generate_mock_cycle_data(days)
            
            if not mock_cycles:
                return {
                    'total_cycles': 0,
                    'average_times': {},
                    'time_distribution': {},
                    'efficiency_trends': {},
                    'recommendations': []
                }
            
            # 计算平均时间
            total_cycles = len(mock_cycles)
            avg_red = sum(c['red_time'] for c in mock_cycles) / total_cycles
            avg_green = sum(c['green_time'] for c in mock_cycles) / total_cycles
            avg_refactor = sum(c['refactor_time'] for c in mock_cycles) / total_cycles
            avg_total = sum(c['total_time'] for c in mock_cycles) / total_cycles
            
            statistics = {
                'total_cycles': total_cycles,
                'period_days': days,
                'average_times': {
                    'red_phase': avg_red,
                    'green_phase': avg_green,
                    'refactor_phase': avg_refactor,
                    'total_cycle': avg_total
                },
                'time_distribution': {
                    'red_percentage': (avg_red / avg_total) * 100 if avg_total > 0 else 0,
                    'green_percentage': (avg_green / avg_total) * 100 if avg_total > 0 else 0,
                    'refactor_percentage': (avg_refactor / avg_total) * 100 if avg_total > 0 else 0
                },
                'efficiency_trends': {'improving': True, 'improvement_percentage': 5.2},
                'recommendations': self._generate_time_recommendations(mock_cycles)
            }
            
            return statistics
            
        except Exception as e:
            self.logger.error(f"获取循环统计时出错: {e}")
            return {
                'total_cycles': 0,
                'average_times': {},
                'time_distribution': {},
                'efficiency_trends': {},
                'recommendations': []
            }
    
    def _generate_mock_cycle_data(self, days: int) -> List[Dict[str, Any]]:
        """生成模拟循环数据"""
        mock_cycles = []
        
        # 模拟最近几天的循环数据
        for i in range(min(days, 10)):  # 最多10个循环
            cycle = {
                'session_id': f"cycle_{i}",
                'feature_name': f"feature_{i}",
                'red_time': 300 + (i * 30),    # 5-8分钟
                'green_time': 180 + (i * 20),  # 3-6分钟
                'refactor_time': 420 + (i * 40), # 7-13分钟
                'total_time': 900 + (i * 90),   # 15-30分钟
                'timestamp': (datetime.now() - timedelta(days=i)).isoformat()
            }
            mock_cycles.append(cycle)
        
        return mock_cycles
    
    def _generate_time_recommendations(self, cycles: List[Dict[str, Any]]) -> List[str]:
        """生成时间优化建议"""
        recommendations = []
        
        if not cycles:
            return recommendations
        
        # 计算平均时间
        avg_red = sum(c['red_time'] for c in cycles) / len(cycles)
        avg_green = sum(c['green_time'] for c in cycles) / len(cycles)
        avg_refactor = sum(c['refactor_time'] for c in cycles) / len(cycles)
        
        # Red阶段建议
        if avg_red > self.time_thresholds['red_phase_max']:
            recommendations.append("Red阶段时间过长，建议简化测试用例或分解功能")
        
        # Green阶段建议
        if avg_green > self.time_thresholds['green_phase_max']:
            recommendations.append("Green阶段时间过长，建议实现更简单的解决方案")
        
        # Refactor阶段建议
        if avg_refactor > self.time_thresholds['refactor_phase_max']:
            recommendations.append("Refactor阶段时间过长，建议小步重构或分阶段进行")
        
        # 如果没有问题，给出积极建议
        if not recommendations:
            recommendations.append("TDD循环时间控制良好，继续保持")
        
        return recommendations 