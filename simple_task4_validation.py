#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4简化验证脚本

快速验证Task 4核心功能是否正常工作
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, 'src')

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    success_count = 0
    total_count = 0
    
    # 测试翻译模块
    modules_to_test = [
        ("voice_came.translation.models", "TranslationConfig"),
        ("voice_came.translation.integration", "VoiceTranslAdapter"),
        ("voice_came.translation.terminology", "SleepTerminologyManager"),
        ("voice_came.translation.quality", "SleepContentQualityAssessor"),
        ("voice_came.translation.optimization", "SleepContentPromptGenerator"),
        ("voice_came.ui.translation_panel", "TranslationControlPanel"),
        ("voice_came.ui.progress_monitor", "RealTimeProgressMonitor"),
        ("voice_came.ui.batch_interface", "BatchProcessingInterface"),
        ("voice_came.ui.results_viewer", "TranslationResultsViewer")
    ]
    
    for module_name, class_name in modules_to_test:
        total_count += 1
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}.{class_name}: {e}")
    
    print(f"\n📊 导入测试结果: {success_count}/{total_count} 成功")
    return success_count, total_count

def test_basic_functionality():
    """测试基础功能"""
    print("\n🔍 测试基础功能...")
    
    success_count = 0
    total_count = 0
    
    # 测试数据模型
    total_count += 1
    try:
        from voice_came.translation.models import TranslationConfig, AudioSegment
        
        config = TranslationConfig(
            source_language="zh",
            target_language="en",
            terminology_enabled=True
        )
        
        segment = AudioSegment(
            start_time=0.0,
            end_time=5.0,
            text="测试文本",
            confidence=0.9
        )
        
        assert config.source_language == "zh"
        assert segment.text == "测试文本"
        print("✅ 数据模型创建和访问")
        success_count += 1
    except Exception as e:
        print(f"❌ 数据模型: {e}")
    
    # 测试VoiceTransl适配器
    total_count += 1
    try:
        from voice_came.translation.integration import VoiceTranslAdapter
        from voice_came.translation.models import WhisperXOutput, AudioSegment
        
        adapter = VoiceTranslAdapter("/fake/path")
        
        whisperx_output = WhisperXOutput(
            segments=[AudioSegment(0.0, 5.0, "测试", 0.9)],
            language="zh",
            metadata={}
        )
        
        converted = adapter.convert_whisperx_to_voicetransl(whisperx_output)
        assert "segments" in converted
        print("✅ VoiceTransl适配器数据转换")
        success_count += 1
    except Exception as e:
        print(f"❌ VoiceTransl适配器: {e}")
    
    # 测试术语管理器
    total_count += 1
    try:
        from voice_came.translation.terminology import SleepTerminologyManager
        
        manager = SleepTerminologyManager("fake_file.yaml")
        
        text = "冥想和放松练习"
        processed = manager.preprocess_for_translation(text, "zh")
        final = manager.postprocess_translation(processed, "en")
        
        print("✅ 术语管理器预处理和后处理")
        success_count += 1
    except Exception as e:
        print(f"❌ 术语管理器: {e}")
    
    # 测试质量评估器
    total_count += 1
    try:
        from voice_came.translation.quality import SleepContentQualityAssessor
        
        assessor = SleepContentQualityAssessor()
        
        quality = assessor.assess_translation_quality(
            "冥想练习", "meditation practice", "en"
        )
        
        assert hasattr(quality, 'overall_score')
        assert 0.0 <= quality.overall_score <= 1.0
        print("✅ 质量评估器评分")
        success_count += 1
    except Exception as e:
        print(f"❌ 质量评估器: {e}")
    
    # 测试翻译控制面板
    total_count += 1
    try:
        from voice_came.ui.translation_panel import TranslationControlPanel
        
        panel = TranslationControlPanel()
        
        # 配置设置
        settings = {
            "source_language": "zh",
            "target_language": "en",
            "quality_level": "high"
        }
        panel.configure_translation_settings(settings)
        
        current_settings = panel.get_translation_settings()
        assert current_settings["source_language"] == "zh"
        print("✅ 翻译控制面板配置")
        success_count += 1
    except Exception as e:
        print(f"❌ 翻译控制面板: {e}")
    
    print(f"\n📊 功能测试结果: {success_count}/{total_count} 成功")
    return success_count, total_count

def test_integration_workflow():
    """测试集成工作流"""
    print("\n🔍 测试集成工作流...")
    
    try:
        # 模拟完整的翻译工作流
        from voice_came.translation.models import AudioSegment, WhisperXOutput, TranslationConfig
        from voice_came.translation.integration import VoiceTranslAdapter
        from voice_came.translation.terminology import SleepTerminologyManager
        from voice_came.translation.quality import SleepContentQualityAssessor
        from voice_came.ui.translation_panel import TranslationControlPanel
        
        print("  1. 创建音频段落...")
        segments = [
            AudioSegment(0.0, 5.0, "让我们开始今晚的冥想练习", 0.95),
            AudioSegment(5.0, 10.0, "深度放松你的身心", 0.90)
        ]
        
        whisperx_output = WhisperXOutput(
            segments=segments,
            language="zh",
            metadata={"duration": 10.0}
        )
        print("  ✅ 音频段落创建完成")
        
        print("  2. 初始化翻译组件...")
        adapter = VoiceTranslAdapter("/fake/voicetransl")
        terminology_manager = SleepTerminologyManager("fake_terms.yaml")
        quality_assessor = SleepContentQualityAssessor()
        control_panel = TranslationControlPanel()
        print("  ✅ 翻译组件初始化完成")
        
        print("  3. 数据格式转换...")
        converted_data = adapter.convert_whisperx_to_voicetransl(whisperx_output)
        assert len(converted_data["segments"]) == 2
        print("  ✅ 数据格式转换完成")
        
        print("  4. 术语预处理...")
        for segment in segments:
            processed_text = terminology_manager.preprocess_for_translation(segment.text, "zh")
            print(f"    原文: {segment.text}")
            print(f"    预处理: {processed_text}")
        print("  ✅ 术语预处理完成")
        
        print("  5. 模拟翻译结果...")
        mock_translations = [
            "Let's begin tonight's meditation practice",
            "Deeply relax your body and mind"
        ]
        
        for i, (segment, translation) in enumerate(zip(segments, mock_translations)):
            # 术语后处理
            final_translation = terminology_manager.postprocess_translation(translation, "en")
            
            # 质量评估
            quality = quality_assessor.assess_translation_quality(segment.text, final_translation, "en")
            
            print(f"    段落{i+1}: {segment.text} → {final_translation} (质量: {quality.overall_score:.2f})")
        
        print("  ✅ 翻译流程完成")
        
        print("  6. UI控制面板集成...")
        control_panel.configure_translation_settings({
            "source_language": "zh",
            "target_language": "en",
            "quality_level": "high",
            "terminology_enabled": True
        })
        
        control_panel.load_audio_files([
            {"path": "/test/meditation.mp3", "name": "meditation.mp3", "duration": 10.0}
        ])
        
        print("  ✅ UI控制面板集成完成")
        
        print("\n🎉 集成工作流测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 集成工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Task 4简化验证开始...")
    print("=" * 50)
    
    # 测试导入
    import_success, import_total = test_imports()
    
    # 测试基础功能
    func_success, func_total = test_basic_functionality()
    
    # 测试集成工作流
    workflow_success = test_integration_workflow()
    
    # 计算总体结果
    total_success = import_success + func_success + (1 if workflow_success else 0)
    total_tests = import_total + func_total + 1
    
    success_rate = total_success / total_tests
    
    print("\n" + "=" * 50)
    print("📊 Task 4验证结果总结:")
    print(f"   模块导入: {import_success}/{import_total}")
    print(f"   基础功能: {func_success}/{func_total}")
    print(f"   集成工作流: {'✅' if workflow_success else '❌'}")
    print(f"   总体成功率: {total_success}/{total_tests} ({success_rate:.1%})")
    
    if success_rate >= 0.8:
        print("\n🎉 Task 4验证基本通过！")
        print("   核心功能正常，可以进行下一步开发")
    elif success_rate >= 0.6:
        print("\n⚠️  Task 4验证部分通过")
        print("   大部分功能正常，建议修复少量问题")
    else:
        print("\n❌ Task 4验证需要修复")
        print("   存在较多问题，建议优先修复")
    
    return success_rate >= 0.8

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
