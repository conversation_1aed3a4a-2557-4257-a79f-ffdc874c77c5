#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理器接口

TDD-Green阶段：实现最小可用的文件处理器接口使测试通过
为批量处理队列提供文件处理的抽象接口
"""

import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, Callable
from abc import ABC, abstractmethod
import logging

from voice_came.utils.logger import get_logger
from voice_came.exceptions import ProcessingError

logger = get_logger(__name__)


class FileProcessor(ABC):
    """文件处理器抽象基类
    
    定义文件处理的统一接口，具体实现由子类提供
    """
    
    @abstractmethod
    async def process_file(
        self, 
        file_path: Path, 
        progress_callback: Optional[Callable[[float, str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """处理单个文件
        
        Args:
            file_path: 文件路径
            progress_callback: 进度回调函数 (progress, message)
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 处理结果
            
        Raises:
            ProcessingError: 处理失败时抛出
        """
        pass
    
    def get_supported_formats(self) -> list[str]:
        """获取支持的文件格式
        
        Returns:
            list[str]: 支持的文件格式列表
        """
        return ["mp4", "avi", "mov", "wav", "mp3"]
    
    def validate_file(self, file_path: Path) -> bool:
        """验证文件是否可以处理
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否可以处理
        """
        if not file_path.exists():
            return False
        
        file_extension = file_path.suffix[1:].lower()
        return file_extension in self.get_supported_formats()


class MockFileProcessor(FileProcessor):
    """模拟文件处理器（用于测试）"""
    
    def __init__(self, process_delay: float = 0.1, fail_on_files: list[str] = None):
        """初始化模拟处理器
        
        Args:
            process_delay: 处理延迟时间（秒）
            fail_on_files: 会失败的文件名列表
        """
        self.process_delay = process_delay
        self.fail_on_files = fail_on_files or []
    
    async def process_file(
        self, 
        file_path: Path, 
        progress_callback: Optional[Callable[[float, str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """模拟文件处理"""
        
        # 检查是否应该失败
        if file_path.name in self.fail_on_files:
            raise ProcessingError(f"模拟处理失败: {file_path.name}")
        
        # 模拟处理进度
        if progress_callback:
            await progress_callback(0.0, "开始处理")
            await asyncio.sleep(self.process_delay / 3)
            
            await progress_callback(0.5, "处理中")
            await asyncio.sleep(self.process_delay / 3)
            
            await progress_callback(1.0, "处理完成")
            await asyncio.sleep(self.process_delay / 3)
        else:
            # 没有进度回调时直接等待
            await asyncio.sleep(self.process_delay)
        
        # 返回模拟结果
        return {
            "status": "success",
            "file_path": str(file_path),
            "processing_time": self.process_delay,
            "result": {
                "transcription": f"模拟转录内容 for {file_path.name}",
                "translation": f"模拟翻译内容 for {file_path.name}"
            }
        } 