[run]
source = src/voice_came
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    */node_modules/*
    setup.py
    */migrations/*
    */static/*
    */media/*
    */locale/*
branch = True
parallel = True

[report]
precision = 2
show_missing = True
skip_covered = False
sort = Cover
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[html]
directory = htmlcov
title = Voice-came Coverage Report

[xml]
output = coverage.xml 