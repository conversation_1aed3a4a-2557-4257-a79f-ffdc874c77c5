#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4 实现验证脚本

验证VoiceTransl集成的TDD Green实现是否正常工作
"""

import sys
import os
sys.path.append('src')

def test_voicetransl_adapter():
    """测试VoiceTransl适配器"""
    try:
        from voice_came.translation.integration import VoiceTranslAdapter
        from voice_came.translation.models import WhisperXOutput, AudioSegment
        
        # 创建适配器
        adapter = VoiceTranslAdapter("/fake/path")
        print("✅ VoiceTranslAdapter 创建成功")
        
        # 测试数据转换
        whisperx_output = WhisperXOutput(
            segments=[
                AudioSegment(0.0, 5.0, "测试文本", 0.9)
            ],
            language="zh",
            metadata={}
        )
        
        converted = adapter.convert_whisperx_to_voicetransl(whisperx_output)
        print("✅ 数据转换功能正常")
        print(f"   转换结果: {converted}")
        
        return True
    except Exception as e:
        print(f"❌ VoiceTranslAdapter 测试失败: {e}")
        return False

def test_terminology_manager():
    """测试术语管理器"""
    try:
        from voice_came.translation.terminology import SleepTerminologyManager
        
        # 创建术语管理器
        manager = SleepTerminologyManager("fake_file.yaml")
        print("✅ SleepTerminologyManager 创建成功")
        
        # 测试预处理
        text = "今晚我们将通过冥想和放松来助眠"
        processed = manager.preprocess_for_translation(text, "zh")
        print("✅ 术语预处理功能正常")
        print(f"   预处理结果: {processed}")
        
        # 测试后处理
        translated = "Tonight we will use [SLEEP_TERM_123] and [SLEEP_TERM_456] for [SLEEP_TERM_789]"
        final = manager.postprocess_translation(translated, "en")
        print("✅ 术语后处理功能正常")
        print(f"   后处理结果: {final}")
        
        return True
    except Exception as e:
        print(f"❌ SleepTerminologyManager 测试失败: {e}")
        return False

def test_quality_assessor():
    """测试质量评估器"""
    try:
        from voice_came.translation.quality import SleepContentQualityAssessor
        
        # 创建质量评估器
        assessor = SleepContentQualityAssessor()
        print("✅ SleepContentQualityAssessor 创建成功")
        
        # 测试质量评估
        source = "冥想和放松练习"
        translation = "meditation and relaxation exercises"
        
        quality = assessor.assess_translation_quality(source, translation, "en")
        print("✅ 质量评估功能正常")
        print(f"   质量评分: {quality.overall_score:.2f}")
        print(f"   术语一致性: {quality.terminology_consistency:.2f}")
        
        return True
    except Exception as e:
        print(f"❌ SleepContentQualityAssessor 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Task 4实现验证...")
    print("=" * 50)
    
    tests = [
        ("VoiceTransl适配器", test_voicetransl_adapter),
        ("术语管理器", test_terminology_manager),
        ("质量评估器", test_quality_assessor)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试 {test_name}...")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Task 4 TDD Green实现成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
