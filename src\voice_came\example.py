"""
示例模块

用于验证测试覆盖率功能的简单模块。
"""


def add(a: int, b: int) -> int:
    """两个数相加"""
    return a + b


def multiply(a: int, b: int) -> int:
    """两个数相乘"""
    return a * b


def divide(a: int, b: int) -> float:
    """两个数相除"""
    if b == 0:
        raise ValueError("Cannot divide by zero")
    return a / b


class Calculator:
    """简单计算器类"""
    
    def __init__(self):
        self.history = []
    
    def calculate(self, operation: str, a: int, b: int) -> float:
        """执行计算操作"""
        if operation == "add":
            result = add(a, b)
        elif operation == "multiply":
            result = multiply(a, b)
        elif operation == "divide":
            result = divide(a, b)
        else:
            raise ValueError(f"Unknown operation: {operation}")
        
        self.history.append(f"{a} {operation} {b} = {result}")
        return result
    
    def get_history(self) -> list:
        """获取计算历史"""
        return self.history.copy() 