#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASMR样本测试脚本
"""

import sys
import os
from asmr_validator import ASMRValidator

def test_asmr_sample():
    """测试ASMR样本"""
    print("🎯 开始ASMR样本验证测试")
    print("=" * 50)
    
    # 初始化验证器
    validator = ASMRValidator()
    
    # 测试文件路径
    test_file = r"d:\AICODE\Voice-came\ASMR样本\ASMR.mp4"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📁 测试文件: {test_file}")
    print(f"📊 文件大小: {os.path.getsize(test_file) / (1024*1024):.2f} MB")
    
    try:
        # 执行验证
        result = validator.validate_single_file(test_file)
        
        print("\n✅ 验证完成！")
        print("📋 验证结果:")
        print("-" * 30)
        
        if result:
            print(f"  文件路径: {result.file_path}")
            print(f"  引擎类型: {result.engine_type}")
            print(f"  模型名称: {result.model_name}")
            print(f"  音频时长: {result.duration:.2f}秒")
            print(f"  采样率: {result.sample_rate}Hz")
            print(f"  RMS能量: {result.rms_energy:.6f}")
            print(f"  频谱质心: {result.spectral_centroid:.2f}Hz")
            print(f"  过零率: {result.zero_crossing_rate:.4f}")
            print(f"  ASMR检测: {result.is_asmr_detected}")
            print(f"  Whisper置信度: {result.whisper_confidence:.3f}")
            print(f"  低音量比例: {result.low_volume_ratio:.3f}")
            print(f"  静音比例: {result.silence_ratio:.3f}")
            print(f"  处理时间: {result.processing_time:.2f}秒")
            print(f"  转录精度: {result.transcription_accuracy:.3f}")
            print(f"  词错误率: {result.word_error_rate:.3f}")
            print(f"  验证状态: {result.validation_status}")
            if result.transcription:
                print(f"  转录文本: {result.transcription[:100]}...")
            if result.error_message:
                print(f"  错误信息: {result.error_message}")
        else:
            print("  无验证结果返回")
            
    except Exception as e:
        print(f"❌ 验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_asmr_sample()