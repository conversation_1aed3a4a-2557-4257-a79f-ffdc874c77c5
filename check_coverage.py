#!/usr/bin/env python3
"""
检查enhanced_batch_queue.py的测试覆盖率
"""

import subprocess
import sys

def run_coverage_check():
    """运行覆盖率检查"""
    try:
        # 运行测试并生成覆盖率报告
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/unit/test_enhanced_batch_queue_focused.py",
            "tests/unit/test_enhanced_batch_queue_edge_cases.py", 
            "tests/unit/test_batch_queue.py",
            "--cov=src/voice_came/core/enhanced_batch_queue.py",
            "--cov-report=term",
            "--tb=no",
            "-q"
        ]
        
        print("🔍 运行enhanced_batch_queue.py覆盖率检查...")
        print(f"命令: {' '.join(cmd)}")
        print("-" * 60)
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"返回码: {result.returncode}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

if __name__ == "__main__":
    success = run_coverage_check()
    if success:
        print("✅ 覆盖率检查完成")
    else:
        print("❌ 覆盖率检查失败")
        sys.exit(1) 