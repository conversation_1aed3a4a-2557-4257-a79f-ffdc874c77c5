"""
TDD度量数据收集器

负责收集和汇总所有TDD相关的度量指标
"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass, asdict
import logging

from .coverage_monitor import CoverageMonitor
from .cycle_time_tracker import CycleTimeTracker
from .quality_analyzer import QualityAnalyzer
from .compliance_checker import ComplianceChecker
from .performance_tracker import PerformanceTracker


@dataclass
class TDDMetrics:
    """TDD度量指标数据结构"""
    timestamp: str
    test_coverage: float
    test_pass_rate: float
    test_count: int
    failed_test_count: int
    cycle_time_stats: Dict[str, float]
    code_quality_score: float
    compliance_rate: float
    defect_density: float
    performance_score: float
    development_velocity: float
    rework_rate: float


class TDDMetricsCollector:
    """TDD度量数据收集器"""
    
    def __init__(self, metrics_dir: str = "metrics"):
        self.metrics_dir = Path(metrics_dir)
        self.metrics_dir.mkdir(exist_ok=True)
        
        self.metrics_file = self.metrics_dir / "tdd_metrics.json"
        self.trends_file = self.metrics_dir / "trends.json"
        
        # 初始化各个监控组件
        self.coverage_monitor = CoverageMonitor()
        self.cycle_tracker = CycleTimeTracker()
        self.quality_analyzer = QualityAnalyzer()
        self.compliance_checker = ComplianceChecker()
        self.performance_tracker = PerformanceTracker()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化度量历史
        self.metrics_history: List[TDDMetrics] = []
        self.load_metrics_history()
    
    def collect_current_metrics(self) -> TDDMetrics:
        """收集当前的TDD度量指标"""
        timestamp = datetime.now().isoformat()
        
        try:
            # 收集测试覆盖率
            coverage_data = self.coverage_monitor.get_current_coverage()
            
            # 收集循环时间统计  
            cycle_stats = self.cycle_tracker.get_cycle_statistics()
            
            # 收集代码质量分析
            quality_score = self.quality_analyzer.analyze_code_quality()
            
            # 收集合规性检查
            compliance_rate = self.compliance_checker.check_tdd_compliance()
            
            # 收集性能指标
            performance_score = self.performance_tracker.get_performance_score()
            
            # 计算开发效率指标
            dev_velocity = self._calculate_development_velocity()
            rework_rate = self._calculate_rework_rate()
            
            metrics = TDDMetrics(
                timestamp=timestamp,
                test_coverage=coverage_data.get('coverage_percentage', 0.0),
                test_pass_rate=coverage_data.get('pass_rate', 0.0),
                test_count=coverage_data.get('total_tests', 0),
                failed_test_count=coverage_data.get('failed_tests', 0),
                cycle_time_stats=cycle_stats,
                code_quality_score=quality_score,
                compliance_rate=compliance_rate,
                defect_density=self._calculate_defect_density(),
                performance_score=performance_score,
                development_velocity=dev_velocity,
                rework_rate=rework_rate
            )
            
            self.logger.info(f"收集到TDD度量指标: 覆盖率={metrics.test_coverage:.2%}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集度量指标时出错: {e}")
            # 返回默认指标
            return TDDMetrics(
                timestamp=timestamp,
                test_coverage=0.0,
                test_pass_rate=0.0,
                test_count=0,
                failed_test_count=0,
                cycle_time_stats={},
                code_quality_score=0.0,
                compliance_rate=0.0,
                defect_density=0.0,
                performance_score=0.0,
                development_velocity=0.0,
                rework_rate=0.0
            )
    
    def save_metrics(self, metrics: TDDMetrics) -> None:
        """保存度量指标到文件"""
        try:
            # 添加到历史记录
            self.metrics_history.append(metrics)
            
            # 保留最近30天的数据
            cutoff_date = datetime.now() - timedelta(days=30)
            self.metrics_history = [
                m for m in self.metrics_history 
                if datetime.fromisoformat(m.timestamp) > cutoff_date
            ]
            
            # 保存到文件
            metrics_data = {
                "metrics": [asdict(m) for m in self.metrics_history]
            }
            
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"度量指标已保存到 {self.metrics_file}")
            
        except Exception as e:
            self.logger.error(f"保存度量指标时出错: {e}")
    
    def load_metrics_history(self) -> None:
        """加载历史度量数据"""
        try:
            if self.metrics_file.exists():
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.metrics_history = [
                    TDDMetrics(**m) for m in data.get('metrics', [])
                ]
                
                self.logger.info(f"加载了 {len(self.metrics_history)} 条历史度量记录")
        except Exception as e:
            self.logger.error(f"加载历史度量数据时出错: {e}")
            self.metrics_history = []
    
    def get_trend_analysis(self, days: int = 7) -> Dict[str, Any]:
        """获取趋势分析"""
        if len(self.metrics_history) < 2:
            return {"error": "数据不足，无法进行趋势分析"}
        
        # 获取指定天数内的数据
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_metrics = [
            m for m in self.metrics_history
            if datetime.fromisoformat(m.timestamp) > cutoff_date
        ]
        
        if len(recent_metrics) < 2:
            return {"error": f"最近{days}天内数据不足"}
        
        # 计算趋势
        first_metric = recent_metrics[0]
        latest_metric = recent_metrics[-1]
        
        return {
            "period_days": days,
            "data_points": len(recent_metrics),
            "trends": {
                "coverage_change": latest_metric.test_coverage - first_metric.test_coverage,
                "quality_change": latest_metric.code_quality_score - first_metric.code_quality_score,
                "compliance_change": latest_metric.compliance_rate - first_metric.compliance_rate,
                "velocity_change": latest_metric.development_velocity - first_metric.development_velocity,
                "rework_reduction": first_metric.rework_rate - latest_metric.rework_rate
            },
            "current_status": {
                "coverage": latest_metric.test_coverage,
                "quality": latest_metric.code_quality_score,
                "compliance": latest_metric.compliance_rate,
                "performance": latest_metric.performance_score
            }
        }
    
    def _calculate_development_velocity(self) -> float:
        """计算开发效率(功能点/天)"""
        # 这里可以根据实际情况实现
        # 临时返回基于测试覆盖率的简单计算
        if len(self.metrics_history) >= 2:
            recent = self.metrics_history[-2:]
            if len(recent) == 2:
                time_diff = datetime.fromisoformat(recent[1].timestamp) - datetime.fromisoformat(recent[0].timestamp)
                test_growth = recent[1].test_count - recent[0].test_count
                if time_diff.total_seconds() > 0:
                    return test_growth / (time_diff.total_seconds() / 86400)  # 每天新增测试数
        return 0.0
    
    def _calculate_rework_rate(self) -> float:
        """计算返工率(基于测试失败率)"""
        if len(self.metrics_history) > 0:
            latest = self.metrics_history[-1]
            if latest.test_count > 0:
                return latest.failed_test_count / latest.test_count
        return 0.0
    
    def _calculate_defect_density(self) -> float:
        """计算缺陷密度(缺陷数/KLOC)"""
        # 临时实现：基于测试失败率
        if len(self.metrics_history) > 0:
            latest = self.metrics_history[-1]
            return latest.failed_test_count * 0.1  # 简化计算
        return 0.0
    
    def generate_real_time_dashboard_data(self) -> Dict[str, Any]:
        """生成实时仪表盘数据"""
        current_metrics = self.collect_current_metrics()
        trend_data = self.get_trend_analysis(7)
        
        return {
            "current_metrics": asdict(current_metrics),
            "trend_analysis": trend_data,
            "alerts": self._check_quality_alerts(current_metrics),
            "recommendations": self._generate_recommendations(current_metrics)
        }
    
    def _check_quality_alerts(self, metrics: TDDMetrics) -> List[Dict[str, str]]:
        """检查质量警报"""
        alerts = []
        
        if metrics.test_coverage < 0.9:
            alerts.append({
                "level": "warning",
                "message": f"测试覆盖率 {metrics.test_coverage:.1%} 低于90%目标",
                "action": "增加单元测试覆盖率"
            })
        
        if metrics.test_pass_rate < 1.0:
            alerts.append({
                "level": "error", 
                "message": f"测试通过率 {metrics.test_pass_rate:.1%} 不是100%",
                "action": "修复失败的测试"
            })
        
        if metrics.compliance_rate < 0.8:
            alerts.append({
                "level": "warning",
                "message": f"TDD合规率 {metrics.compliance_rate:.1%} 偏低",
                "action": "加强测试先行实践"
            })
        
        return alerts
    
    def _generate_recommendations(self, metrics: TDDMetrics) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if metrics.test_coverage < 0.95:
            recommendations.append("建议增加边界条件测试用例")
        
        if metrics.rework_rate > 0.1:
            recommendations.append("建议加强代码审查，减少返工")
        
        if metrics.development_velocity < 1.0:
            recommendations.append("建议优化TDD流程，提升开发效率")
        
        return recommendations 