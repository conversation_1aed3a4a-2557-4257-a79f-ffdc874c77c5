#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice-came异常类单元测试

测试所有自定义异常类的功能和继承关系
"""

import sys
import os
import pytest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from voice_came.exceptions import (
    VoiceCameError,
    FileValidationError,
    WhisperXError,
    TranslationError,
    BatchProcessingError,
    ConfigurationError,
    AudioProcessingError,
    QueueError,
    ProcessingError
)


class TestVoiceCameError:
    """基础异常类测试"""
    
    def test_voice_came_error_basic(self):
        """测试基础异常类的基本功能"""
        message = "这是一个测试错误"
        error = VoiceCameError(message)
        
        assert str(error) == message
        assert error.message == message
        assert error.error_code is None
        assert isinstance(error, Exception)
    
    def test_voice_came_error_with_code(self):
        """测试带错误代码的异常"""
        message = "系统错误"
        error_code = "SYS001"
        error = VoiceCameError(message, error_code)
        
        assert str(error) == message
        assert error.message == message
        assert error.error_code == error_code
    
    def test_voice_came_error_inheritance(self):
        """测试异常继承关系"""
        error = VoiceCameError("测试")
        assert isinstance(error, Exception)
        assert isinstance(error, VoiceCameError)
    
    def test_voice_came_error_empty_message(self):
        """测试空消息的异常"""
        error = VoiceCameError("")
        assert error.message == ""
        assert str(error) == ""
    
    def test_voice_came_error_none_code(self):
        """测试None错误代码"""
        error = VoiceCameError("测试", None)
        assert error.error_code is None


class TestFileValidationError:
    """文件验证异常测试"""
    
    def test_file_validation_error_basic(self):
        """测试文件验证异常基本功能"""
        message = "文件格式不支持"
        error = FileValidationError(message)
        
        assert str(error) == message
        assert error.message == message
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, FileValidationError)
    
    def test_file_validation_error_with_code(self):
        """测试带错误代码的文件验证异常"""
        message = "文件大小超出限制"
        error_code = "FILE001"
        error = FileValidationError(message, error_code)
        
        assert error.message == message
        assert error.error_code == error_code
    
    def test_file_validation_error_inheritance(self):
        """测试文件验证异常继承关系"""
        error = FileValidationError("测试")
        assert isinstance(error, Exception)
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, FileValidationError)


class TestWhisperXError:
    """WhisperX异常测试"""
    
    def test_whisperx_error_basic(self):
        """测试WhisperX异常基本功能"""
        message = "语音识别失败"
        error = WhisperXError(message)
        
        assert str(error) == message
        assert error.message == message
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, WhisperXError)
    
    def test_whisperx_error_with_code(self):
        """测试带错误代码的WhisperX异常"""
        message = "模型加载失败"
        error_code = "WX001"
        error = WhisperXError(message, error_code)
        
        assert error.message == message
        assert error.error_code == error_code


class TestTranslationError:
    """翻译异常测试"""
    
    def test_translation_error_basic(self):
        """测试翻译异常基本功能"""
        message = "翻译服务不可用"
        error = TranslationError(message)
        
        assert str(error) == message
        assert error.message == message
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, TranslationError)
    
    def test_translation_error_with_code(self):
        """测试带错误代码的翻译异常"""
        message = "API配额已用尽"
        error_code = "TRANS001"
        error = TranslationError(message, error_code)
        
        assert error.message == message
        assert error.error_code == error_code


class TestBatchProcessingError:
    """批量处理异常测试"""
    
    def test_batch_processing_error_basic(self):
        """测试批量处理异常基本功能"""
        message = "批量任务中断"
        error = BatchProcessingError(message)
        
        assert str(error) == message
        assert error.message == message
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, BatchProcessingError)
    
    def test_batch_processing_error_with_code(self):
        """测试带错误代码的批量处理异常"""
        message = "队列已满"
        error_code = "BATCH001"
        error = BatchProcessingError(message, error_code)
        
        assert error.message == message
        assert error.error_code == error_code


class TestConfigurationError:
    """配置异常测试"""
    
    def test_configuration_error_basic(self):
        """测试配置异常基本功能"""
        message = "配置文件缺失"
        error = ConfigurationError(message)
        
        assert str(error) == message
        assert error.message == message
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, ConfigurationError)
    
    def test_configuration_error_with_code(self):
        """测试带错误代码的配置异常"""
        message = "配置格式错误"
        error_code = "CONF001"
        error = ConfigurationError(message, error_code)
        
        assert error.message == message
        assert error.error_code == error_code


class TestAudioProcessingError:
    """音频处理异常测试"""
    
    def test_audio_processing_error_basic(self):
        """测试音频处理异常基本功能"""
        message = "音频解码失败"
        error = AudioProcessingError(message)
        
        assert str(error) == message
        assert error.message == message
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, AudioProcessingError)
    
    def test_audio_processing_error_with_code(self):
        """测试带错误代码的音频处理异常"""
        message = "音频格式不支持"
        error_code = "AUDIO001"
        error = AudioProcessingError(message, error_code)
        
        assert error.message == message
        assert error.error_code == error_code


class TestQueueError:
    """队列异常测试"""
    
    def test_queue_error_basic(self):
        """测试队列异常基本功能"""
        message = "队列操作失败"
        error = QueueError(message)
        
        assert str(error) == message
        assert error.message == message
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, QueueError)
    
    def test_queue_error_with_code(self):
        """测试带错误代码的队列异常"""
        message = "队列超时"
        error_code = "QUEUE001"
        error = QueueError(message, error_code)
        
        assert error.message == message
        assert error.error_code == error_code


class TestProcessingError:
    """处理异常测试"""
    
    def test_processing_error_basic(self):
        """测试处理异常基本功能"""
        message = "文件处理失败"
        error = ProcessingError(message)
        
        assert str(error) == message
        assert error.message == message
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, ProcessingError)
    
    def test_processing_error_with_code(self):
        """测试带错误代码的处理异常"""
        message = "处理超时"
        error_code = "PROC001"
        error = ProcessingError(message, error_code)
        
        assert error.message == message
        assert error.error_code == error_code


class TestExceptionRaising:
    """异常抛出测试"""
    
    def test_raise_voice_came_error(self):
        """测试抛出VoiceCameError异常"""
        with pytest.raises(VoiceCameError) as exc_info:
            raise VoiceCameError("测试异常")
        
        assert str(exc_info.value) == "测试异常"
    
    def test_raise_file_validation_error(self):
        """测试抛出FileValidationError异常"""
        with pytest.raises(FileValidationError) as exc_info:
            raise FileValidationError("文件验证失败", "FILE001")
        
        assert exc_info.value.error_code == "FILE001"
    
    def test_catch_base_exception(self):
        """测试捕获基础异常"""
        with pytest.raises(VoiceCameError):
            raise FileValidationError("测试")
        
        with pytest.raises(VoiceCameError):
            raise TranslationError("测试")
        
        with pytest.raises(VoiceCameError):
            raise BatchProcessingError("测试")


class TestExceptionEdgeCases:
    """异常边界情况测试"""
    
    def test_unicode_message(self):
        """测试Unicode消息"""
        message = "这是一个包含Unicode字符的错误消息：🚨⚠️"
        error = VoiceCameError(message)
        assert error.message == message
    
    def test_very_long_message(self):
        """测试超长消息"""
        message = "x" * 10000
        error = VoiceCameError(message)
        assert len(error.message) == 10000
    
    def test_special_characters_in_code(self):
        """测试错误代码中的特殊字符"""
        error_code = "ERR-001_TEST"
        error = VoiceCameError("测试", error_code)
        assert error.error_code == error_code
    
    def test_none_message_handling(self):
        """测试None消息处理"""
        # VoiceCameError实际上可以接受None作为消息
        error = VoiceCameError(None)
        assert error.message is None
        assert str(error) == 'None'


# 测试所有异常类型
def test_all_exception_types():
    """测试所有异常类型都能正常实例化"""
    exceptions = [
        VoiceCameError,
        FileValidationError,
        WhisperXError,
        TranslationError,
        BatchProcessingError,
        ConfigurationError,
        AudioProcessingError,
        QueueError,
        ProcessingError
    ]
    
    for exc_class in exceptions:
        error = exc_class("测试消息", "TEST001")
        assert isinstance(error, VoiceCameError)
        assert isinstance(error, Exception)
        assert error.message == "测试消息"
        assert error.error_code == "TEST001"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 