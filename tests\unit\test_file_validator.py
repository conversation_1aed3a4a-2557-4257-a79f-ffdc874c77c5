#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件验证功能测试用例

TDD-Red阶段：编写所有测试用例，确保初始状态为FAIL
测试文件格式验证、大小限制、批量验证等核心功能
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from voice_came.core.file_validator import FileValidator
from voice_came.exceptions import FileValidationError


class TestFileValidator:
    """文件验证器测试类"""
    
    @pytest.fixture
    def validator(self):
        """测试用的文件验证器实例"""
        # 禁用文件头检查，专注于核心验证逻辑
        config = {'check_file_header': False}
        return FileValidator(config=config)
    
    @pytest.fixture
    def sample_files(self, tmp_path):
        """创建测试用的示例文件"""
        files = {}
        
        # 创建不同格式的测试文件
        files['mp4'] = tmp_path / "test_video.mp4"
        files['mp4'].write_bytes(b"fake mp4 data" * 1000)  # 约13KB
        
        files['avi'] = tmp_path / "test_video.avi"  
        files['avi'].write_bytes(b"fake avi data" * 1000)
        
        files['mov'] = tmp_path / "test_video.mov"
        files['mov'].write_bytes(b"fake mov data" * 1000)
        
        files['wav'] = tmp_path / "test_audio.wav"
        files['wav'].write_bytes(b"fake wav data" * 1000)
        
        files['mp3'] = tmp_path / "test_audio.mp3"
        files['mp3'].write_bytes(b"fake mp3 data" * 1000)
        
        # 不支持的格式
        files['txt'] = tmp_path / "test_doc.txt"
        files['txt'].write_text("fake text content")
        
        files['pdf'] = tmp_path / "test_doc.pdf"
        files['pdf'].write_bytes(b"fake pdf data")
        
        # 空文件
        files['empty'] = tmp_path / "empty.mp4"
        files['empty'].write_bytes(b"")
        
        # 大文件 (模拟5GB)
        files['large'] = tmp_path / "large_video.mp4"
        # 不实际创建5GB文件，而是mock文件大小
        files['large'].write_bytes(b"fake large video data")
        
        return files

    # === 支持格式测试 ===
    
    @pytest.mark.parametrize("file_format", ["mp4", "avi", "mov", "wav", "mp3"])
    def test_validate_supported_formats_success(self, validator, sample_files, file_format):
        """测试支持的文件格式验证通过"""
        file_path = sample_files[file_format]
        
        result = validator.validate_file(file_path)
        
        assert result.is_valid is True
        assert result.file_path == file_path
        assert result.file_format == file_format
        assert result.file_size > 0
        assert result.errors == []
    
    @pytest.mark.parametrize("file_format", ["txt", "pdf", "doc", "xlsx"])
    def test_validate_unsupported_formats_fail(self, validator, sample_files, file_format):
        """测试不支持的文件格式验证失败"""
        if file_format in sample_files:
            file_path = sample_files[file_format]
        else:
            # 动态创建不支持格式的文件
            file_path = sample_files['txt'].parent / f"test.{file_format}"
            file_path.write_bytes(b"fake data")
        
        result = validator.validate_file(file_path)
        
        assert result.is_valid is False
        assert f"不支持的文件格式: {file_format}" in result.errors
    
    def test_validate_file_without_extension(self, validator, tmp_path):
        """测试无扩展名文件验证失败"""
        file_path = tmp_path / "no_extension_file"
        file_path.write_bytes(b"some data")
        
        result = validator.validate_file(file_path)
        
        assert result.is_valid is False
        assert "文件无扩展名" in result.errors
    
    # === 文件大小测试 ===
    
    def test_validate_file_size_within_limit(self, validator, sample_files):
        """测试文件大小在限制内验证通过"""
        file_path = sample_files['mp4']
        
        result = validator.validate_file(file_path)
        
        assert result.is_valid is True
        assert result.file_size < validator.MAX_FILE_SIZE
        assert result.file_size > 0
    
    def test_validate_file_size_exceeds_limit(self, validator, sample_files):
        """测试文件大小超过限制验证失败"""
        file_path = sample_files['large']
        
        # Mock os.stat函数，确保返回正确的stat_result对象
        with patch('voice_came.core.file_validator.os.stat') as mock_stat:
            # 创建一个完整的Mock对象来模拟os.stat_result
            from types import SimpleNamespace
            mock_result = SimpleNamespace()
            mock_result.st_size = 5 * 1024 * 1024 * 1024  # 5GB
            mock_result.st_mode = 0o100644  # 普通文件权限
            mock_result.st_mtime = 1640995200  # 时间戳
            mock_stat.return_value = mock_result
            
            result = validator.validate_file(file_path)
            
            assert result.is_valid is False
            assert "文件大小超过限制" in result.errors[0]
            assert "4.0GB" in result.errors[0]  # 应该显示限制大小
    
    def test_validate_empty_file_fail(self, validator, sample_files):
        """测试空文件验证失败"""
        file_path = sample_files['empty']
        
        result = validator.validate_file(file_path)
        
        assert result.is_valid is False
        assert "文件为空" in result.errors
    
    # === 文件存在性测试 ===
    
    def test_validate_nonexistent_file_fail(self, validator):
        """测试不存在的文件验证失败"""
        file_path = Path("/nonexistent/path/file.mp4")
        
        result = validator.validate_file(file_path)
        
        assert result.is_valid is False
        assert "文件不存在" in result.errors
    
    def test_validate_directory_as_file_fail(self, validator, tmp_path):
        """测试将目录当作文件验证失败"""
        dir_path = tmp_path / "test_directory"
        dir_path.mkdir()
        
        result = validator.validate_file(dir_path)
        
        assert result.is_valid is False
        assert "不是一个文件" in result.errors
    
    # === 文件损坏测试 ===
    
    def test_validate_corrupted_file_fail(self, tmp_path):
        """测试损坏文件验证失败"""
        # 创建启用文件头检查的验证器
        config = {'check_file_header': True}
        validator_with_header_check = FileValidator(config=config)
        
        # 创建一个看起来像视频但内容损坏的文件
        corrupted_file = tmp_path / "corrupted.mp4"
        corrupted_file.write_bytes(b"this is not a valid mp4 file content")
        
        # Mock文件头检查
        with patch.object(validator_with_header_check, '_check_file_header') as mock_check:
            mock_check.return_value = False
            
            result = validator_with_header_check.validate_file(corrupted_file)
            
            assert result.is_valid is False
            assert "文件头验证失败" in result.errors
    
    # === 批量验证测试 ===
    
    def test_batch_validate_all_valid_files(self, validator, sample_files):
        """测试批量验证所有有效文件"""
        valid_files = [
            sample_files['mp4'],
            sample_files['avi'], 
            sample_files['mov'],
            sample_files['wav'],
            sample_files['mp3']
        ]
        
        results = validator.batch_validate(valid_files)
        
        assert len(results) == 5
        assert all(result.is_valid for result in results)
        assert len([r for r in results if r.is_valid]) == 5
    
    def test_batch_validate_mixed_files(self, validator, sample_files):
        """测试批量验证混合文件（有效和无效）"""
        mixed_files = [
            sample_files['mp4'],  # 有效
            sample_files['txt'],  # 无效格式
            sample_files['avi'],  # 有效
            sample_files['empty'], # 无效大小
        ]
        
        results = validator.batch_validate(mixed_files)
        
        assert len(results) == 4
        valid_results = [r for r in results if r.is_valid]
        invalid_results = [r for r in results if not r.is_valid]
        
        assert len(valid_results) == 2  # mp4, avi
        assert len(invalid_results) == 2  # txt, empty
    
    def test_batch_validate_empty_list(self, validator):
        """测试批量验证空列表"""
        results = validator.batch_validate([])
        
        assert results == []
    
    def test_batch_validate_with_progress_callback(self, validator, sample_files):
        """测试带进度回调的批量验证"""
        files = [sample_files['mp4'], sample_files['avi']]
        progress_calls = []
        
        def progress_callback(current, total, file_name):
            progress_calls.append((current, total, file_name))
        
        results = validator.batch_validate(files, progress_callback=progress_callback)
        
        assert len(results) == 2
        assert len(progress_calls) == 2
        assert progress_calls[0] == (1, 2, files[0].name)
        assert progress_calls[1] == (2, 2, files[1].name)
    
    # === 错误处理测试 ===
    
    def test_validate_with_permission_error(self, validator, tmp_path):
        """测试文件权限错误处理"""
        file_path = tmp_path / "permission_test.mp4"
        file_path.write_bytes(b"test data")
        
        # Mock权限错误
        with patch('voice_came.core.file_validator.os.stat', side_effect=PermissionError("Permission denied")):
            result = validator.validate_file(file_path)
            
            assert result.is_valid is False
            # 检查错误消息（可能被外层异常处理器捕获）
            assert ("权限不足" in result.errors[0] or "Permission denied" in result.errors[0])
    
    def test_validate_with_os_error(self, validator, tmp_path):
        """测试操作系统错误处理"""
        file_path = tmp_path / "os_error_test.mp4"
        file_path.write_bytes(b"test data")
        
        # Mock OS错误
        with patch('voice_came.core.file_validator.os.stat', side_effect=OSError("OS error")):
            result = validator.validate_file(file_path)
            
            assert result.is_valid is False
            # 检查错误消息（可能被外层异常处理器捕获）
            assert ("文件访问错误" in result.errors[0] or "OS error" in result.errors[0])
    
    # === 配置参数测试 ===
    
    def test_validator_with_custom_config(self):
        """测试自定义配置的验证器"""
        custom_config = {
            'max_file_size': 1024 * 1024,  # 1MB
            'supported_formats': ['mp4', 'avi'],
            'check_file_header': False
        }
        
        validator = FileValidator(config=custom_config)
        
        assert validator.MAX_FILE_SIZE == 1024 * 1024
        assert validator.SUPPORTED_FORMATS == ['mp4', 'avi']
        assert validator.CHECK_FILE_HEADER is False
    
    def test_get_supported_formats(self, validator):
        """测试获取支持的格式列表"""
        formats = validator.get_supported_formats()
        
        expected_formats = ['mp4', 'avi', 'mov', 'mkv', 'wav', 'mp3', 'flac', 'aac']
        assert formats == expected_formats
    
    def test_get_max_file_size_human_readable(self, validator):
        """测试获取人类可读的最大文件大小"""
        size_str = validator.get_max_file_size_str()
        
        assert size_str == "4.0GB"
    
    # === 性能测试 ===
    
    @pytest.mark.slow
    def test_batch_validate_performance(self, validator, tmp_path):
        """测试批量验证性能"""
        import time
        
        # 创建100个测试文件
        files = []
        for i in range(100):
            file_path = tmp_path / f"test_file_{i}.mp4"
            file_path.write_bytes(b"test data" * 100)
            files.append(file_path)
        
        start_time = time.time()
        results = validator.batch_validate(files)
        end_time = time.time()
        
        # 验证性能要求：100个文件应该在2秒内完成
        processing_time = end_time - start_time
        assert processing_time < 2.0, f"批量验证耗时过长: {processing_time}秒"
        assert len(results) == 100
        assert all(result.is_valid for result in results)
    
    # === 集成测试 ===
    
    @pytest.mark.integration
    def test_file_validator_integration_with_real_files(self, validator):
        """测试文件验证器与真实文件的集成"""
        # 这个测试需要真实的媒体文件
        # 在实际环境中，可以放置一些测试用的真实媒体文件
        pass
    
    # === 边界条件测试 ===
    
    def test_validate_file_at_size_boundary(self, validator, tmp_path):
        """测试文件大小边界条件"""
        # 创建刚好达到大小限制的文件
        boundary_file = tmp_path / "boundary.mp4"
        boundary_file.write_bytes(b"test")
        
        # Mock文件大小为刚好4GB
        with patch('voice_came.core.file_validator.os.stat') as mock_stat:
            from types import SimpleNamespace
            mock_result = SimpleNamespace()
            mock_result.st_size = validator.MAX_FILE_SIZE
            mock_result.st_mode = 0o100644
            mock_result.st_mtime = 1640995200
            mock_stat.return_value = mock_result
            
            result = validator.validate_file(boundary_file)
            
            assert result.is_valid is True  # 等于限制应该通过
    
    def test_validate_file_just_over_size_boundary(self, validator, tmp_path):
        """测试文件大小刚好超过边界"""
        boundary_file = tmp_path / "over_boundary.mp4"
        boundary_file.write_bytes(b"test")
        
        # Mock文件大小为刚好超过4GB
        with patch('voice_came.core.file_validator.os.stat') as mock_stat:
            from types import SimpleNamespace
            mock_result = SimpleNamespace()
            mock_result.st_size = validator.MAX_FILE_SIZE + 1
            mock_result.st_mode = 0o100644
            mock_result.st_mtime = 1640995200
            mock_stat.return_value = mock_result
            
            result = validator.validate_file(boundary_file)
            
            assert result.is_valid is False
            assert "文件大小超过限制" in result.errors[0] 