@echo off
REM Voice-came Windows 环境设置脚本
REM 支持 Windows 10/11

echo ===============================================
echo   Voice-came 语音翻译系统 - Windows 环境设置
echo ===============================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [警告] 建议以管理员身份运行此脚本以安装系统依赖
    echo.
)

REM 检查Python
echo [步骤 1/6] 检查Python环境...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 未找到Python，请先安装Python 3.8-3.11
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    python --version
    echo [✓] Python检查通过
)
echo.

REM 检查pip
echo [步骤 2/6] 检查pip...
python -m pip --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] pip未正确安装
    pause
    exit /b 1
) else (
    echo [✓] pip可用
)
echo.

REM 检查Git
echo [步骤 3/6] 检查Git...
git --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [警告] 未找到Git，建议安装Git for Windows
    echo 下载地址: https://git-scm.com/download/win
) else (
    git --version
    echo [✓] Git可用
)
echo.

REM 安装Chocolatey（如果需要）
echo [步骤 4/6] 检查包管理器...
choco --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [信息] 未检测到Chocolatey
    set /p INSTALL_CHOCO="是否安装Chocolatey包管理器？(y/n): "
    if /i "%INSTALL_CHOCO%"=="y" (
        echo [信息] 安装Chocolatey...
        @powershell -NoProfile -ExecutionPolicy Bypass -Command "iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))"
        if %errorLevel% neq 0 (
            echo [警告] Chocolatey安装失败，将跳过FFmpeg自动安装
        ) else (
            echo [✓] Chocolatey安装成功
        )
    )
) else (
    choco --version
    echo [✓] Chocolatey可用
)
echo.

REM 安装FFmpeg
echo [步骤 5/6] 检查FFmpeg...
ffmpeg -version >nul 2>&1
if %errorLevel% neq 0 (
    echo [信息] 未找到FFmpeg
    choco --version >nul 2>&1
    if %errorLevel% equ 0 (
        set /p INSTALL_FFMPEG="是否通过Chocolatey安装FFmpeg？(y/n): "
        if /i "%INSTALL_FFMPEG%"=="y" (
            echo [信息] 安装FFmpeg...
            choco install ffmpeg -y
            if %errorLevel% neq 0 (
                echo [警告] FFmpeg安装失败，请手动安装
            ) else (
                echo [✓] FFmpeg安装成功
            )
        )
    ) else (
        echo [警告] 请手动安装FFmpeg并添加到PATH
        echo 下载地址: https://ffmpeg.org/download.html
    )
) else (
    echo [✓] FFmpeg可用
)
echo.

REM 检查NVIDIA GPU
echo [步骤 6/6] 检查GPU支持...
nvidia-smi >nul 2>&1
if %errorLevel% neq 0 (
    echo [信息] 未检测到NVIDIA GPU，将使用CPU版本
    set GPU_SUPPORT=false
) else (
    echo [✓] 检测到NVIDIA GPU
    nvidia-smi | findstr "CUDA Version"
    set GPU_SUPPORT=true
)
echo.

REM 创建虚拟环境
echo ===============================================
echo   开始安装Python依赖
echo ===============================================
echo.

set /p CREATE_VENV="是否创建Python虚拟环境？(推荐, y/n): "
if /i "%CREATE_VENV%"=="y" (
    echo [信息] 创建虚拟环境 'voice-came-env'...
    python -m venv voice-came-env
    if %errorLevel% neq 0 (
        echo [错误] 虚拟环境创建失败
        pause
        exit /b 1
    )
    
    echo [信息] 激活虚拟环境...
    call voice-came-env\Scripts\activate.bat
    if %errorLevel% neq 0 (
        echo [错误] 虚拟环境激活失败
        pause
        exit /b 1
    )
    echo [✓] 虚拟环境已激活
    echo.
)

REM 安装依赖
echo [信息] 运行Python依赖安装脚本...
python scripts\install_dependencies.py
if %errorLevel% neq 0 (
    echo [错误] 依赖安装失败
    pause
    exit /b 1
)

echo.
echo ===============================================
echo   安装完成！
echo ===============================================
echo.

if /i "%CREATE_VENV%"=="y" (
    echo 下次使用前，请激活虚拟环境：
    echo   voice-came-env\Scripts\activate.bat
    echo.
)

echo 测试安装：
echo   python test_installation.py
echo.

echo 开始使用Voice-came系统！
echo.

pause 