#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本兼容性检查脚本
"""

import sys

def check_versions():
    print("🔍 系统环境检查")
    print("=" * 40)
    
    print(f"Python版本: {sys.version}")
    
    try:
        import numpy as np
        print(f"NumPy版本: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy导入失败: {e}")
        return
    
    try:
        import librosa
        print(f"Librosa版本: {librosa.__version__}")
    except ImportError as e:
        print(f"❌ Librosa导入失败: {e}")
        return
    
    try:
        import numba
        print(f"Numba版本: {numba.__version__}")
    except ImportError as e:
        print(f"⚠️ Numba未安装: {e}")
    
    # 测试基础音频处理
    try:
        print("\n🎵 测试音频处理功能...")
        # 生成测试音频
        import numpy as np
        duration = 1.0  # 1秒
        sr = 22050
        t = np.linspace(0, duration, int(sr * duration))
        y = 0.1 * np.sin(2 * np.pi * 440 * t)  # 440Hz正弦波
        
        # 测试librosa基础功能
        rms = librosa.feature.rms(y=y)[0]
        print(f"✅ RMS计算成功: {rms.mean():.6f}")
        
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
        print(f"✅ 频谱质心计算成功: {spectral_centroids.mean():.2f}Hz")
        
        zcr = librosa.feature.zero_crossing_rate(y)[0]
        print(f"✅ 过零率计算成功: {zcr.mean():.4f}")
        
        print("\n✅ 音频处理功能正常！")
        
    except Exception as e:
        print(f"❌ 音频处理测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_versions()