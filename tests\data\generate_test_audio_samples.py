#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成测试音频样本

为语音活动检测(SAD)测试生成各种类型的音频样本
"""

import numpy as np
import wave
from pathlib import Path
from typing import Optional

def generate_sine_wave(frequency: float, duration: float, sample_rate: int = 16000, amplitude: float = 0.5) -> np.ndarray:
    """生成正弦波"""
    t = np.linspace(0, duration, int(sample_rate * duration))
    return amplitude * np.sin(2 * np.pi * frequency * t)

def generate_white_noise(duration: float, sample_rate: int = 16000, amplitude: float = 0.1) -> np.ndarray:
    """生成白噪声"""
    samples = int(sample_rate * duration)
    return amplitude * np.random.randn(samples)

def generate_silence(duration: float, sample_rate: int = 16000) -> np.ndarray:
    """生成静音"""
    samples = int(sample_rate * duration)
    return np.zeros(samples)

def save_audio_as_wav(audio_data: np.ndarray, filename: str, sample_rate: int = 16000):
    """保存音频为WAV文件"""
    # 确保音频数据在有效范围内
    audio_data = np.clip(audio_data, -1.0, 1.0)
    
    # 转换为16位整数
    audio_int16 = (audio_data * 32767).astype(np.int16)
    
    filepath = Path(__file__).parent / "audio_samples" / filename
    
    with wave.open(str(filepath), 'w') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_int16.tobytes())
    
    print(f"Generated: {filepath}")

def generate_all_test_samples():
    """生成所有测试样本"""
    sample_rate = 16000
    
    # 1. 纯语音样本 (5秒)
    speech_audio = generate_sine_wave(440, 5.0, sample_rate, 0.5)  # 440Hz音调
    save_audio_as_wav(speech_audio, "speech_only_5s.wav", sample_rate)
    
    # 2. 纯静音样本 (3秒)
    silence_audio = generate_silence(3.0, sample_rate)
    save_audio_as_wav(silence_audio, "silence_only_3s.wav", sample_rate)
    
    # 3. 语音+静音+语音 (5秒总长度)
    speech_part1 = generate_sine_wave(440, 2.0, sample_rate, 0.5)
    silence_part = generate_silence(1.0, sample_rate)
    speech_part2 = generate_sine_wave(880, 2.0, sample_rate, 0.5)  # 更高音调
    speech_silence_speech = np.concatenate([speech_part1, silence_part, speech_part2])
    save_audio_as_wav(speech_silence_speech, "speech_silence_speech_5s.wav", sample_rate)
    
    # 4. 长音频样本 (60秒)
    long_speech = generate_sine_wave(440, 60.0, sample_rate, 0.3)
    save_audio_as_wav(long_speech, "long_audio_60s.wav", sample_rate)
    
    # 5. 噪声环境下的语音 (4秒)
    clean_speech = generate_sine_wave(440, 4.0, sample_rate, 0.6)
    noise = generate_white_noise(4.0, sample_rate, 0.1)
    noisy_speech = clean_speech + noise
    save_audio_as_wav(noisy_speech, "noisy_speech_4s.wav", sample_rate)
    
    # 6. 极短音频 (0.5秒)
    very_short = generate_sine_wave(440, 0.5, sample_rate, 0.5)
    save_audio_as_wav(very_short, "very_short_0.5s.wav", sample_rate)
    
    # 7. ASMR低音量样本 (10秒)
    asmr_whisper = generate_sine_wave(200, 10.0, sample_rate, 0.1)  # 低频低音量
    save_audio_as_wav(asmr_whisper, "asmr_whisper_10s.wav", sample_rate)
    
    # 8. 多频率混合语音 (6秒)
    freq1 = generate_sine_wave(300, 2.0, sample_rate, 0.3)
    freq2 = generate_sine_wave(600, 2.0, sample_rate, 0.3)
    freq3 = generate_sine_wave(900, 2.0, sample_rate, 0.3)
    mixed_freq = np.concatenate([freq1, freq2, freq3])
    save_audio_as_wav(mixed_freq, "mixed_frequency_6s.wav", sample_rate)
    
    # 9. 间歇性语音 (8秒)
    intermittent = np.array([])
    for i in range(4):
        speech_burst = generate_sine_wave(440, 1.0, sample_rate, 0.5)
        silence_gap = generate_silence(1.0, sample_rate)
        intermittent = np.concatenate([intermittent, speech_burst, silence_gap])
    save_audio_as_wav(intermittent, "intermittent_speech_8s.wav", sample_rate)
    
    # 10. 梯度音量样本 (5秒)
    t = np.linspace(0, 5.0, sample_rate * 5)
    gradient_amplitude = np.linspace(0.1, 0.8, len(t))  # 音量从小到大
    gradient_audio = gradient_amplitude * np.sin(2 * np.pi * 440 * t)
    save_audio_as_wav(gradient_audio, "gradient_volume_5s.wav", sample_rate)

if __name__ == "__main__":
    print("正在生成测试音频样本...")
    generate_all_test_samples()
    print("所有测试音频样本生成完成！") 