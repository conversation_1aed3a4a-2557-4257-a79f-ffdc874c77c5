#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4集成测试和质量验证脚本

系统性验证VoiceTransl翻译引擎集成的完整实现，
包括基础功能、集成测试、性能验证等。
"""

import sys
import os
import asyncio
import time
import traceback
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.insert(0, 'src')

class Task4IntegrationValidator:
    """Task 4集成验证器"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        self.error_log = []
        
    def log_error(self, test_name: str, error: Exception):
        """记录错误"""
        error_info = {
            "test": test_name,
            "error": str(error),
            "traceback": traceback.format_exc()
        }
        self.error_log.append(error_info)
        print(f"❌ {test_name}: {error}")
    
    def log_success(self, test_name: str, details: str = ""):
        """记录成功"""
        self.test_results[test_name] = True
        print(f"✅ {test_name}: {details}")
    
    def test_basic_imports(self) -> bool:
        """测试基础导入"""
        print("\n📋 测试基础模块导入...")
        
        try:
            # 测试翻译模块导入
            from voice_came.translation.models import (
                TranslationConfig, TranslationResult, AudioSegment
            )
            self.log_success("翻译模型导入", "基础数据模型正常")
            
            from voice_came.translation.integration import VoiceTranslAdapter
            self.log_success("VoiceTransl适配器导入", "集成模块正常")
            
            from voice_came.translation.terminology import SleepTerminologyManager
            self.log_success("术语管理器导入", "术语模块正常")
            
            from voice_came.translation.quality import SleepContentQualityAssessor
            self.log_success("质量评估器导入", "质量模块正常")
            
            from voice_came.translation.optimization import (
                SleepContentPromptGenerator, TranslationQualityOptimizer
            )
            self.log_success("质量优化器导入", "优化模块正常")
            
            return True
            
        except Exception as e:
            self.log_error("基础模块导入", e)
            return False
    
    def test_ui_imports(self) -> bool:
        """测试UI模块导入"""
        print("\n📋 测试UI模块导入...")
        
        try:
            from voice_came.ui.translation_panel import TranslationControlPanel
            self.log_success("翻译控制面板导入", "UI控制模块正常")
            
            from voice_came.ui.progress_monitor import RealTimeProgressMonitor
            self.log_success("进度监控器导入", "监控模块正常")
            
            from voice_came.ui.batch_interface import BatchProcessingInterface
            self.log_success("批量处理界面导入", "批量模块正常")
            
            from voice_came.ui.results_viewer import TranslationResultsViewer
            self.log_success("结果查看器导入", "结果模块正常")
            
            return True
            
        except Exception as e:
            self.log_error("UI模块导入", e)
            return False
    
    def test_data_models_functionality(self) -> bool:
        """测试数据模型功能"""
        print("\n📋 测试数据模型功能...")
        
        try:
            from voice_came.translation.models import (
                TranslationConfig, TranslationResult, AudioSegment, 
                WhisperXOutput, VoiceTranslOutput
            )
            
            # 测试AudioSegment创建
            segment = AudioSegment(
                start_time=0.0,
                end_time=5.0,
                text="测试文本",
                confidence=0.9
            )
            assert segment.start_time == 0.0
            assert segment.text == "测试文本"
            self.log_success("AudioSegment创建", f"时长: {segment.end_time - segment.start_time}s")
            
            # 测试TranslationConfig创建
            config = TranslationConfig(
                source_language="zh",
                target_language="en",
                terminology_enabled=True,
                quality_optimization=True
            )
            assert config.source_language == "zh"
            assert config.terminology_enabled is True
            self.log_success("TranslationConfig创建", f"语言对: {config.source_language}→{config.target_language}")
            
            # 测试TranslationResult创建
            result = TranslationResult(
                job_id="test_job",
                original_text="原始文本",
                translated_text="translated text",
                quality_score=0.85,
                terminology_applied=[],
                processing_time=1.5,
                metadata={}
            )
            assert result.quality_score == 0.85
            self.log_success("TranslationResult创建", f"质量分数: {result.quality_score}")
            
            return True
            
        except Exception as e:
            self.log_error("数据模型功能", e)
            return False
    
    def test_voicetransl_adapter_basic(self) -> bool:
        """测试VoiceTransl适配器基础功能"""
        print("\n📋 测试VoiceTransl适配器基础功能...")
        
        try:
            from voice_came.translation.integration import VoiceTranslAdapter
            from voice_came.translation.models import WhisperXOutput, AudioSegment
            
            # 创建适配器
            adapter = VoiceTranslAdapter("/fake/voicetransl/path")
            self.log_success("VoiceTransl适配器创建", "适配器实例化成功")
            
            # 测试数据转换
            whisperx_output = WhisperXOutput(
                segments=[
                    AudioSegment(0.0, 5.0, "测试冥想练习", 0.9),
                    AudioSegment(5.0, 10.0, "深度放松训练", 0.85)
                ],
                language="zh",
                metadata={"duration": 10.0}
            )
            
            converted_data = adapter.convert_whisperx_to_voicetransl(whisperx_output)
            assert "segments" in converted_data
            assert len(converted_data["segments"]) == 2
            self.log_success("数据格式转换", f"转换了 {len(converted_data['segments'])} 个段落")
            
            return True
            
        except Exception as e:
            self.log_error("VoiceTransl适配器基础功能", e)
            return False
    
    def test_terminology_manager_basic(self) -> bool:
        """测试术语管理器基础功能"""
        print("\n📋 测试术语管理器基础功能...")
        
        try:
            from voice_came.translation.terminology import SleepTerminologyManager
            
            # 创建术语管理器
            manager = SleepTerminologyManager("fake_terminology.yaml")
            self.log_success("术语管理器创建", "管理器实例化成功")
            
            # 测试术语预处理
            test_text = "今晚我们将通过冥想和放松来获得更好的助眠效果"
            processed_text = manager.preprocess_for_translation(test_text, "zh")
            
            # 检查是否包含占位符
            has_placeholders = "[SLEEP_TERM_" in processed_text
            self.log_success("术语预处理", f"占位符替换: {'是' if has_placeholders else '否'}")
            
            # 测试术语后处理
            translated_with_placeholders = "Tonight we will use [SLEEP_TERM_123] and [SLEEP_TERM_456] for better [SLEEP_TERM_789]"
            final_text = manager.postprocess_translation(translated_with_placeholders, "en")
            
            # 检查是否正确替换
            has_meditation = "meditation" in final_text
            has_relaxation = "relaxation" in final_text
            self.log_success("术语后处理", f"术语替换: meditation={has_meditation}, relaxation={has_relaxation}")
            
            return True
            
        except Exception as e:
            self.log_error("术语管理器基础功能", e)
            return False
    
    def test_quality_assessor_basic(self) -> bool:
        """测试质量评估器基础功能"""
        print("\n📋 测试质量评估器基础功能...")
        
        try:
            from voice_came.translation.quality import SleepContentQualityAssessor
            
            # 创建质量评估器
            assessor = SleepContentQualityAssessor()
            self.log_success("质量评估器创建", "评估器实例化成功")
            
            # 测试质量评估
            source_text = "冥想和放松练习有助于改善睡眠质量"
            translation = "Meditation and relaxation exercises help improve sleep quality"
            
            quality_score = assessor.assess_translation_quality(source_text, translation, "en")
            
            assert hasattr(quality_score, 'overall_score')
            assert 0.0 <= quality_score.overall_score <= 1.0
            self.log_success("翻译质量评估", f"整体分数: {quality_score.overall_score:.2f}")
            
            # 测试语调评估
            calming_text = "Gently close your eyes and breathe softly"
            aggressive_text = "Quickly shut your eyes and breathe hard"
            
            calming_score = assessor.assess_calming_tone(calming_text)
            aggressive_score = assessor.assess_calming_tone(aggressive_text)
            
            assert calming_score > aggressive_score
            self.log_success("语调评估", f"舒缓语调分数更高: {calming_score:.2f} > {aggressive_score:.2f}")
            
            return True
            
        except Exception as e:
            self.log_error("质量评估器基础功能", e)
            return False
    
    def test_optimization_modules(self) -> bool:
        """测试优化模块功能"""
        print("\n📋 测试优化模块功能...")
        
        try:
            from voice_came.translation.optimization import (
                SleepContentPromptGenerator, TranslationQualityOptimizer, AutomaticRetryManager
            )
            from voice_came.translation.models import TranslationResult
            
            # 测试提示词生成器
            prompt_generator = SleepContentPromptGenerator()
            meditation_prompt = prompt_generator.generate_meditation_prompt("冥想练习", None)
            assert "meditation" in meditation_prompt.lower()
            self.log_success("提示词生成器", f"生成了 {len(meditation_prompt)} 字符的提示词")
            
            # 测试质量优化器
            optimizer = TranslationQualityOptimizer()
            test_result = TranslationResult(
                job_id="test",
                original_text="冥想练习",
                translated_text="thinking practice",  # 错误术语
                quality_score=0.4,
                terminology_applied=[],
                processing_time=1.0,
                metadata={}
            )
            
            improved_result = optimizer.improve_translation_quality(test_result)
            assert improved_result.quality_score > test_result.quality_score
            self.log_success("质量优化器", f"质量提升: {test_result.quality_score:.2f} → {improved_result.quality_score:.2f}")
            
            # 测试重试管理器
            retry_manager = AutomaticRetryManager(max_retries=3, quality_threshold=0.8)
            should_retry = retry_manager.should_retry(test_result)
            assert should_retry is True
            self.log_success("重试管理器", f"低质量翻译触发重试: {should_retry}")
            
            return True
            
        except Exception as e:
            self.log_error("优化模块功能", e)
            return False
    
    async def test_ui_components_basic(self) -> bool:
        """测试UI组件基础功能"""
        print("\n📋 测试UI组件基础功能...")
        
        try:
            from voice_came.ui.translation_panel import TranslationControlPanel
            from voice_came.ui.progress_monitor import RealTimeProgressMonitor
            from voice_came.ui.batch_interface import BatchProcessingInterface
            from voice_came.ui.results_viewer import TranslationResultsViewer
            
            # 测试翻译控制面板
            panel = TranslationControlPanel()
            mock_files = [
                {"path": "/test/audio1.mp3", "name": "meditation.mp3", "size": 1024, "duration": 300}
            ]
            panel.load_audio_files(mock_files)
            panel.select_files(["/test/audio1.mp3"])
            
            settings = {
                "source_language": "zh",
                "target_language": "en",
                "quality_level": "high",
                "terminology_enabled": True
            }
            panel.configure_translation_settings(settings)
            
            job = panel.create_translation_job()
            assert job.job_id is not None
            self.log_success("翻译控制面板", f"创建任务: {job.job_id}")
            
            # 测试进度监控器
            monitor = RealTimeProgressMonitor()
            monitor.update_overall_progress(0.5)
            monitor.update_current_file("test.mp3")
            
            progress = monitor.get_current_progress()
            assert progress["overall_progress"] == 0.5
            self.log_success("进度监控器", f"进度更新: {progress['overall_progress']:.1%}")
            
            # 测试批量处理界面
            batch_interface = BatchProcessingInterface()
            batch_config = {
                "job_name": "测试批量任务",
                "source_language": "zh",
                "target_languages": ["en"],
                "parallel_workers": 2
            }
            batch_interface.configure_batch_job(batch_config)
            
            is_valid = batch_interface.validate_batch_config()
            assert is_valid is True
            self.log_success("批量处理界面", f"配置验证: {'通过' if is_valid else '失败'}")
            
            # 测试结果查看器
            results_viewer = TranslationResultsViewer()
            comparison_data = {
                "original": "冥想练习",
                "translation": "meditation practice",
                "quality_score": 0.9
            }
            results_viewer.display_translation_comparison(comparison_data)
            
            current_data = results_viewer.get_comparison_data()
            assert current_data is not None
            self.log_success("结果查看器", f"对比数据: {current_data['quality_score']:.2f}")
            
            return True
            
        except Exception as e:
            self.log_error("UI组件基础功能", e)
            return False
    
    def test_performance_basic(self) -> bool:
        """测试基础性能"""
        print("\n📋 测试基础性能...")
        
        try:
            from voice_came.translation.terminology import SleepTerminologyManager
            from voice_came.translation.quality import SleepContentQualityAssessor
            
            # 测试术语处理性能
            manager = SleepTerminologyManager("fake_file.yaml")
            
            start_time = time.time()
            for i in range(100):
                text = f"第{i}次冥想和放松练习，帮助获得更好的助眠效果"
                processed = manager.preprocess_for_translation(text, "zh")
                final = manager.postprocess_translation(processed.replace("冥想", "[SLEEP_TERM_123]"), "en")
            
            terminology_time = time.time() - start_time
            self.performance_metrics["terminology_processing"] = terminology_time
            self.log_success("术语处理性能", f"100次处理耗时: {terminology_time:.3f}s")
            
            # 测试质量评估性能
            assessor = SleepContentQualityAssessor()
            
            start_time = time.time()
            for i in range(50):
                source = f"第{i}次冥想练习"
                translation = f"The {i}th meditation practice"
                quality = assessor.assess_translation_quality(source, translation, "en")
            
            quality_time = time.time() - start_time
            self.performance_metrics["quality_assessment"] = quality_time
            self.log_success("质量评估性能", f"50次评估耗时: {quality_time:.3f}s")
            
            return True
            
        except Exception as e:
            self.log_error("基础性能测试", e)
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始Task 4集成测试和质量验证...")
        print("=" * 60)
        
        test_functions = [
            ("基础模块导入", self.test_basic_imports),
            ("UI模块导入", self.test_ui_imports),
            ("数据模型功能", self.test_data_models_functionality),
            ("VoiceTransl适配器", self.test_voicetransl_adapter_basic),
            ("术语管理器", self.test_terminology_manager_basic),
            ("质量评估器", self.test_quality_assessor_basic),
            ("优化模块", self.test_optimization_modules),
            ("UI组件", self.test_ui_components_basic),
            ("基础性能", self.test_performance_basic)
        ]
        
        passed = 0
        total = len(test_functions)
        
        for test_name, test_func in test_functions:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                if result:
                    passed += 1
                    
            except Exception as e:
                self.log_error(f"{test_name}执行", e)
            
            print("-" * 40)
        
        # 生成测试报告
        success_rate = passed / total
        
        print(f"\n📊 测试结果总结:")
        print(f"   通过测试: {passed}/{total} ({success_rate:.1%})")
        print(f"   失败测试: {total - passed}")
        print(f"   错误数量: {len(self.error_log)}")
        
        if self.performance_metrics:
            print(f"\n⚡ 性能指标:")
            for metric, value in self.performance_metrics.items():
                print(f"   {metric}: {value:.3f}s")
        
        if self.error_log:
            print(f"\n❌ 错误详情:")
            for error in self.error_log[-3:]:  # 显示最后3个错误
                print(f"   {error['test']}: {error['error']}")
        
        if success_rate >= 0.8:
            print("\n🎉 Task 4集成测试基本通过！")
        else:
            print("\n⚠️  Task 4集成测试需要修复问题")
        
        return {
            "success_rate": success_rate,
            "passed": passed,
            "total": total,
            "errors": len(self.error_log),
            "performance_metrics": self.performance_metrics
        }


async def main():
    """主函数"""
    validator = Task4IntegrationValidator()
    results = await validator.run_all_tests()
    
    # 返回退出码
    return 0 if results["success_rate"] >= 0.8 else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
