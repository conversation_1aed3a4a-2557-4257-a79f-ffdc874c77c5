# Voice-came 开发规范文档 v1.0

## 📋 文档信息
- **项目名称**: Voice-came (基于VoiceTransl的睡眠内容全球化工具)
- **文档版本**: v1.0
- **制定日期**: 2025年1月
- **适用范围**: Voice-came项目全体开发成员
- **更新周期**: 每季度评估，必要时更新

---

## 🎯 1. 开发规范总则

### 1.1 规范目标
- **提升代码质量**: 确保代码可读性、可维护性和可扩展性
- **降低协作成本**: 统一开发标准，减少沟通成本和理解偏差
- **保障项目稳定**: 通过规范化开发降低bug率和系统风险
- **加速迭代效率**: 标准化流程提升开发和部署效率

### 1.2 执行原则
- **强制执行**: 所有代码必须通过规范检查才能合并
- **持续改进**: 根据项目实践持续优化规范内容
- **工具辅助**: 使用自动化工具确保规范执行
- **团队共识**: 规范变更需要团队讨论和一致同意

---

## 💻 2. 代码规范

### 2.1 Python代码规范 (基于PEP 8)

#### 2.1.1 命名规范
```python
# ✅ 正确示例
class WhisperXEngine:
    """WhisperX语音识别引擎"""
    
    def __init__(self, model_path: str, device: str = "cuda"):
        self.model_path = model_path
        self.device = device
        self._model = None
    
    def process_audio_file(self, audio_path: str) -> dict:
        """处理音频文件并返回识别结果"""
        pass

# 变量和函数命名
audio_file_path = "/path/to/audio.wav"
translation_result = translate_text(source_text, target_language)
MAX_BATCH_SIZE = 10
DEFAULT_MODEL_NAME = "whisperx-large-v2"

# ❌ 错误示例
class whisperxengine:  # 类名应该使用PascalCase
    def ProcessAudioFile(self, audioPath):  # 函数名应该使用snake_case
        maxBatchSize = 10  # 变量名应该使用snake_case
```

#### 2.1.2 代码结构规范
```python
# ✅ 文件头部结构
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice-came WhisperX集成模块

该模块负责集成WhisperX语音识别引擎到VoiceTransl架构中
支持长视频处理、批量处理和多语言识别

Author: Voice-came Team
Created: 2025-01-XX
Version: 1.0.0
"""

import os
import sys
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path

# 第三方库导入
import torch
import whisperx
from faster_whisper import WhisperModel

# 本地模块导入
from voice_came.core.base_engine import BaseEngine
from voice_came.utils.audio_utils import AudioProcessor
from voice_came.config.settings import WHISPERX_CONFIG

# 常量定义
DEFAULT_LANGUAGE = "auto"
SUPPORTED_FORMATS = [".wav", ".mp3", ".mp4", ".m4a", ".flac"]
MAX_AUDIO_LENGTH = 3600 * 12  # 12小时
```

### 2.2 JavaScript/TypeScript规范 (如有前端需求)

#### 2.2.1 命名规范
```javascript
// ✅ 正确示例
class AudioProcessor {
    constructor(config) {
        this.maxFileSize = config.maxFileSize;
        this.supportedFormats = config.supportedFormats;
    }
    
    async processAudioFile(filePath) {
        // 处理逻辑
    }
}

const DEFAULT_CONFIG = {
    maxFileSize: 100 * 1024 * 1024, // 100MB
    supportedFormats: ['.wav', '.mp3', '.mp4']
};

// ❌ 错误示例
class audioprocessor {  // 应该使用PascalCase
    constructor(config) {
        this.MaxFileSize = config.maxFileSize;  // 应该使用camelCase
    }
}
```

---

## 📝 3. 注释规范

### 3.1 文件头注释
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice-came 批量处理核心模块

该模块实现了Voice-came的核心批量处理功能，包括：
- 批量视频导入和队列管理
- WhisperX语音识别集成
- 多语言翻译处理
- 文件组织和导出功能

主要类：
    BatchProcessor: 批量处理主控制器
    VideoQueue: 视频处理队列管理
    ResultExporter: 结果导出管理器

使用示例：
    processor = BatchProcessor(config)
    results = processor.process_video_batch(video_list)

Author: Voice-came Team
Created: 2025-01-XX
Last Modified: 2025-01-XX
Version: 1.0.0
Dependencies: whisperx>=3.1.0, torch>=2.0.0
"""
```

### 3.2 函数注释规范
```python
def process_batch_videos(
    video_paths: List[str],
    output_dir: str,
    whisperx_model: str = "large-v2",
    target_languages: List[str] = None,
    max_concurrent: int = 3
) -> Dict[str, dict]:
    """
    批量处理视频文件的语音识别和翻译
    
    Args:
        video_paths: 视频文件路径列表
        output_dir: 输出目录路径
        whisperx_model: WhisperX模型名称
        target_languages: 目标翻译语言列表
        max_concurrent: 最大并发处理数
    
    Returns:
        Dict[str, dict]: 处理结果字典，键为文件路径，值为处理结果
    
    Raises:
        ValueError: 当输入参数无效时
        FileNotFoundError: 当视频文件不存在时
        ProcessingError: 当处理过程出错时
    
    Example:
        >>> results = process_batch_videos(
        ...     video_paths=["/path/to/video1.mp4", "/path/to/video2.mp4"],
        ...     output_dir="/path/to/output",
        ...     target_languages=["en", "zh", "ja"]
        ... )
    """
    if not video_paths:
        raise ValueError("视频路径列表不能为空")
    
    if target_languages is None:
        target_languages = ["en", "zh"]
    
    # 实现逻辑...
    pass
```

---

## 📁 4. 文件命名规范

### 4.1 Python文件命名
```
# ✅ 正确示例
whisperx_engine.py          # 核心引擎文件
batch_processor.py          # 批量处理器
audio_utils.py              # 音频工具函数
translation_manager.py      # 翻译管理器
config_loader.py            # 配置加载器
test_whisperx_engine.py     # 测试文件

# ❌ 错误示例
WhisperXEngine.py           # 不要使用PascalCase
whisperx-engine.py          # 不要使用连字符
whisperxengine.py           # 单词间要有下划线分隔
```

### 4.2 配置文件命名
```
# ✅ 配置文件
whisperx_config.yaml        # WhisperX配置
translation_config.json     # 翻译配置
model_settings.ini          # 模型设置
batch_processing.conf       # 批量处理配置

# ✅ 脚本文件
setup_environment.sh        # 环境设置脚本
install_models.py           # 模型安装脚本
run_batch_processing.py     # 批量处理启动脚本
```

### 4.3 文档文件命名
```
# ✅ 文档文件
README.md                   # 项目说明
CHANGELOG.md               # 变更日志
API_REFERENCE.md           # API参考文档
DEPLOYMENT_GUIDE.md        # 部署指南
TROUBLESHOOTING.md         # 故障排除指南

# ✅ 规范文档
Voice-came_开发规范_v1.0.md
Voice-came_PRD_v4.1.md
WhisperX集成方案.md
```

---

## 🗂️ 5. 文件存放规范

### 5.1 项目目录结构
```
Voice-came/
├── README.md                          # 项目说明文档
├── requirements.txt                   # Python依赖
├── setup.py                          # 安装配置
├── .gitignore                        # Git忽略文件
├── .env.example                      # 环境变量示例
│
├── docs/                             # 📚 文档目录
│   ├── API_REFERENCE.md              # API参考文档
│   ├── DEPLOYMENT_GUIDE.md           # 部署指南
│   ├── TROUBLESHOOTING.md            # 故障排除
│   └── development/                  # 开发文档
│       ├── Voice-came_开发规范_v1.0.md
│       ├── Voice-came_PRD_v4.1.md
│       └── WhisperX集成方案.md
│
├── voice_came/                       # 🏗️ 主要代码目录
│   ├── __init__.py
│   ├── main.py                       # 主入口文件
│   │
│   ├── core/                         # 核心功能模块
│   │   ├── __init__.py
│   │   ├── base_engine.py            # 基础引擎抽象类
│   │   ├── whisperx_engine.py        # WhisperX引擎实现
│   │   ├── batch_processor.py        # 批量处理器
│   │   └── translation_manager.py    # 翻译管理器
│   │
│   ├── engines/                      # 🔧 引擎实现
│   │   ├── __init__.py
│   │   ├── whisperx/                 # WhisperX相关
│   │   │   ├── __init__.py
│   │   │   ├── whisperx_wrapper.py   # WhisperX包装器
│   │   │   └── models/               # 模型文件目录
│   │   └── translation/              # 翻译引擎
│   │       ├── __init__.py
│   │       ├── gemma_translator.py   # Gemma翻译器
│   │       └── qwen_translator.py    # Qwen翻译器
│   │
│   ├── translation/                  # 🔄 翻译模块（新增）
│   │   ├── __init__.py
│   │   ├── models.py                 # 数据模型定义
│   │   ├── business.py               # 业务逻辑层
│   │   └── integration.py            # 外部系统集成
│   │
│   ├── utils/                        # 🛠️ 工具函数
│   │   ├── __init__.py
│   │   ├── audio_utils.py            # 音频处理工具
│   │   ├── file_utils.py             # 文件操作工具
│   │   ├── text_utils.py             # 文本处理工具
│   │   └── logger.py                 # 日志工具
│   │
│   ├── config/                       # ⚙️ 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py               # 主配置文件
│   │   ├── whisperx_config.yaml      # WhisperX配置
│   │   └── translation_config.json   # 翻译配置
│   │
│   ├── ui/                           # 🖥️ 用户界面
│   │   ├── __init__.py
│   │   ├── main_window.py            # 主窗口
│   │   ├── batch_dialog.py           # 批量处理对话框
│   │   └── progress_widget.py        # 进度显示组件
│   │
│   └── exceptions/                   # ❌ 异常定义
│       ├── __init__.py
│       ├── engine_exceptions.py      # 引擎异常
│       └── processing_exceptions.py  # 处理异常
│
├── tests/                            # 🧪 测试目录
│   ├── __init__.py
│   ├── conftest.py                   # pytest配置
│   ├── test_whisperx_engine.py       # WhisperX引擎测试
│   ├── test_batch_processor.py       # 批量处理测试
│   ├── test_translation_manager.py   # 翻译管理器测试
│   ├── translation/                  # 翻译模块测试
│   │   ├── test_models.py            # 数据模型测试
│   │   ├── test_business.py          # 业务逻辑测试
│   │   └── test_integration.py       # 集成测试
│   └── fixtures/                     # 测试数据
│       ├── sample_audio.wav
│       └── sample_video.mp4
│
├── scripts/                          # 📜 脚本目录
│   ├── setup_environment.sh          # 环境设置
│   ├── install_models.py             # 模型安装
│   ├── run_batch_processing.py       # 批量处理启动
│   └── cleanup_temp_files.py         # 临时文件清理
│
├── data/                             # 📊 数据目录
│   ├── models/                       # 模型文件
│   │   ├── whisperx/
│   │   ├── gemma/
│   │   └── qwen/
│   ├── temp/                         # 临时文件
│   └── output/                       # 输出文件
│       └── .gitkeep
│
└── logs/                             # 📋 日志目录
    ├── app.log                       # 应用日志
    ├── error.log                     # 错误日志
    └── performance.log               # 性能日志
```

### 5.2 文件存放原则

#### 5.2.1 按功能分类
- **core/**: 核心业务逻辑，不依赖具体实现
- **engines/**: 具体引擎实现，可插拔设计
- **utils/**: 通用工具函数，无业务逻辑
- **config/**: 配置文件和配置管理
- **ui/**: 用户界面相关代码

#### 5.2.2 按生命周期分类
- **src/**: 源代码，版本控制
- **data/**: 数据文件，部分版本控制
- **logs/**: 日志文件，不版本控制
- **temp/**: 临时文件，不版本控制

#### 5.2.3 按访问权限分类
- **public/**: 公开接口和文档
- **internal/**: 内部实现，不对外暴露
- **private/**: 私有配置，不提交到版本控制

### 5.3 新文件创建指导

#### 5.3.1 文件创建决策流程
```python
# 新文件存放决策树
def determine_file_location(file_type: str, functionality: str) -> str:
    """确定新文件的存放位置"""
    
    if file_type == "核心业务逻辑":
        return "voice_came/core/"
    elif file_type == "引擎实现":
        if functionality == "WhisperX相关":
            return "voice_came/engines/whisperx/"
        elif functionality == "翻译相关":
            return "voice_came/engines/translation/"
        else:
            return f"voice_came/engines/{functionality}/"
    elif file_type == "工具函数":
        return "voice_came/utils/"
    elif file_type == "配置文件":
        return "voice_came/config/"
    elif file_type == "用户界面":
        return "voice_came/ui/"
    elif file_type == "异常定义":
        return "voice_came/exceptions/"
    elif file_type == "测试文件":
        if functionality == "单元测试":
            return "tests/"
        elif functionality == "集成测试":
            return "tests/integration/"
        elif functionality == "性能测试":
            return "tests/performance/"
    elif file_type == "脚本文件":
        return "scripts/"
    elif file_type == "文档文件":
        if functionality == "开发文档":
            return "docs/development/"
        else:
            return "docs/"
    elif file_type == "数据文件":
        return "data/"
    elif file_type == "日志文件":
        return "logs/"
    else:
        raise ValueError(f"未知文件类型: {file_type}")
```

#### 5.3.2 常见新文件类型示例
```bash
# WhisperX相关新文件
voice_came/engines/whisperx/
├── whisperx_wrapper.py        # WhisperX包装器
├── whisperx_config.py         # WhisperX配置管理
├── whisperx_utils.py          # WhisperX工具函数
└── model_loader.py            # 模型加载器

# 批量处理相关新文件
voice_came/core/
├── video_queue.py             # 视频队列管理
├── progress_tracker.py        # 进度跟踪器
└── result_collector.py        # 结果收集器

# 新增工具函数
voice_came/utils/
├── video_utils.py             # 视频处理工具
├── subtitle_utils.py          # 字幕处理工具
├── format_converter.py        # 格式转换工具
└── performance_monitor.py     # 性能监控工具

# 新增配置文件
voice_came/config/
├── batch_config.yaml          # 批量处理配置
├── performance_config.json    # 性能配置
└── logging_config.yaml        # 日志配置

# 新增测试文件
tests/
├── test_video_queue.py        # 视频队列测试
├── test_progress_tracker.py   # 进度跟踪测试
├── integration/               # 集成测试目录
│   ├── test_end_to_end.py     # 端到端测试
│   └── test_performance.py    # 性能测试
└── fixtures/                  # 测试数据
    ├── sample_audio.wav
    └── sample_video.mp4

# 新增脚本文件
scripts/
├── validate_installation.py   # 安装验证脚本
├── benchmark_performance.py   # 性能基准测试
├── export_results.py         # 结果导出脚本
└── migrate_data.py           # 数据迁移脚本

# 新增文档文件
docs/
├── PERFORMANCE_TUNING.md     # 性能调优指南
├── MODEL_MANAGEMENT.md       # 模型管理指南
├── BATCH_PROCESSING_GUIDE.md # 批量处理指南
└── development/
    ├── API_DESIGN.md          # API设计文档
    └── DATABASE_SCHEMA.md     # 数据库设计
```

#### 5.3.3 文件创建检查清单
在创建新文件时，必须检查以下项目：

**基础检查**
- [ ] **路径正确**: 文件放在了正确的目录中
- [ ] **命名规范**: 文件名符合第4章命名规范
- [ ] **功能单一**: 文件职责清晰，不与现有文件重复
- [ ] **依赖合理**: 导入关系符合架构设计

**配套文件检查**
- [ ] **测试文件**: 核心功能文件必须有对应的测试文件
- [ ] **配置文件**: 需要配置的功能必须有配置文件
- [ ] **文档更新**: 重要文件必须有相应的文档说明
- [ ] **异常定义**: 新功能的异常必须在exceptions/目录中定义

**代码质量检查**
- [ ] **文件头注释**: 按照第3.1节规范添加文件头注释
- [ ] **函数注释**: 按照第3.2节规范添加函数注释
- [ ] **日志记录**: 关键操作必须有日志记录
- [ ] **错误处理**: 必须有完善的错误处理机制

#### 5.3.4 特殊情况处理

**临时文件处理**
```bash
# 临时文件统一放在data/temp/目录下
data/temp/
├── processing_20250115_143022/    # 按时间戳命名的临时处理目录
├── cache_whisperx/               # WhisperX缓存
├── cache_translation/            # 翻译缓存
└── logs_temp/                   # 临时日志
```

**大文件和模型文件**
```bash
# 大文件和模型文件放在data/目录下
data/
├── models/                      # 模型文件
│   ├── whisperx/               # WhisperX模型
│   ├── gemma/                  # Gemma模型
│   └── qwen/                   # Qwen模型
├── samples/                     # 样本数据
│   ├── audio/                  # 音频样本
│   └── video/                  # 视频样本
└── output/                      # 输出结果
    ├── 20250115_batch1/        # 按日期和批次命名
    └── 20250115_batch2/
```

**日志文件管理**
```bash
# 日志文件按日期和类型分类
logs/
├── app_20250115.log            # 按日期分割的应用日志
├── error_20250115.log          # 错误日志
├── performance_20250115.log    # 性能日志
└── whisperx_20250115.log       # WhisperX专用日志
```

#### 5.3.5 文件创建实操示例

**示例1：创建字幕处理工具**
```bash
# 1. 创建主文件
touch voice_came/utils/subtitle_utils.py

# 2. 创建测试文件
touch tests/test_subtitle_utils.py

# 3. 如果需要配置文件
touch voice_came/config/subtitle_config.yaml

# 4. 更新相关文档
# 在docs/API_REFERENCE.md中添加API说明
```

**示例2：创建新的翻译引擎**
```bash
# 1. 创建引擎目录和文件
mkdir -p voice_came/engines/translation/
touch voice_came/engines/translation/custom_translator.py

# 2. 创建配置文件
touch voice_came/config/custom_translation_config.json

# 3. 创建测试文件
touch tests/test_custom_translator.py

# 4. 创建文档
touch docs/CUSTOM_TRANSLATOR_GUIDE.md

# 5. 更新异常定义
# 在voice_came/exceptions/translation_exceptions.py中添加相关异常
```

**示例3：创建性能监控模块**
```bash
# 1. 创建核心模块
touch voice_came/core/performance_monitor.py

# 2. 创建工具函数
touch voice_came/utils/metrics_collector.py

# 3. 创建配置文件
touch voice_came/config/monitoring_config.yaml

# 4. 创建测试文件
touch tests/test_performance_monitor.py
touch tests/performance/test_metrics_collection.py

# 5. 创建文档
touch docs/PERFORMANCE_MONITORING.md
```

---

## 📈 6. 开发工具规范

### 6.1 Git提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

<body>

<footer>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(whisperx): 添加WhisperX引擎集成功能

- 实现WhisperX包装器类
- 添加批量音频处理支持
- 集成到VoiceTransl架构中

Closes #123
```

### 6.2 代码审查规范
```markdown
## 代码审查检查清单

### 功能性检查
- [ ] 功能是否按需求正确实现
- [ ] 边界条件是否正确处理
- [ ] 错误处理是否完善
- [ ] 性能是否满足要求

### 代码质量检查
- [ ] 代码是否符合命名规范
- [ ] 函数是否单一职责
- [ ] 是否有重复代码
- [ ] 注释是否清晰完整

### 安全性检查
- [ ] 是否有安全漏洞
- [ ] 输入验证是否充分
- [ ] 敏感信息是否正确处理
- [ ] 权限控制是否合理
```

### 6.3 代码格式化工具
```bash
# Python代码格式化
pip install black isort flake8 mypy

# 配置文件 .black
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# 配置文件 .isort.cfg
[settings]
profile = black
multi_line_output = 3
line_length = 88
```

---

## 📊 7. 质量保证规范

### 7.1 代码质量指标
- **圈复杂度**: ≤ 10
- **函数长度**: ≤ 50行
- **类长度**: ≤ 500行
- **嵌套深度**: ≤ 4层
- **测试覆盖率**: ≥ 80%

### 7.2 性能要求
- **处理速度**: 3小时视频处理时间 ≤ 30分钟
- **内存使用**: GPU内存使用 < 8GB
- **并发处理**: 支持3-5个视频同时处理
- **错误恢复**: 处理失败后能够快速重试

### 7.3 测试覆盖率要求
```python
# pytest配置 - pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --cov=voice_came
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80

# 测试示例
import pytest
from unittest.mock import Mock, patch
from voice_came.core.whisperx_engine import WhisperXEngine

class TestWhisperXEngine:
    """WhisperX引擎测试类"""
    
    @pytest.fixture
    def engine(self):
        """测试引擎实例"""
        return WhisperXEngine(model_name="base", device="cpu")
    
    @pytest.fixture
    def sample_audio_path(self):
        """示例音频文件路径"""
        return "tests/fixtures/sample_audio.wav"
    
    def test_engine_initialization(self, engine):
        """测试引擎初始化"""
        assert engine.model_name == "base"
        assert engine.device == "cpu"
        assert engine.model is not None
    
    def test_transcribe_success(self, engine, sample_audio_path):
        """测试成功转录"""
        result = engine.transcribe(sample_audio_path)
        
        assert "text" in result
        assert "segments" in result
        assert "language" in result
        assert isinstance(result["text"], str)
        assert len(result["text"]) > 0
    
    def test_transcribe_file_not_found(self, engine):
        """测试文件不存在的情况"""
        with pytest.raises(FileNotFoundError):
            engine.transcribe("nonexistent_file.wav")
    
    @patch('voice_came.core.whisperx_engine.whisperx.load_model')
    def test_model_loading_failure(self, mock_load_model):
        """测试模型加载失败"""
        mock_load_model.side_effect = Exception("模型加载失败")
        
        with pytest.raises(Exception):
            WhisperXEngine(model_name="invalid_model")
```

---

## 🚀 8. 部署和发布规范

### 8.1 环境配置规范
```bash
# .env.example - 环境变量模板
# 复制为 .env 并填入实际值

# 基础配置
VOICE_CAME_ENV=development
VOICE_CAME_DEBUG=true
VOICE_CAME_LOG_LEVEL=INFO

# WhisperX配置
WHISPERX_MODEL_PATH=/path/to/whisperx/models
WHISPERX_DEVICE=cuda
WHISPERX_COMPUTE_TYPE=float16

# 翻译配置
GEMMA_MODEL_PATH=/path/to/gemma/models
QWEN_MODEL_PATH=/path/to/qwen/models
TRANSLATION_CACHE_SIZE=1000

# 文件路径配置
INPUT_DIR=/path/to/input
OUTPUT_DIR=/path/to/output
TEMP_DIR=/path/to/temp
LOG_DIR=/path/to/logs

# 性能配置
MAX_CONCURRENT_JOBS=3
MAX_AUDIO_LENGTH=43200
BATCH_SIZE=16
```

### 8.2 版本发布规范
```bash
# 版本号规范 (语义化版本)
# MAJOR.MINOR.PATCH
# 1.0.0 - 初始版本
# 1.0.1 - 补丁版本 (bug修复)
# 1.1.0 - 次要版本 (新功能)
# 2.0.0 - 主要版本 (破坏性变更)

# 发布检查清单
echo "发布前检查清单:"
echo "□ 所有测试通过"
echo "□ 代码审查完成"
echo "□ 文档更新完成"
echo "□ 版本号更新"
echo "□ CHANGELOG更新"
echo "□ 依赖版本锁定"
echo "□ 安全扫描通过"
echo "□ 性能测试通过"
```

---

## 📋 9. 监控和维护规范

### 9.1 日志规范
```python
import logging
from voice_came.utils.logger import get_logger

# 日志级别使用
logger = get_logger(__name__)

# INFO: 关键操作记录
logger.info(f"开始处理视频: {video_path}")

# DEBUG: 调试信息
logger.debug(f"使用模型: {model_name}, 设备: {device}")

# WARNING: 警告信息
logger.warning(f"视频文件较大: {file_size}MB，处理时间可能较长")

# ERROR: 错误信息
logger.error(f"处理失败: {video_path}, 错误: {str(e)}", exc_info=True)
```

### 9.2 错误处理规范
```python
# ✅ 正确的错误处理
def process_audio_file(audio_path: str) -> dict:
    """处理音频文件"""
    try:
        # 验证输入
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        # 执行处理
        result = self._do_process(audio_path)
        return result
        
    except FileNotFoundError:
        logger.error(f"文件不存在: {audio_path}")
        raise
    except Exception as e:
        logger.error(f"处理音频文件失败: {audio_path}, 错误: {str(e)}")
        raise ProcessingError(f"音频处理失败: {str(e)}") from e
```

### 9.3 性能监控规范
```python
import time
import psutil
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 记录开始状态
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        try:
            result = func(*args, **kwargs)
            
            # 记录成功指标
            duration = time.time() - start_time
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_delta = end_memory - start_memory
            
            logger.info(f"性能指标 - 函数: {func.__name__}, "
                       f"耗时: {duration:.2f}s, "
                       f"内存变化: {memory_delta:.2f}MB")
            
            return result
            
        except Exception as e:
            # 记录失败指标
            duration = time.time() - start_time
            logger.error(f"性能指标 - 函数: {func.__name__}, "
                        f"失败耗时: {duration:.2f}s, "
                        f"错误: {str(e)}")
            raise
    
    return wrapper

# 使用示例
@monitor_performance
def process_large_video(video_path: str) -> dict:
    """处理大视频文件"""
    # 处理逻辑...
    pass
```

---

## 🔒 10. 安全规范

### 10.1 敏感信息处理
```python
import os

# ✅ 正确示例 - 从环境变量获取
api_key = os.environ.get('TRANSLATION_API_KEY')
if not api_key:
    raise ValueError("未设置翻译API密钥")

# ❌ 错误示例 - 不要硬编码
api_key = "sk-1234567890abcdef"  # 不要硬编码API密钥
```

### 10.2 输入验证规范
```python
def validate_audio_file_path(file_path: str) -> bool:
    """验证音频文件路径"""
    # 检查路径格式
    if not isinstance(file_path, str) or not file_path.strip():
        return False
    
    # 检查文件存在性
    path = Path(file_path)
    if not path.exists() or not path.is_file():
        return False
    
    # 检查文件扩展名
    allowed_extensions = {'.wav', '.mp3', '.mp4', '.m4a', '.flac'}
    if path.suffix.lower() not in allowed_extensions:
        return False
    
    # 检查文件大小 (最大1GB)
    if path.stat().st_size > 1024 * 1024 * 1024:
        return False
    
    return True
```

---

## 📈 11. 规范执行和监督

### 11.1 开发流程检查
1. **代码编写**: 遵循命名规范和代码结构规范
2. **自测验证**: 本地运行测试确保功能正常
3. **代码审查**: 提交PR进行代码审查
4. **集成测试**: 通过CI/CD管道的自动化测试
5. **部署发布**: 按照发布规范进行版本发布

### 11.2 质量门禁
- **代码格式**: 必须通过代码格式检查
- **测试覆盖**: 测试覆盖率必须≥80%
- **性能测试**: 关键功能必须通过性能测试
- **安全扫描**: 必须通过安全漏洞扫描

---

## 📞 12. 联系和支持

### 12.1 规范问题反馈
- **技术问题**: 在项目Issue中提出
- **规范建议**: 发起Pull Request讨论
- **紧急问题**: 联系项目负责人

### 12.2 规范文档维护
- **文档版本**: 与项目版本同步更新
- **更新频率**: 每季度评估，必要时更新
- **维护责任**: 项目经理和技术负责人

---

## 📝 附录

### A. 常用命令速查
```bash
# 项目初始化
git clone <repository>
cd Voice-came
pip install -r requirements.txt

# 代码检查
python -m py_compile voice_came/*.py
python -m pytest tests/ -v

# 运行项目
python voice_came/main.py

# 清理临时文件
python scripts/cleanup_temp_files.py
```

### B. 开发环境配置
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 设置环境变量
cp .env.example .env
# 编辑 .env 文件填入实际配置
```

---

**文档版本**: v1.0  
**最后更新**: 2025年1月  
**下次评估**: 2025年4月 