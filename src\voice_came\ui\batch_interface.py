#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4.4: 批量处理界面组件 (TDD-Green最小实现)

提供批量翻译任务的管理界面，包括任务配置、
队列管理、并行处理控制等功能。
"""

import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging

from ..translation.models import BatchJob, BatchProgress

logger = logging.getLogger(__name__)


class BatchJobStatus(Enum):
    """批量任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BatchJobConfig:
    """批量任务配置"""
    job_name: str
    source_language: str
    target_languages: List[str]
    quality_level: str
    parallel_workers: int
    output_format: str
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class BatchJobInfo:
    """批量任务信息"""
    job_id: str
    config: BatchJobConfig
    status: BatchJobStatus
    progress: float = 0.0
    files_total: int = 0
    files_completed: int = 0
    files_failed: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class BatchProcessingInterface:
    """批量处理界面 - TDD Green最小实现"""
    
    def __init__(self):
        self.batch_jobs: Dict[str, BatchJobInfo] = {}
        self.job_queue: List[str] = []
        self.current_config: Optional[BatchJobConfig] = None
        self.parallel_workers = 3
        self.job_counter = 0
        self.processing_tasks: Dict[str, asyncio.Task] = {}
    
    def configure_batch_job(self, config: Dict[str, Any]) -> None:
        """配置批量任务"""
        # TDD Green: 最小实现，存储配置
        self.current_config = BatchJobConfig(
            job_name=config.get("job_name", "批量翻译任务"),
            source_language=config.get("source_language", "zh"),
            target_languages=config.get("target_languages", ["en"]),
            quality_level=config.get("quality_level", "high"),
            parallel_workers=config.get("parallel_workers", 3),
            output_format=config.get("output_format", "srt")
        )
        
        self.parallel_workers = self.current_config.parallel_workers
        logger.info(f"Batch job configured: {self.current_config.job_name}")
    
    def validate_batch_config(self) -> bool:
        """验证批量配置"""
        # TDD Green: 最小实现，基本验证
        if not self.current_config:
            return False
        
        # 检查必要字段
        if not self.current_config.job_name:
            return False
        
        if not self.current_config.source_language:
            return False
        
        if not self.current_config.target_languages:
            return False
        
        if self.current_config.parallel_workers <= 0:
            return False
        
        return True
    
    def add_to_batch_queue(self, job_name: str, files: Optional[List[str]] = None) -> str:
        """添加任务到批量队列"""
        # TDD Green: 最小实现，创建任务并加入队列
        if not self.current_config:
            raise ValueError("Batch configuration not set")
        
        self.job_counter += 1
        job_id = f"batch_job_{self.job_counter}"
        
        # 创建任务信息
        job_info = BatchJobInfo(
            job_id=job_id,
            config=self.current_config,
            status=BatchJobStatus.PENDING,
            files_total=len(files) if files else 0
        )
        
        self.batch_jobs[job_id] = job_info
        self.job_queue.append(job_id)
        
        logger.info(f"Added job {job_id} to batch queue")
        return job_id
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        # TDD Green: 最小实现，返回队列信息
        pending_jobs = [
            job_id for job_id in self.job_queue 
            if self.batch_jobs[job_id].status == BatchJobStatus.PENDING
        ]
        
        running_jobs = [
            job_id for job_id, job_info in self.batch_jobs.items()
            if job_info.status == BatchJobStatus.RUNNING
        ]
        
        completed_jobs = [
            job_id for job_id, job_info in self.batch_jobs.items()
            if job_info.status == BatchJobStatus.COMPLETED
        ]
        
        return {
            "pending_jobs": len(pending_jobs),
            "running_jobs": len(running_jobs),
            "completed_jobs": len(completed_jobs),
            "total_jobs": len(self.batch_jobs),
            "queue_length": len(self.job_queue),
            "parallel_workers": self.parallel_workers
        }
    
    def remove_from_queue(self, job_id: str) -> bool:
        """从队列中移除任务"""
        # TDD Green: 最小实现，移除任务
        if job_id not in self.batch_jobs:
            return False
        
        job_info = self.batch_jobs[job_id]
        
        # 只能移除未开始的任务
        if job_info.status != BatchJobStatus.PENDING:
            logger.warning(f"Cannot remove job {job_id} with status {job_info.status}")
            return False
        
        # 从队列和任务字典中移除
        if job_id in self.job_queue:
            self.job_queue.remove(job_id)
        
        del self.batch_jobs[job_id]
        
        logger.info(f"Removed job {job_id} from queue")
        return True
    
    def set_parallel_workers(self, worker_count: int) -> None:
        """设置并行工作线程数"""
        # TDD Green: 最小实现，设置工作线程数
        if worker_count <= 0:
            raise ValueError("Worker count must be positive")
        
        self.parallel_workers = worker_count
        logger.info(f"Parallel workers set to {worker_count}")
    
    def start_batch_processing(self) -> Optional[str]:
        """启动批量处理"""
        # TDD Green: 最小实现，启动批量处理
        if not self.job_queue:
            logger.warning("No jobs in queue to process")
            return None
        
        # 创建批量处理任务
        batch_id = f"batch_processing_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 启动处理任务
        processing_task = asyncio.create_task(
            self._process_batch_queue(batch_id)
        )
        self.processing_tasks[batch_id] = processing_task
        
        logger.info(f"Started batch processing: {batch_id}")
        return batch_id
    
    def pause_batch_processing(self, batch_id: str) -> bool:
        """暂停批量处理"""
        # TDD Green: 最小实现，暂停处理
        if batch_id not in self.processing_tasks:
            return False
        
        # 将运行中的任务标记为暂停
        for job_info in self.batch_jobs.values():
            if job_info.status == BatchJobStatus.RUNNING:
                job_info.status = BatchJobStatus.PAUSED
        
        logger.info(f"Paused batch processing: {batch_id}")
        return True
    
    def resume_batch_processing(self, batch_id: str) -> bool:
        """恢复批量处理"""
        # TDD Green: 最小实现，恢复处理
        if batch_id not in self.processing_tasks:
            return False
        
        # 将暂停的任务标记为运行中
        for job_info in self.batch_jobs.values():
            if job_info.status == BatchJobStatus.PAUSED:
                job_info.status = BatchJobStatus.RUNNING
        
        logger.info(f"Resumed batch processing: {batch_id}")
        return True
    
    def get_batch_results(self, batch_id: str) -> Dict[str, Any]:
        """获取批量处理结果"""
        # TDD Green: 最小实现，返回结果摘要
        completed_jobs = [
            job_info for job_info in self.batch_jobs.values()
            if job_info.status == BatchJobStatus.COMPLETED
        ]
        
        failed_jobs = [
            job_info for job_info in self.batch_jobs.values()
            if job_info.status == BatchJobStatus.FAILED
        ]
        
        return {
            "batch_id": batch_id,
            "completed_jobs": len(completed_jobs),
            "failed_jobs": len(failed_jobs),
            "total_jobs": len(self.batch_jobs),
            "success_rate": len(completed_jobs) / max(len(self.batch_jobs), 1),
            "completed_job_details": [
                {
                    "job_id": job.job_id,
                    "job_name": job.config.job_name,
                    "files_completed": job.files_completed,
                    "files_total": job.files_total
                }
                for job in completed_jobs
            ],
            "failed_job_details": [
                {
                    "job_id": job.job_id,
                    "job_name": job.config.job_name,
                    "files_failed": job.files_failed
                }
                for job in failed_jobs
            ]
        }
    
    def export_batch_results(self, batch_id: str, output_path: str) -> Optional[str]:
        """导出批量结果"""
        # TDD Green: 最小实现，模拟导出
        results = self.get_batch_results(batch_id)
        
        # 在实际实现中会生成真实的导出文件
        export_file = f"{output_path}/batch_results_{batch_id}.json"
        
        logger.info(f"Exported batch results to {export_file}")
        return export_file
    
    def generate_batch_report(self, batch_id: str) -> Dict[str, Any]:
        """生成批量处理报告"""
        # TDD Green: 最小实现，生成基本报告
        results = self.get_batch_results(batch_id)
        
        total_files = sum(job.files_total for job in self.batch_jobs.values())
        completed_files = sum(job.files_completed for job in self.batch_jobs.values())
        failed_files = sum(job.files_failed for job in self.batch_jobs.values())
        
        return {
            "summary": {
                "batch_id": batch_id,
                "total_jobs": results["total_jobs"],
                "completed_jobs": results["completed_jobs"],
                "failed_jobs": results["failed_jobs"],
                "success_rate": results["success_rate"]
            },
            "statistics": {
                "total_files": total_files,
                "completed_files": completed_files,
                "failed_files": failed_files,
                "file_success_rate": completed_files / max(total_files, 1)
            },
            "configuration": {
                "parallel_workers": self.parallel_workers,
                "quality_level": self.current_config.quality_level if self.current_config else "unknown"
            },
            "generated_at": datetime.now().isoformat()
        }
    
    async def _process_batch_queue(self, batch_id: str) -> None:
        """处理批量队列"""
        # TDD Green: 最小实现，模拟批量处理
        try:
            while self.job_queue:
                # 获取下一个任务
                job_id = self.job_queue.pop(0)
                job_info = self.batch_jobs[job_id]
                
                # 更新任务状态
                job_info.status = BatchJobStatus.RUNNING
                job_info.started_at = datetime.now()
                
                # 模拟处理时间
                await asyncio.sleep(1.0)
                
                # 模拟处理结果
                job_info.files_completed = job_info.files_total
                job_info.progress = 1.0
                job_info.status = BatchJobStatus.COMPLETED
                job_info.completed_at = datetime.now()
                
                logger.info(f"Completed job {job_id}")
        
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
        finally:
            # 清理处理任务
            if batch_id in self.processing_tasks:
                del self.processing_tasks[batch_id]
