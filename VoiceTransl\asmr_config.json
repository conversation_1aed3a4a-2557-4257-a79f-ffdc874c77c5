{"engines": {"whisper_cpp": {"enabled": true, "models": ["ggml-small.bin", "ggml-medium.bin", "ggml-large-v2.bin"], "param_file": "whisper/param.txt"}, "faster_whisper": {"enabled": true, "models": ["faster-whisper-small", "faster-whisper-medium", "faster-whisper-large-v3"], "param_file": "whisper-faster/param.txt"}, "whisperx": {"enabled": false, "models": ["small", "medium", "large-v2"], "param_file": "whisperx/param.txt"}}, "asmr_detection": {"min_duration": 10.0, "max_rms_threshold": 0.05, "min_silence_ratio": 0.1, "whisper_confidence_threshold": 0.7, "spectral_centroid_threshold": 2000}, "validation_criteria": {"max_processing_time": 300, "min_transcription_accuracy": 0.8, "max_word_error_rate": 0.2}, "output": {"report_dir": "asmr_validation_reports", "detailed_logs": true, "export_formats": ["json", "csv", "html"]}}