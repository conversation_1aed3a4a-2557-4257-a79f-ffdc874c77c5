#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice-came 核心模块

提供文件验证、批量处理队列、任务管理等核心功能
"""

__version__ = "0.1.0"
__author__ = "Voice-came Team"

# 导出主要组件
from .file_validator import (
    VideoFileValidator, 
    AudioFileValidator,
    ValidationResult, 
    FileValidationError,
    SupportedFormats,
    BasicFileValidator
)
from .batch_queue import BatchQueue, ProcessingJob, JobStatus
from .file_processor import FileProcessor

__all__ = [
    "VideoFileValidator",
    "AudioFileValidator", 
    "ValidationResult", 
    "FileValidationError",
    "SupportedFormats",
    "BasicFileValidator",
    "BatchQueue",
    "ProcessingJob",
    "JobStatus",
    "FileProcessor",
] 