<execution>
  <constraint>
    ## Voice-came开发的客观限制
    - **VoiceTransl架构约束**：必须基于现有VoiceTransl架构扩展，不可重新架构
    - **WhisperX版本依赖**：需要兼容WhisperX 3.1.1+版本的API变化
    - **GPU内存限制**：最低8GB显存要求，需要优化长视频处理的内存使用
    - **模型文件约束**：Gemma3-12B-Q4和Qwen3模型文件5-10GB，存储和加载压力
    - **跨平台兼容性**：必须支持Windows、macOS、Linux三个平台
    - **Python版本约束**：基于Python 3.8+，需要考虑不同版本的兼容性
    - **开源协议限制**：遵循GPL-3.0协议，商业化使用受限
    - **ASMR场景特殊性**：低音量(-25dB到-30dB)音频处理的技术挑战
  </constraint>

  <rule>
    ## Voice-came开发强制规则
    - **代码规范严格执行**：100%遵循PEP 8规范，使用black、flake8、mypy检查
    - **测试驱动开发**：单元测试覆盖率≥90%，集成测试覆盖核心流程
    - **性能指标达标**：3小时视频处理≤30分钟，GPU利用率≥85%
    - **错误处理完整**：所有异常情况必须有明确的处理和用户提示
    - **文档同步更新**：代码变更必须同步更新API文档和用户指南
    - **版本控制规范**：使用Git Flow工作流，commit信息遵循约定式提交
    - **依赖管理严格**：使用requirements.txt锁定版本，避免依赖冲突
    - **安全性保证**：本地处理模式，严禁收集或上传用户音频数据
    - **向下兼容原则**：新版本必须兼容旧版本的配置文件和数据格式
  </rule>

  <guideline>
    ## Voice-came开发指导原则
    - **模块化设计优先**：核心功能模块化，支持插件式扩展
    - **性能优化渐进式**：先实现功能，再优化性能，避免过早优化
    - **用户体验中心**：界面设计简洁直观，操作流程符合用户习惯
    - **错误恢复友好**：支持断点续传，处理中断后可恢复
    - **资源使用合理**：智能管理GPU/CPU/内存资源，避免系统卡顿
    - **日志记录详细**：关键操作和错误信息详细记录，便于调试
    - **配置管理灵活**：支持多种配置方式，满足不同用户需求
    - **社区反馈重视**：积极响应用户反馈，快速修复问题
  </guideline>

  <process>
    ## Voice-came开发执行流程

    ### Phase 1: 需求分析与技术设计
    ```
    1.1 需求分析
    - 分析PRD文档，明确功能需求和性能指标
    - 识别技术难点和风险点
    - 评估现有VoiceTransl架构的扩展可行性
    - 制定技术方案和架构设计
    
    1.2 技术方案设计
    - WhisperX集成方案设计
    - 翻译引擎集成架构
    - 批量处理系统设计
    - 用户界面架构规划
    - 数据流和接口设计
    
    1.3 开发计划制定
    - 功能模块分解和优先级排序
    - 开发时间估算和里程碑设定
    - 风险识别和应对策略
    - 测试策略和质量保证计划
    ```

    ### Phase 2: 环境搭建与基础开发
    ```
    2.1 开发环境配置
    - Python 3.8+环境搭建
    - 依赖包安装和版本管理
    - GPU环境配置和测试
    - 开发工具配置（IDE、调试器、代码检查工具）
    
    2.2 项目结构初始化
    - 基于开发规范创建项目目录结构
    - 配置文件模板创建
    - 基础工具类和异常类定义
    - 日志系统和配置管理初始化
    
    2.3 核心模块框架搭建
    - BaseEngine抽象类定义
    - WhisperXEngine框架实现
    - TranslationManager框架实现
    - BatchProcessor框架实现
    ```

    ### Phase 3: 核心功能开发
    ```
    3.1 WhisperX引擎集成开发
    TDD开发流程：
    - 编写WhisperX集成测试用例
    - 实现WhisperXWrapper基础功能
    - 添加音频预处理和后处理
    - 优化GPU内存使用和性能
    - 添加错误处理和恢复机制
    
    3.2 翻译引擎开发
    TDD开发流程：
    - 编写翻译引擎测试用例
    - 实现Gemma3Translator基础功能
    - 实现Qwen3Translator基础功能
    - 添加术语管理和质量控制
    - 优化翻译性能和准确性
    
    3.3 批量处理系统开发
    TDD开发流程：
    - 编写批量处理测试用例
    - 实现任务队列管理
    - 实现并发控制和资源管理
    - 添加进度跟踪和状态管理
    - 实现错误处理和断点续传
    ```

    ### Phase 4: 用户界面开发
    ```
    4.1 主界面开发
    - 主窗口布局和组件设计
    - 文件拖拽上传功能
    - 配置参数设置界面
    - 实时日志显示组件
    
    4.2 批量处理界面开发
    - 批量任务管理界面
    - 实时进度显示组件
    - 处理结果预览功能
    - 多格式导出功能
    
    4.3 用户体验优化
    - 界面响应性能优化
    - 操作流程简化
    - 错误提示和帮助信息
    - 快捷键和便捷操作
    ```

    ### Phase 5: 集成测试与优化
    ```
    5.1 模块集成测试
    - 语音识别和翻译引擎集成测试
    - 批量处理系统集成测试
    - 用户界面和后端集成测试
    - 跨平台兼容性测试
    
    5.2 性能优化
    - GPU利用率优化（目标≥85%）
    - 内存使用优化（峰值≤16GB）
    - 处理速度优化（3小时视频≤30分钟）
    - 并发处理优化
    
    5.3 质量保证
    - 代码覆盖率检查（≥90%）
    - 代码质量检查（复杂度≤10）
    - 安全性检查和隐私保护验证
    - 用户体验测试和优化
    ```

    ### Phase 6: 部署与维护
    ```
    6.1 打包和分发
    - 跨平台打包配置
    - 依赖库和模型文件打包
    - 安装程序制作
    - 版本发布和更新机制
    
    6.2 文档和支持
    - 用户手册和快速入门指南
    - API文档和开发者指南
    - 常见问题和故障排除
    - 社区支持和反馈渠道
    
    6.3 持续维护
    - 用户反馈收集和处理
    - Bug修复和性能优化
    - 新功能开发和版本迭代
    - 技术债务管理和重构
    ```

    ## 开发工作流程规范

    ### Git工作流程
    ```
    主分支策略：
    - main: 生产环境稳定版本
    - develop: 开发环境集成分支
    - feature/*: 功能开发分支
    - hotfix/*: 紧急修复分支
    - release/*: 版本发布分支
    
    提交规范：
    - feat: 新功能开发
    - fix: Bug修复
    - docs: 文档更新
    - style: 代码格式调整
    - refactor: 代码重构
    - test: 测试相关
    - chore: 构建和工具相关
    ```

    ### 代码审查流程
    ```
    1. 开发者提交Pull Request
    2. 自动化检查（代码规范、测试、覆盖率）
    3. 同行代码审查（至少1人审查）
    4. 技术负责人最终审查
    5. 合并到目标分支
    6. 自动化部署和测试
    ```

    ### 发布流程
    ```
    1. 功能开发完成并测试通过
    2. 创建release分支
    3. 版本号更新和CHANGELOG生成
    4. 最终测试和质量检查
    5. 合并到main分支并打标签
    6. 自动化构建和打包
    7. 发布到分发渠道
    8. 发布公告和文档更新
    ```
  </process>

  <criteria>
    ## Voice-came开发质量标准

    ### 代码质量指标
    - ✅ 单元测试覆盖率 ≥ 90%
    - ✅ 集成测试覆盖核心业务流程
    - ✅ 代码复杂度 ≤ 10（圈复杂度）
    - ✅ 函数长度 ≤ 50行
    - ✅ 类长度 ≤ 500行
    - ✅ PEP 8规范100%遵循
    - ✅ 类型注解覆盖率 ≥ 95%
    - ✅ 文档字符串覆盖率 ≥ 90%

    ### 性能指标要求
    - ✅ 3小时视频处理时间 ≤ 30分钟
    - ✅ GPU利用率 ≥ 85%
    - ✅ 内存使用峰值 ≤ 16GB
    - ✅ 识别准确率 ≥ 95%（标准语音）
    - ✅ 识别准确率 ≥ 90%（ASMR低音量）
    - ✅ 翻译质量BLEU分数 ≥ 0.6
    - ✅ 界面响应时间 ≤ 100ms
    - ✅ 批量处理并发数 ≥ 3个文件

    ### 用户体验标准
    - ✅ 新用户上手时间 ≤ 30分钟
    - ✅ 核心功能使用率 ≥ 80%
    - ✅ 用户任务完成率 ≥ 95%
    - ✅ 用户满意度 ≥ 4.5/5.0
    - ✅ 错误恢复成功率 ≥ 90%
    - ✅ 帮助文档完整性 ≥ 95%

    ### 系统稳定性标准
    - ✅ 系统崩溃率 ≤ 0.1%
    - ✅ 内存泄漏检测通过
    - ✅ 长时间运行稳定性测试通过
    - ✅ 跨平台兼容性测试通过
    - ✅ 并发处理稳定性测试通过
    - ✅ 异常情况恢复测试通过

    ### 安全性和隐私标准
    - ✅ 本地处理模式，无数据上传
    - ✅ 临时文件自动清理
    - ✅ 用户数据加密存储
    - ✅ 权限控制和访问限制
    - ✅ 安全漏洞扫描通过
    - ✅ 隐私保护合规性检查通过
  </criteria>
</execution> 