[pytest]
minversion = 7.0
addopts = 
    -v
    --strict-markers
    --strict-config
    --cov=src/voice_came
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=90
    --cov-branch
    --tb=short
    --durations=10
testpaths = 
    tests
    src
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    fast: marks tests as fast (select with '-m fast')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    smoke: marks tests as smoke tests
    api: marks tests that test API endpoints
    offline: marks tests that don't require network
    performance: marks tests as performance tests
    stress: marks tests as stress tests
    benchmark: marks tests as benchmark tests
    tdd_red: marks tests in TDD Red phase (failing tests)
    tdd_green: marks tests in TDD Green phase (passing tests)
    tdd_refactor: marks tests in TDD Refactor phase (optimization tests)
    tdd_red_validation: marks tests for TDD Red phase validation
    tdd_cycle: marks tests that validate complete TDD cycles
    coverage_target: marks tests that must meet coverage targets
    boundary: marks tests as boundary condition tests
    error_recovery: marks tests for error recovery scenarios
    configuration: marks tests for configuration edge cases
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::pytest.PytestDeprecationWarning
    ignore::pytest.PytestUnknownMarkWarning 