#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理队列管理

TDD-Green阶段：实现最小可用的队列管理功能使测试通过
支持任务添加、移除、状态管理和并发控制
"""

import asyncio
import time
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass, asdict
import threading
import logging
import uuid

from voice_came.utils.logger import get_logger
from voice_came.exceptions import QueueError, ProcessingError

logger = get_logger(__name__)


class JobStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProcessingJob:
    """处理任务数据类"""
    id: str
    file_path: Path
    job_type: str
    priority: int = 1
    metadata: Dict[str, Any] = None
    status: JobStatus = JobStatus.PENDING
    created_at: float = None
    started_at: float = None
    completed_at: float = None
    progress: float = 0.0
    error_message: str = ""
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.created_at is None:
            self.created_at = time.time()
        if isinstance(self.file_path, str):
            self.file_path = Path(self.file_path)


class BatchQueue:
    """批量处理队列管理器
    
    管理文件处理任务的队列，支持并发控制、状态跟踪和持久化
    """
    
    def __init__(self, max_concurrent_jobs: int = 3, auto_save: bool = False, state_file: Path = None):
        """初始化批量处理队列
        
        Args:
            max_concurrent_jobs: 最大并发任务数
            auto_save: 是否自动保存状态
            state_file: 状态文件路径
        """
        self._jobs: Dict[str, ProcessingJob] = {}
        self._max_concurrent_jobs = max_concurrent_jobs
        self._running_jobs: Dict[str, ProcessingJob] = {}
        self._processor = None
        self._is_running = False
        self._is_paused = False
        self._auto_save = auto_save
        self._state_file = state_file
        self._progress_callback: Optional[Callable] = None
        self._event_loop = None
        self._worker_tasks: List[asyncio.Task] = []
        
        # 如果有状态文件且自动保存，尝试加载
        if self._auto_save and self._state_file and self._state_file.exists():
            try:
                self.load_state(self._state_file)
            except Exception as e:
                logger.warning(f"加载状态文件失败: {e}")
    
    def add_job(self, job: ProcessingJob) -> str:
        """添加任务到队列
        
        Args:
            job: 处理任务
            
        Returns:
            str: 任务ID
            
        Raises:
            QueueError: 如果任务ID已存在
        """
        if job.id in self._jobs:
            raise QueueError(f"任务ID已存在: {job.id}")
        
        self._jobs[job.id] = job
        logger.info(f"任务已添加到队列: {job.id}")
        
        # 自动保存
        if self._auto_save:
            self._save_state()
        
        return job.id
    
    def remove_job(self, job_id: str) -> ProcessingJob:
        """从队列移除任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            ProcessingJob: 被移除的任务
            
        Raises:
            QueueError: 如果任务不存在
        """
        if job_id not in self._jobs:
            raise QueueError(f"任务不存在: {job_id}")
        
        job = self._jobs[job_id]
        
        # 如果任务正在运行，取消它
        if job_id in self._running_jobs:
            job.status = JobStatus.CANCELLED
            self._running_jobs.pop(job_id, None)
        
        # 从队列中移除
        del self._jobs[job_id]
        
        logger.info(f"任务已从队列移除: {job_id}")
        
        # 自动保存
        if self._auto_save:
            self._save_state()
        
        return job
    
    def get_job(self, job_id: str) -> Optional[ProcessingJob]:
        """获取指定任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            Optional[ProcessingJob]: 任务对象，如果不存在则返回None
        """
        return self._jobs.get(job_id)
    
    def get_all_jobs(self) -> List[ProcessingJob]:
        """获取所有任务"""
        return list(self._jobs.values())
    
    def get_scheduled_jobs(self) -> List[ProcessingJob]:
        """获取按优先级和创建时间排序的任务列表"""
        pending_jobs = [job for job in self._jobs.values() if job.status == JobStatus.PENDING]
        # 按优先级（升序）和创建时间（升序）排序
        return sorted(pending_jobs, key=lambda x: (x.priority, x.created_at))
    
    def get_queue_size(self) -> int:
        """获取队列总大小"""
        return len(self._jobs)
    
    def get_pending_jobs_count(self) -> int:
        """获取待处理任务数量"""
        return len([job for job in self._jobs.values() if job.status == JobStatus.PENDING])
    
    def get_running_jobs_count(self) -> int:
        """获取正在运行的任务数量"""
        return len(self._running_jobs)
    
    def get_completed_jobs_count(self) -> int:
        """获取已完成任务数量"""
        return len([job for job in self._jobs.values() if job.status == JobStatus.COMPLETED])
    
    def get_failed_jobs_count(self) -> int:
        """获取失败任务数量"""
        return len([job for job in self._jobs.values() if job.status == JobStatus.FAILED])
    
    def get_max_concurrent_jobs(self) -> int:
        """获取最大并发任务数"""
        return self._max_concurrent_jobs
    
    def set_max_concurrent_jobs(self, max_jobs: int):
        """设置最大并发任务数"""
        self._max_concurrent_jobs = max_jobs
        logger.info(f"最大并发任务数已设置为: {max_jobs}")
    
    def set_processor(self, processor):
        """设置文件处理器"""
        self._processor = processor
        logger.info("文件处理器已设置")
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self._progress_callback = callback
    
    def start_processing(self):
        """开始处理任务"""
        if self._is_running:
            logger.warning("队列已在运行中")
            return
        
        self._is_running = True
        self._is_paused = False
        
        # 在新线程中运行事件循环
        self._processing_thread = threading.Thread(target=self._run_processing_loop)
        self._processing_thread.start()
        
        logger.info("开始处理任务队列")
    
    def pause_processing(self):
        """暂停处理"""
        self._is_paused = True
        logger.info("任务队列已暂停")
    
    def resume_processing(self):
        """恢复处理"""
        self._is_paused = False
        logger.info("任务队列已恢复")
    
    def stop_processing(self):
        """停止处理"""
        self._is_running = False
        self._is_paused = False
        
        # 取消所有正在运行的任务
        for job in self._running_jobs.values():
            job.status = JobStatus.CANCELLED
        
        self._running_jobs.clear()
        
        logger.info("任务队列已停止")
    
    def is_running(self) -> bool:
        """检查队列是否正在运行"""
        return self._is_running
    
    def is_paused(self) -> bool:
        """检查队列是否暂停"""
        return self._is_paused
    
    def wait_for_completion(self, timeout: float = None):
        """等待所有任务完成
        
        Args:
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        
        while (self.get_pending_jobs_count() > 0 or self.get_running_jobs_count() > 0):
            if timeout and (time.time() - start_time) > timeout:
                logger.warning("等待任务完成超时")
                break
            time.sleep(0.1)
        
        logger.info("所有任务已完成")
    
    def get_overall_progress(self) -> Dict[str, Any]:
        """获取整体进度信息"""
        total_jobs = len(self._jobs)
        completed_jobs = self.get_completed_jobs_count()
        failed_jobs = self.get_failed_jobs_count()
        
        if total_jobs == 0:
            progress_percentage = 0.0
        else:
            progress_percentage = (completed_jobs + failed_jobs) / total_jobs * 100
        
        return {
            "total_jobs": total_jobs,
            "completed_jobs": completed_jobs,
            "failed_jobs": failed_jobs,
            "running_jobs": self.get_running_jobs_count(),
            "pending_jobs": self.get_pending_jobs_count(),
            "progress_percentage": progress_percentage
        }
    
    def save_state(self, file_path: Path):
        """保存队列状态到文件"""
        try:
            state_data = {
                "jobs": [asdict(job) for job in self._jobs.values()],
                "max_concurrent_jobs": self._max_concurrent_jobs,
                "timestamp": time.time()
            }
            
            # 处理Path对象序列化
            for job_data in state_data["jobs"]:
                job_data["file_path"] = str(job_data["file_path"])
                job_data["status"] = job_data["status"].value if isinstance(job_data["status"], JobStatus) else job_data["status"]
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"队列状态已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存队列状态失败: {e}")
            raise
    
    def load_state(self, file_path: Path):
        """从文件加载队列状态"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 恢复任务
            self._jobs.clear()
            for job_data in state_data.get("jobs", []):
                # 恢复Path对象
                job_data["file_path"] = Path(job_data["file_path"])
                # 恢复JobStatus枚举
                job_data["status"] = JobStatus(job_data["status"])
                
                job = ProcessingJob(**job_data)
                self._jobs[job.id] = job
            
            # 恢复设置
            self._max_concurrent_jobs = state_data.get("max_concurrent_jobs", 3)
            
            logger.info(f"队列状态已从文件加载: {file_path}")
            
        except Exception as e:
            logger.error(f"加载队列状态失败: {e}")
            raise
    
    def load_state_from_file(self, file_path: Path) -> Dict[str, Any]:
        """从文件读取状态数据（不加载到当前队列）"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"读取状态文件失败: {e}")
            raise
    
    def shutdown(self):
        """关闭队列并清理资源"""
        self.stop_processing()
        
        if self._auto_save and self._state_file:
            try:
                self.save_state(self._state_file)
            except Exception as e:
                logger.error(f"关闭时保存状态失败: {e}")
        
        logger.info("队列已关闭")
    
    def cleanup_completed_jobs(self):
        """清理已完成的任务"""
        completed_jobs = [job_id for job_id, job in self._jobs.items() 
                         if job.status in [JobStatus.COMPLETED, JobStatus.FAILED]]
        
        for job_id in completed_jobs:
            del self._jobs[job_id]
        
        logger.info(f"已清理 {len(completed_jobs)} 个完成的任务")
    
    def get_memory_usage(self) -> int:
        """获取内存使用量（简单实现）"""
        # 简单计算：任务数量 * 估算每个任务的内存占用
        return len(self._jobs) * 1024  # 假设每个任务占用1KB
    
    def _run_processing_loop(self):
        """运行处理循环（在单独线程中）"""
        try:
            # 创建新的事件循环
            self._event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._event_loop)
            
            # 运行主处理逻辑
            self._event_loop.run_until_complete(self._process_jobs())
            
        except Exception as e:
            logger.error(f"处理循环错误: {e}")
        finally:
            if self._event_loop:
                self._event_loop.close()
    
    async def _process_jobs(self):
        """异步处理任务"""
        while self._is_running:
            try:
                if self._is_paused:
                    await asyncio.sleep(0.1)
                    continue
                
                # 获取待处理任务
                pending_jobs = self.get_scheduled_jobs()
                
                # 启动新任务（在并发限制内）
                available_slots = self._max_concurrent_jobs - len(self._running_jobs)
                
                for i in range(min(available_slots, len(pending_jobs))):
                    job = pending_jobs[i]
                    await self._start_job(job)
                
                # 短暂等待避免忙等待
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"处理任务时发生错误: {e}")
                await asyncio.sleep(1)
    
    async def _start_job(self, job: ProcessingJob):
        """启动单个任务"""
        try:
            if not self._processor:
                logger.error("没有设置文件处理器")
                return
            
            # 更新任务状态
            job.status = JobStatus.RUNNING
            job.started_at = time.time()
            self._running_jobs[job.id] = job
            
            logger.info(f"开始处理任务: {job.id}")
            
            # 创建任务
            task = asyncio.create_task(self._process_single_job(job))
            self._worker_tasks.append(task)
            
        except Exception as e:
            logger.error(f"启动任务失败: {e}")
            job.status = JobStatus.FAILED
            job.error_message = str(e)
    
    async def _process_single_job(self, job: ProcessingJob):
        """处理单个任务"""
        try:
            # 调用处理器
            result = await self._processor.process_file(job.file_path)
            
            # 更新任务状态
            job.status = JobStatus.COMPLETED
            job.completed_at = time.time()
            job.progress = 1.0
            
            logger.info(f"任务处理完成: {job.id}")
            
        except ProcessingError as e:
            # 处理错误，可能需要重试
            await self._handle_job_failure(job, str(e))
            
        except Exception as e:
            # 其他错误
            await self._handle_job_failure(job, f"未知错误: {str(e)}")
            
        finally:
            # 从运行列表中移除
            self._running_jobs.pop(job.id, None)
            
            # 自动保存
            if self._auto_save:
                self._save_state()
    
    async def _handle_job_failure(self, job: ProcessingJob, error_message: str):
        """处理任务失败"""
        job.retry_count += 1
        job.error_message = error_message
        
        if job.retry_count <= job.max_retries:
            # 重试
            job.status = JobStatus.PENDING
            logger.warning(f"任务将重试: {job.id}, 重试次数: {job.retry_count}")
        else:
            # 达到最大重试次数，标记为失败
            job.status = JobStatus.FAILED
            job.completed_at = time.time()
            logger.error(f"任务处理失败: {job.id}, 错误: {error_message}")
    
    def _save_state(self):
        """内部方法：自动保存状态"""
        if self._state_file:
            try:
                self.save_state(self._state_file)
            except Exception as e:
                logger.error(f"自动保存状态失败: {e}")