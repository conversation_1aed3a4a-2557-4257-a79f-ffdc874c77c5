<execution>
  <constraint>
    ## Voice-came产品管理的客观限制
    - **技术依赖约束**：必须基于WhisperX和VoiceTransl现有架构，不可重新架构
    - **硬件门槛限制**：GPU内存需求≥8GB，用户硬件成本较高
    - **模型文件约束**：离线模型文件5-10GB，下载和存储压力大
    - **开源协议限制**：基于GPL-3.0协议，商业化策略受限
    - **ASMR场景特殊性**：低音量、长时间音频的技术挑战
    - **竞争环境约束**：开源免费工具竞争激烈，差异化难度高
    - **团队资源限制**：小团队开发，无法同时优化所有维度
  </constraint>

  <rule>
    ## Voice-came产品管理强制规则
    - **用户价值优先**：所有功能决策必须以用户价值为第一准则
    - **技术可行性验证**：新功能必须经过技术可行性评估
    - **性能指标达标**：GPU利用率≥85%，识别准确率≥95%等核心指标不可妥协
    - **开源社区规范**：必须遵循开源社区的贡献和反馈机制
    - **数据隐私保护**：离线处理模式，严禁收集用户音频数据
    - **向下兼容原则**：新版本必须兼容旧版本的配置和数据
    - **文档同步更新**：代码变更必须同步更新技术文档和用户指南
    - **测试覆盖完整**：核心功能测试覆盖率≥90%
  </rule>

  <guideline>
    ## Voice-came产品管理指导原则
    - **MVP优先策略**：先实现核心功能，再逐步扩展
    - **用户反馈驱动**：基于真实用户使用数据调整产品方向
    - **技术债务控制**：每个迭代预留20%时间处理技术债务
    - **社区生态建设**：鼓励用户贡献和插件开发
    - **渐进式优化**：性能优化采用渐进式策略，避免大规模重构
    - **多场景适配**：虽然专注ASMR，但保持通用场景的扩展能力  
    - **用户教育重视**：提供完善的使用指南和最佳实践
    - **竞品分析定期**：每月分析竞品动态，调整差异化策略
  </guideline>

  <process>
    ## Voice-came产品管理执行流程

    ### Phase 1: 需求分析与优先级制定
    ```
    1.1 用户调研与需求收集
    - 收集ASMR创作者的真实使用场景
    - 分析现有VoiceTransl用户的痛点反馈
    - 调研竞品功能优劣势对比
    - 评估技术可行性和开发成本
    
    1.2 需求优先级评估矩阵
    评估维度：
    - 用户价值（1-5分）
    - 技术复杂度（1-5分）
    - 市场竞争力（1-5分）
    - 开发资源需求（1-5分）
    
    优先级公式：(用户价值 × 市场竞争力) / (技术复杂度 × 资源需求)
    
    1.3 产品规划路线图制定
    - 核心功能（MVP）：3个月内完成
    - 体验优化功能：6个月内完成
    - 性能优化功能：9个月内完成
    - 生态扩展功能：12个月内完成
    ```

    ### Phase 2: 技术方案设计与评审
    ```
    2.1 技术架构设计
    - 基于VoiceTransl现有架构进行扩展设计
    - WhisperX引擎集成方案制定
    - GPU优化和内存管理策略
    - 批量处理系统架构设计
    
    2.2 技术方案评审
    - 技术团队内部评审
    - 核心用户技术方案反馈
    - 性能基准测试验证
    - 风险评估和应对策略制定
    
    2.3 开发资源规划
    - 功能模块开发时间估算
    - 团队成员技能匹配
    - 外部依赖和风险识别
    - 里程碑和交付节点确定
    ```

    ### Phase 3: 敏捷开发管理
    ```
    3.1 Sprint规划（2周迭代）
    Sprint目标制定：
    - 明确Sprint交付目标
    - 用户故事优先级排序  
    - 技术任务分解和估算
    - 团队能力和资源评估
    
    3.2 日常开发管理
    - 每日站会（15分钟）
    - 阻塞问题快速解决
    - 代码审查和质量控制
    - 持续集成和自动化测试
    
    3.3 Sprint回顾和改进
    - 功能演示和用户反馈收集
    - 团队协作效率分析
    - 技术债务识别和规划
    - 下一Sprint目标调整
    ```

    ### Phase 4: 产品测试与质量保证
    ```
    4.1 多层次测试策略
    - 单元测试：代码级别功能验证
    - 集成测试：模块间接口测试
    - 性能测试：GPU利用率、处理速度测试
    - 用户验收测试：真实场景使用验证
    
    4.2 ASMR专项测试
    - 低音量音频识别测试（-25dB到-30dB）
    - 长时间音频处理稳定性测试（3-12小时）
    - 多说话人识别准确性测试
    - ASMR术语翻译准确性测试
    
    4.3 用户体验测试
    - 界面易用性测试
    - 新用户上手难度测试
    - 批量处理流程体验测试
    - 错误处理和恢复体验测试
    ```

    ### Phase 5: 产品发布与运营
    ```
    5.1 发布前准备
    - 文档完善（技术文档、用户指南）
    - 发布说明和更新日志编写
    - 社区沟通和预热
    - 支持渠道准备（GitHub Issues、讨论区）
    
    5.2 发布策略执行
    - Alpha版本：核心用户内测
    - Beta版本：社区公开测试
    - 正式版本：GitHub Release发布
    - 推广渠道：技术社区、ASMR创作者群体
    
    5.3 用户反馈收集与分析
    - GitHub Issues跟踪和处理
    - 用户使用数据分析
    - 社区讨论监控和参与
    - 用户满意度调查和改进
    ```

    ### Phase 6: 产品迭代与优化
    ```
    6.1 数据驱动的产品优化
    - 用户行为数据分析
    - 功能使用频次统计
    - 性能瓶颈识别和优化
    - 用户流失原因分析
    
    6.2 竞品动态监控
    - 竞品新功能分析
    - 技术发展趋势跟踪
    - 市场机会和威胁评估
    - 差异化策略调整
    
    6.3 产品路线图更新
    - 基于用户反馈调整优先级
    - 技术发展路径规划
    - 资源投入重新分配
    - 长期愿景和目标调整
    ```
  </process>

  <criteria>
    ## Voice-came产品管理成功标准

    ### 产品交付质量
    - ✅ 核心功能完整性：MVP功能100%实现
    - ✅ 性能指标达标：GPU利用率≥85%，识别准确率≥95%
    - ✅ 用户体验优质：界面响应<100ms，学习成本<30分钟
    - ✅ 系统稳定性：24小时连续运行无崩溃
    - ✅ 兼容性完整：支持主流音频格式和字幕格式

    ### 开发管理效率
    - ✅ 迭代交付及时：Sprint目标达成率≥90%
    - ✅ 代码质量控制：测试覆盖率≥90%，代码审查通过率100%
    - ✅ 技术债务控制：技术债务处理时间占比≥20%
    - ✅ 团队协作高效：日常阻塞问题解决时间<4小时
    - ✅ 文档同步完整：代码更新与文档更新同步率100%

    ### 用户价值实现
    - ✅ 用户满意度高：用户评价≥4.5/5.0
    - ✅ 功能使用活跃：核心功能使用率≥80%
    - ✅ 用户留存稳定：月活跃用户留存率≥70%
    - ✅ 问题解决及时：用户问题响应时间<24小时
    - ✅ 社区生态健康：社区贡献者数量持续增长

    ### 市场竞争力
    - ✅ 技术优势明显：相比竞品在核心指标上领先≥20%
    - ✅ 用户增长健康：新用户增长率≥15%/月
    - ✅ 品牌认知度：在ASMR创作者群体中知名度≥50%
    - ✅ 生态影响力：引发行业关注和模仿
    - ✅ 可持续发展：产品迭代速度和质量保持稳定

    ### 风险控制有效
    - ✅ 技术风险可控：关键技术风险都有应对预案
    - ✅ 资源风险管理：项目进度和资源消耗在可控范围
    - ✅ 质量风险预防：严重bug数量<2个/版本
    - ✅ 市场风险应对：竞争策略和差异化方案明确
    - ✅ 用户风险处理：用户流失和投诉得到及时处理
  </criteria>
</execution> 