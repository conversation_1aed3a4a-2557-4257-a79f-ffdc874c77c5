#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强批量处理队列模块 - TDD Refactor阶段优化实现

Task 2.9: 批量处理队列重构优化 (TDD-Refactor阶段)
目标：在测试保护下重构和优化队列管理代码

重构优化要求：
1. 优化队列性能和内存使用
2. 增强错误恢复和重试机制
3. 改进队列监控和日志记录
4. 确保所有测试持续通过
5. 添加压力测试和性能基准

遵循TDD-Refactor原则：在测试保护下安全重构，提升代码质量
"""

import asyncio
import time
import json
import threading
import logging
import gc
import psutil
import weakref
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any, Union, Set
from enum import Enum
from dataclasses import dataclass, asdict, field
from collections import deque, defaultdict
import heapq
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics

from voice_came.utils.logger import get_logger

logger = get_logger(__name__)


class JobState:
    """任务状态常量类"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class JobPriority:
    """任务优先级常量"""
    LOW = 3
    NORMAL = 2
    HIGH = 1
    URGENT = 0


@dataclass
class QueueJob:
    """队列任务类 - 重构优化版本"""
    
    def __init__(self, id: str, file_path: Path, job_type: str = "processing", 
                 priority: int = JobPriority.NORMAL, **kwargs):
        """初始化队列任务
        
        Args:
            id: 任务唯一标识
            file_path: 文件路径
            job_type: 任务类型
            priority: 任务优先级 (0=最高, 3=最低)
            **kwargs: 其他任务参数
        """
        self.id = id
        self.file_path = Path(file_path) if not isinstance(file_path, Path) else file_path
        self.job_type = job_type
        self.priority = priority
        self.status = JobState.PENDING
        self.progress = 0.0
        self.created_at = time.time()
        self.started_at = None
        self.completed_at = None
        
        # 错误处理和重试相关
        self.error_message = ""
        self.retry_count = 0
        self.max_retries = kwargs.get('max_retries', 3)
        self.retry_delay = kwargs.get('retry_delay', 1.0)
        
        # 性能监控
        self.memory_usage = 0
        self.processing_time = 0.0
        self.estimated_duration = kwargs.get('estimated_duration', 60.0)
        
        # 处理其他参数
        for key, value in kwargs.items():
            if not hasattr(self, key):
                setattr(self, key, value)
    
    def __lt__(self, other):
        """支持优先级队列排序"""
        if self.priority != other.priority:
            return self.priority < other.priority
        return self.created_at < other.created_at
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries
    
    def reset_for_retry(self):
        """重置任务状态以便重试"""
        self.status = JobState.RETRYING
        self.retry_count += 1
        self.started_at = None
        self.progress = 0.0
        self.error_message = ""
    
    def get_estimated_remaining_time(self) -> float:
        """获取估计剩余时间"""
        if self.progress <= 0:
            return self.estimated_duration
        
        elapsed = time.time() - (self.started_at or self.created_at)
        if self.progress > 0:
            total_estimated = elapsed / (self.progress / 100.0)
            return max(0, total_estimated - elapsed)
        
        return self.estimated_duration


@dataclass
class QueueMetrics:
    """队列性能指标"""
    total_jobs: int = 0
    pending_jobs: int = 0
    running_jobs: int = 0
    completed_jobs: int = 0
    failed_jobs: int = 0
    cancelled_jobs: int = 0
    retrying_jobs: int = 0
    
    # 性能指标
    average_processing_time: float = 0.0
    peak_memory_usage: float = 0.0
    current_memory_usage: float = 0.0
    throughput_per_hour: float = 0.0
    success_rate: float = 0.0
    
    # 时间统计
    total_processing_time: float = 0.0
    queue_start_time: float = 0.0
    last_update_time: float = 0.0
    
    def update_from_jobs(self, jobs: Dict[str, QueueJob]):
        """从任务字典更新指标"""
        self.total_jobs = len(jobs)
        self.pending_jobs = sum(1 for job in jobs.values() if job.status == JobState.PENDING)
        self.running_jobs = sum(1 for job in jobs.values() if job.status == JobState.RUNNING)
        self.completed_jobs = sum(1 for job in jobs.values() if job.status == JobState.COMPLETED)
        self.failed_jobs = sum(1 for job in jobs.values() if job.status == JobState.FAILED)
        self.cancelled_jobs = sum(1 for job in jobs.values() if job.status == JobState.CANCELLED)
        self.retrying_jobs = sum(1 for job in jobs.values() if job.status == JobState.RETRYING)
        
        # 计算成功率
        finished_jobs = self.completed_jobs + self.failed_jobs + self.cancelled_jobs
        if finished_jobs > 0:
            self.success_rate = self.completed_jobs / finished_jobs
        
        # 计算平均处理时间
        completed_jobs_list = [job for job in jobs.values() if job.status == JobState.COMPLETED]
        if completed_jobs_list:
            processing_times = [job.processing_time for job in completed_jobs_list if job.processing_time > 0]
            if processing_times:
                self.average_processing_time = statistics.mean(processing_times)
        
        # 计算吞吐量
        if self.queue_start_time > 0:
            hours_running = (time.time() - self.queue_start_time) / 3600.0
            if hours_running > 0:
                self.throughput_per_hour = self.completed_jobs / hours_running
        
        self.last_update_time = time.time()


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.performance_history = deque(maxlen=max_history_size)
        self.memory_samples = deque(maxlen=100)
        self.start_time = time.time()
        
    def record_job_completion(self, job: QueueJob):
        """记录任务完成"""
        sample = {
            'timestamp': time.time(),
            'job_id': job.id,
            'processing_time': job.processing_time,
            'memory_usage': job.memory_usage,
            'status': job.status,
            'retry_count': job.retry_count
        }
        self.performance_history.append(sample)
    
    def record_memory_usage(self):
        """记录内存使用情况"""
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.memory_samples.append({
                'timestamp': time.time(),
                'memory_mb': memory_mb
            })
        except Exception as e:
            logger.warning(f"无法获取内存信息: {e}")
    
    def get_performance_summary(self) -> dict:
        """获取性能摘要"""
        if not self.performance_history:
            return {}
        
        recent_samples = list(self.performance_history)[-100:]  # 最近100个样本
        
        processing_times = [s['processing_time'] for s in recent_samples if s['processing_time'] > 0]
        memory_usages = [s['memory_usage'] for s in recent_samples if s['memory_usage'] > 0]
        
        summary = {
            'total_samples': len(self.performance_history),
            'uptime_hours': (time.time() - self.start_time) / 3600.0,
        }
        
        if processing_times:
            summary.update({
                'avg_processing_time': statistics.mean(processing_times),
                'min_processing_time': min(processing_times),
                'max_processing_time': max(processing_times),
                'processing_time_std': statistics.stdev(processing_times) if len(processing_times) > 1 else 0
            })
        
        if memory_usages:
            summary.update({
                'avg_memory_usage': statistics.mean(memory_usages),
                'peak_memory_usage': max(memory_usages)
            })
        
        if self.memory_samples:
            current_memory = self.memory_samples[-1]['memory_mb']
            summary['current_memory_mb'] = current_memory
        
        return summary


class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self):
        self.error_patterns = defaultdict(int)
        self.recovery_strategies = {
            'FileNotFoundError': self._handle_file_not_found,
            'PermissionError': self._handle_permission_error,
            'MemoryError': self._handle_memory_error,
            'TimeoutError': self._handle_timeout_error,
            'Default': self._handle_default_error
        }
    
    def handle_job_error(self, job: QueueJob, error: Exception) -> bool:
        """处理任务错误
        
        Returns:
            bool: 是否可以重试
        """
        error_type = type(error).__name__
        self.error_patterns[error_type] += 1
        
        logger.warning(f"任务 {job.id} 发生错误: {error_type} - {str(error)}")
        
        # 记录错误信息
        job.error_message = f"{error_type}: {str(error)}"
        
        # 选择恢复策略
        strategy = self.recovery_strategies.get(error_type, self.recovery_strategies['Default'])
        
        return strategy(job, error)
    
    def _handle_file_not_found(self, job: QueueJob, error: Exception) -> bool:
        """处理文件未找到错误"""
        # 文件不存在通常不需要重试
        logger.error(f"文件未找到，跳过重试: {job.file_path}")
        return False
    
    def _handle_permission_error(self, job: QueueJob, error: Exception) -> bool:
        """处理权限错误"""
        # 权限错误可能是临时的，允许重试
        logger.warning(f"权限错误，将重试: {job.file_path}")
        return job.can_retry()
    
    def _handle_memory_error(self, job: QueueJob, error: Exception) -> bool:
        """处理内存错误"""
        # 内存错误需要清理后重试
        logger.warning(f"内存不足，执行垃圾回收后重试")
        gc.collect()
        return job.can_retry()
    
    def _handle_timeout_error(self, job: QueueJob, error: Exception) -> bool:
        """处理超时错误"""
        # 超时错误可以重试，但增加延迟
        job.retry_delay *= 2  # 指数退避
        logger.warning(f"处理超时，将延迟重试: {job.retry_delay}秒")
        return job.can_retry()
    
    def _handle_default_error(self, job: QueueJob, error: Exception) -> bool:
        """处理默认错误"""
        return job.can_retry()
    
    def get_error_statistics(self) -> dict:
        """获取错误统计"""
        total_errors = sum(self.error_patterns.values())
        if total_errors == 0:
            return {}
        
        return {
            'total_errors': total_errors,
            'error_breakdown': dict(self.error_patterns),
            'most_common_error': max(self.error_patterns.items(), key=lambda x: x[1])[0] if self.error_patterns else None
        }


class EnhancedBatchQueue:
    """增强批量处理队列 - TDD Refactor阶段重构优化版本"""
    
    def __init__(self, max_concurrent: int = 4, state_file: Optional[Path] = None, 
                 enable_monitoring: bool = True, cleanup_interval: int = 300, **kwargs):
        """初始化增强批量队列
        
        Args:
            max_concurrent: 最大并发任务数
            state_file: 状态持久化文件路径
            enable_monitoring: 是否启用性能监控
            cleanup_interval: 清理完成任务的间隔时间（秒）
            **kwargs: 其他初始化参数
        """
        self.max_concurrent = max_concurrent
        self.state_file = Path(state_file) if state_file else None
        self.enable_monitoring = enable_monitoring
        self.cleanup_interval = cleanup_interval
        
        # 队列存储 - 优化数据结构
        self._jobs: Dict[str, QueueJob] = {}
        self._pending_queue = []  # 优先级队列 (heapq)
        self._running_jobs: Dict[str, QueueJob] = {}
        self._completed_jobs: Set[str] = set()  # 只保存ID，节省内存
        
        # 控制状态
        self._is_running = False
        self._is_paused = False
        self._progress_callback: Optional[Callable] = None
        self._lock = threading.RLock()  # 线程安全
        
        # 性能优化组件
        self._thread_pool: Optional[ThreadPoolExecutor] = None
        self._metrics = QueueMetrics()
        self._performance_monitor = PerformanceMonitor() if enable_monitoring else None
        self._error_recovery = ErrorRecoveryManager()
        
        # 后台任务
        self._cleanup_timer: Optional[threading.Timer] = None
        self._monitoring_timer: Optional[threading.Timer] = None
        
        # 如果有状态文件，尝试加载
        if self.state_file and self.state_file.exists():
            self._load_state_from_file()
        
        # 设置定期清理
        if cleanup_interval > 0:
            self._schedule_cleanup()
    
    def add_job(self, job: QueueJob) -> str:
        """添加任务到队列（线程安全）
        
        Args:
            job: 队列任务对象
            
        Returns:
            str: 任务ID
        """
        with self._lock:
            if job.id in self._jobs:
                raise ValueError(f"任务ID已存在: {job.id}")
            
            self._jobs[job.id] = job
            job.status = JobState.PENDING
            
            # 添加到优先级队列
            heapq.heappush(self._pending_queue, job)
            
            logger.debug(f"任务已添加到队列: {job.id} (优先级: {job.priority})")
            
            return job.id
    
    def remove_job(self, job_id: str) -> QueueJob:
        """从队列移除任务（线程安全）
        
        Args:
            job_id: 任务ID
            
        Returns:
            QueueJob: 被移除的任务
            
        Raises:
            KeyError: 如果任务不存在
        """
        with self._lock:
            if job_id not in self._jobs:
                raise KeyError(f"任务不存在: {job_id}")
            
            job = self._jobs.pop(job_id)
            
            # 从相应的队列中移除
            if job_id in self._running_jobs:
                self._running_jobs.pop(job_id)
                job.status = JobState.CANCELLED
            
            self._completed_jobs.discard(job_id)
            
            # 从优先级队列中移除（标记为取消）
            job.status = JobState.CANCELLED
            
            logger.debug(f"任务已从队列移除: {job_id}")
            
            return job
    
    def size(self) -> int:
        """获取队列总大小"""
        with self._lock:
            return len(self._jobs)
    
    def get_running_count(self) -> int:
        """获取正在运行的任务数量"""
        with self._lock:
            return len(self._running_jobs)
    
    def get_pending_count(self) -> int:
        """获取待处理任务数量 - 优化版本"""
        with self._lock:
            # 直接统计优先级队列中未取消的任务
            return len([job for job in self._pending_queue if job.status == JobState.PENDING])
    
    def get_metrics(self) -> QueueMetrics:
        """获取队列指标"""
        with self._lock:
            self._metrics.update_from_jobs(self._jobs)
            return self._metrics
    
    def get_performance_summary(self) -> dict:
        """获取性能摘要"""
        if not self._performance_monitor:
            return {}
        
        summary = self._performance_monitor.get_performance_summary()
        summary.update({
            'queue_metrics': asdict(self.get_metrics()),
            'error_statistics': self._error_recovery.get_error_statistics()
        })
        
        return summary
    
    def start(self):
        """启动队列处理"""
        with self._lock:
            if self._is_running:
                logger.warning("队列已在运行中")
                return
            
            self._is_running = True
            self._is_paused = False
            
            # 初始化线程池
            self._thread_pool = ThreadPoolExecutor(
                max_workers=self.max_concurrent + 2,  # +2 用于管理任务
                thread_name_prefix="BatchQueue"
            )
            
            # 更新开始时间
            if self._metrics.queue_start_time == 0:
                self._metrics.queue_start_time = time.time()
            
            # 启动主处理循环
            self._thread_pool.submit(self._process_jobs_loop)
            
            # 启动监控
            if self.enable_monitoring:
                self._start_monitoring()
            
            logger.info(f"队列处理已启动，最大并发数: {self.max_concurrent}")
    
    def pause(self):
        """暂停队列处理"""
        with self._lock:
            if not self._is_running:
                logger.warning("队列未运行")
                return
            
            self._is_paused = True
            logger.info("队列处理已暂停")
    
    def resume(self):
        """恢复队列处理"""
        with self._lock:
            if not self._is_running:
                logger.warning("队列未运行")
                return
            
            self._is_paused = False
            logger.info("队列处理已恢复")
    
    def stop(self, wait_for_completion: bool = False):
        """停止队列处理
        
        Args:
            wait_for_completion: 是否等待正在运行的任务完成
        """
        with self._lock:
            if not self._is_running:
                logger.warning("队列未运行")
                return
            
            self._is_running = False
            self._is_paused = False
            
            logger.info(f"正在停止队列处理... 等待完成: {wait_for_completion}")
            
            if not wait_for_completion:
                # 立即取消所有运行中的任务
                for job in self._running_jobs.values():
                    job.status = JobState.CANCELLED
                self._running_jobs.clear()
            
            # 关闭线程池
            if self._thread_pool:
                self._thread_pool.shutdown(wait=wait_for_completion)
                self._thread_pool = None
            
            # 停止定时器
            self._stop_timers()
            
            logger.info("队列处理已停止")
    
    def _process_jobs_loop(self):
        """主处理循环 - 优化版本"""
        logger.debug("开始主处理循环")
        
        while self._is_running:
            try:
                if self._is_paused:
                    time.sleep(0.1)
                    continue
                
                # 检查是否可以启动新任务
                with self._lock:
                    if (len(self._running_jobs) < self.max_concurrent and 
                        self._pending_queue):
                        
                        # 获取最高优先级的待处理任务
                        job = None
                        temp_jobs = []
                        
                        # 从优先级队列中找到第一个有效的待处理任务
                        while self._pending_queue:
                            candidate = heapq.heappop(self._pending_queue)
                            if candidate.status == JobState.PENDING:
                                job = candidate
                                break
                            elif candidate.status == JobState.RETRYING:
                                # 检查重试延迟
                                if time.time() - candidate.completed_at >= candidate.retry_delay:
                                    job = candidate
                                    break
                                else:
                                    temp_jobs.append(candidate)
                            # 忽略已取消的任务
                        
                        # 将暂时不能处理的任务放回队列
                        for temp_job in temp_jobs:
                            heapq.heappush(self._pending_queue, temp_job)
                        
                        if job:
                            self._start_job_processing(job)
                
                time.sleep(0.01)  # 短暂暂停，减少CPU占用
                
            except Exception as e:
                logger.error(f"主处理循环发生错误: {e}")
                time.sleep(1)  # 错误时暂停更长时间
        
        logger.debug("主处理循环已结束")
    
    def _start_job_processing(self, job: QueueJob):
        """开始处理单个任务"""
        with self._lock:
            job.status = JobState.RUNNING
            job.started_at = time.time()
            self._running_jobs[job.id] = job
        
        # 在线程池中异步处理任务
        if self._thread_pool:
            future = self._thread_pool.submit(self._process_single_job, job)
            # 不需要等待，让线程池管理
    
    def _process_single_job(self, job: QueueJob):
        """处理单个任务 - 重构优化版本"""
        start_memory = self._get_current_memory_usage()
        
        try:
            logger.info(f"开始处理任务: {job.id}")
            
            # 模拟处理过程 - 更真实的进度更新
            total_steps = 10
            for step in range(total_steps):
                if not self._is_running or job.status == JobState.CANCELLED:
                    logger.info(f"任务被取消: {job.id}")
                    return
                
                # 模拟处理步骤
                time.sleep(0.05)  # 模拟处理时间
                
                # 更新进度
                progress = (step + 1) / total_steps * 100
                job.progress = progress
                
                # 触发进度回调
                if self._progress_callback:
                    try:
                        self._progress_callback(job.id, progress)
                    except Exception as e:
                        logger.warning(f"进度回调错误: {e}")
                
                # 记录内存使用
                if self._performance_monitor and step % 3 == 0:
                    self._performance_monitor.record_memory_usage()
            
            # 任务完成
            with self._lock:
                job.status = JobState.COMPLETED
                job.completed_at = time.time()
                job.processing_time = job.completed_at - job.started_at
                job.memory_usage = self._get_current_memory_usage() - start_memory
                
                # 移动到完成集合
                self._running_jobs.pop(job.id, None)
                self._completed_jobs.add(job.id)
            
            logger.info(f"任务处理完成: {job.id} (耗时: {job.processing_time:.2f}秒)")
            
            # 记录性能指标
            if self._performance_monitor:
                self._performance_monitor.record_job_completion(job)
            
        except Exception as e:
            logger.error(f"任务处理失败: {job.id} - {e}")
            
            # 使用错误恢复管理器处理错误
            should_retry = self._error_recovery.handle_job_error(job, e)
            
            with self._lock:
                if should_retry:
                    # 重试任务
                    job.reset_for_retry()
                    job.completed_at = time.time()  # 设置重试时间基准
                    heapq.heappush(self._pending_queue, job)
                    logger.info(f"任务将重试: {job.id} (第{job.retry_count}次)")
                else:
                    # 标记为失败
                    job.status = JobState.FAILED
                    job.completed_at = time.time()
                    job.processing_time = job.completed_at - (job.started_at or job.created_at)
                
                # 从运行列表中移除
                self._running_jobs.pop(job.id, None)
                
                if job.status == JobState.FAILED:
                    self._completed_jobs.add(job.id)
            
            # 记录失败的性能指标
            if self._performance_monitor:
                self._performance_monitor.record_job_completion(job)
        
        finally:
            # 确保任务不在运行列表中
            with self._lock:
                self._running_jobs.pop(job.id, None)
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数
        
        Args:
            callback: 进度回调函数，接收(job_id, progress)参数
        """
        self._progress_callback = callback
        logger.debug("进度回调函数已设置")
    
    def save_state(self):
        """保存队列状态到文件 - 优化版本"""
        if not self.state_file:
            return
        
        try:
            # 确保目录存在
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            with self._lock:
                # 准备状态数据 - 只保存必要信息
                state_data = {
                    "jobs": {},
                    "metadata": {
                        "total_jobs": len(self._jobs),
                        "max_concurrent": self.max_concurrent,
                        "saved_at": time.time(),
                        "queue_start_time": self._metrics.queue_start_time,
                        "version": "2.9"  # 版本号，用于向后兼容
                    }
                }
                
                # 只序列化未完成的任务（节省空间）
                for job_id, job in self._jobs.items():
                    if job.status not in [JobState.COMPLETED, JobState.CANCELLED]:
                        state_data["jobs"][job_id] = {
                            "id": job.id,
                            "file_path": str(job.file_path),
                            "job_type": job.job_type,
                            "priority": job.priority,
                            "status": job.status,
                            "progress": job.progress,
                            "created_at": job.created_at,
                            "started_at": job.started_at,
                            "completed_at": job.completed_at,
                            "retry_count": job.retry_count,
                            "max_retries": job.max_retries,
                            "retry_delay": job.retry_delay,
                            "error_message": job.error_message
                        }
            
            # 异步写入文件
            temp_file = self.state_file.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            
            # 原子性替换
            temp_file.replace(self.state_file)
            
            logger.debug(f"队列状态已保存: {len(state_data['jobs'])} 个活跃任务")
            
        except Exception as e:
            logger.error(f"保存队列状态失败: {e}")
    
    def _load_state_from_file(self):
        """从文件加载队列状态 - 兼容性增强版本"""
        if not self.state_file or not self.state_file.exists():
            return
        
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 检查版本兼容性
            version = state_data.get("metadata", {}).get("version", "2.8")
            logger.debug(f"加载状态文件版本: {version}")
            
            # 恢复任务数据
            jobs_data = state_data.get("jobs", {})
            for job_id, job_data in jobs_data.items():
                job = QueueJob(
                    id=job_data["id"],
                    file_path=Path(job_data["file_path"]),
                    job_type=job_data.get("job_type", "processing"),
                    priority=job_data.get("priority", JobPriority.NORMAL)
                )
                
                # 恢复任务状态
                job.status = job_data.get("status", JobState.PENDING)
                job.progress = job_data.get("progress", 0.0)
                job.created_at = job_data.get("created_at", time.time())
                job.started_at = job_data.get("started_at")
                job.completed_at = job_data.get("completed_at")
                
                # 恢复重试相关信息（新版本）
                if version >= "2.9":
                    job.retry_count = job_data.get("retry_count", 0)
                    job.max_retries = job_data.get("max_retries", 3)
                    job.retry_delay = job_data.get("retry_delay", 1.0)
                    job.error_message = job_data.get("error_message", "")
                
                self._jobs[job_id] = job
                
                # 根据状态分配到相应队列
                if job.status in [JobState.PENDING, JobState.RETRYING]:
                    heapq.heappush(self._pending_queue, job)
                elif job.status == JobState.RUNNING:
                    # 重启后将运行中的任务重置为待处理
                    job.status = JobState.PENDING
                    heapq.heappush(self._pending_queue, job)
            
            # 恢复元数据
            metadata = state_data.get("metadata", {})
            if "max_concurrent" in metadata:
                self.max_concurrent = metadata["max_concurrent"]
            if "queue_start_time" in metadata:
                self._metrics.queue_start_time = metadata["queue_start_time"]
            
            logger.info(f"从状态文件恢复了 {len(jobs_data)} 个任务")
                
        except Exception as e:
            logger.error(f"加载状态文件失败: {e}")
            # 如果加载失败，继续使用空队列
            self._jobs = {}
            self._pending_queue = []
    
    def _schedule_cleanup(self):
        """安排定期清理任务"""
        if self.cleanup_interval > 0:
            self._cleanup_timer = threading.Timer(self.cleanup_interval, self._cleanup_completed_jobs)
            self._cleanup_timer.daemon = True
            self._cleanup_timer.start()
    
    def _cleanup_completed_jobs(self):
        """清理已完成的任务以节省内存"""
        try:
            with self._lock:
                # 获取完成时间超过1小时的任务
                cutoff_time = time.time() - 3600  # 1小时前
                
                jobs_to_remove = []
                for job_id in self._completed_jobs.copy():
                    if job_id in self._jobs:
                        job = self._jobs[job_id]
                        if (job.status in [JobState.COMPLETED, JobState.FAILED, JobState.CANCELLED] and
                            job.completed_at and job.completed_at < cutoff_time):
                            jobs_to_remove.append(job_id)
                
                # 删除过期任务
                for job_id in jobs_to_remove:
                    self._jobs.pop(job_id, None)
                    self._completed_jobs.discard(job_id)
                
                if jobs_to_remove:
                    logger.debug(f"清理了 {len(jobs_to_remove)} 个过期完成任务")
                
                # 执行垃圾回收
                gc.collect()
        
        except Exception as e:
            logger.error(f"清理任务时发生错误: {e}")
        
        finally:
            # 重新安排下一次清理
            if self._is_running:
                self._schedule_cleanup()
    
    def _start_monitoring(self):
        """启动性能监控"""
        if self._performance_monitor:
            def monitor_loop():
                while self._is_running:
                    try:
                        self._performance_monitor.record_memory_usage()
                        time.sleep(10)  # 每10秒记录一次
                    except Exception as e:
                        logger.warning(f"监控过程中发生错误: {e}")
                        break
            
            if self._thread_pool:
                self._thread_pool.submit(monitor_loop)
    
    def _stop_timers(self):
        """停止所有定时器"""
        if self._cleanup_timer:
            self._cleanup_timer.cancel()
            self._cleanup_timer = None
        
        if self._monitoring_timer:
            self._monitoring_timer.cancel()
            self._monitoring_timer = None
    
    def _get_current_memory_usage(self) -> float:
        """获取当前内存使用量(MB)"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0
    
    def __del__(self):
        """析构函数：确保资源清理"""
        try:
            self.stop(wait_for_completion=False)
        except:
            pass


class QueueManager:
    """队列管理器 - 重构优化版本"""
    
    def __init__(self):
        self.queues: Dict[str, EnhancedBatchQueue] = {}
        self._lock = threading.Lock()
    
    def create_queue(self, name: str, **kwargs) -> EnhancedBatchQueue:
        """创建新队列"""
        with self._lock:
            if name in self.queues:
                raise ValueError(f"队列已存在: {name}")
            
            queue = EnhancedBatchQueue(**kwargs)
            self.queues[name] = queue
            
            logger.info(f"创建新队列: {name}")
            return queue
    
    def get_queue(self, name: str) -> Optional[EnhancedBatchQueue]:
        """获取队列"""
        return self.queues.get(name)
    
    def remove_queue(self, name: str):
        """移除队列"""
        with self._lock:
            if name in self.queues:
                queue = self.queues.pop(name)
                queue.stop(wait_for_completion=False)
                logger.info(f"移除队列: {name}")
    
    def get_global_metrics(self) -> dict:
        """获取全局指标"""
        total_metrics = {
            'total_queues': len(self.queues),
            'total_jobs': 0,
            'total_running': 0,
            'total_pending': 0,
            'total_completed': 0,
            'total_failed': 0
        }
        
        for queue in self.queues.values():
            metrics = queue.get_metrics()
            total_metrics['total_jobs'] += metrics.total_jobs
            total_metrics['total_running'] += metrics.running_jobs
            total_metrics['total_pending'] += metrics.pending_jobs
            total_metrics['total_completed'] += metrics.completed_jobs
            total_metrics['total_failed'] += metrics.failed_jobs
        
        return total_metrics 