#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块单元测试
"""

import sys
import os
import pytest
import logging
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from voice_came.utils.logger import get_logger, setup_file_logging


class TestGetLogger:
    """get_logger函数测试"""
    
    def test_get_logger_basic(self):
        """测试基本的日志器获取"""
        logger = get_logger("test_logger")
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == "test_logger"
        assert logger.level == logging.INFO
        assert len(logger.handlers) > 0
    
    def test_get_logger_with_level(self):
        """测试指定日志级别"""
        logger = get_logger("test_logger_debug", "DEBUG")
        assert logger.level == logging.DEBUG
        
        logger = get_logger("test_logger_warning", "WARNING")
        assert logger.level == logging.WARNING
        
        logger = get_logger("test_logger_error", "ERROR")
        assert logger.level == logging.ERROR
    
    def test_get_logger_case_insensitive_level(self):
        """测试日志级别大小写不敏感"""
        logger1 = get_logger("test_case1", "debug")
        logger2 = get_logger("test_case2", "DEBUG")
        logger3 = get_logger("test_case3", "Debug")
        
        assert logger1.level == logging.DEBUG
        assert logger2.level == logging.DEBUG
        assert logger3.level == logging.DEBUG
    
    def test_get_logger_reuse_existing(self):
        """测试重复获取同名日志器"""
        logger1 = get_logger("same_name")
        logger2 = get_logger("same_name")
        
        # 应该是同一个对象
        assert logger1 is logger2
        # 不应该重复添加处理器
        assert len(logger1.handlers) == len(logger2.handlers)
    
    def test_get_logger_handler_configuration(self):
        """测试日志处理器配置"""
        logger = get_logger("test_handler", "INFO")
        
        # 检查处理器
        assert len(logger.handlers) > 0
        handler = logger.handlers[0]
        assert isinstance(handler, logging.StreamHandler)
        assert handler.level == logging.INFO
        
        # 检查格式器
        formatter = handler.formatter
        assert formatter is not None
        assert "%(asctime)s" in formatter._fmt
        assert "%(name)s" in formatter._fmt
        assert "%(levelname)s" in formatter._fmt
        assert "%(message)s" in formatter._fmt
    
    def test_get_logger_invalid_level(self):
        """测试无效的日志级别"""
        with pytest.raises(AttributeError):
            get_logger("test_invalid", "INVALID_LEVEL")
    
    def test_get_logger_output_stream(self):
        """测试日志器输出流配置"""
        logger = get_logger("test_stream")
        handler = logger.handlers[0]
        assert handler.stream == sys.stdout
    
    @patch('logging.getLogger')
    def test_get_logger_mock(self, mock_get_logger):
        """测试使用Mock的日志器获取"""
        mock_logger = MagicMock()
        mock_logger.handlers = []
        mock_get_logger.return_value = mock_logger
        
        result = get_logger("mock_test")
        
        mock_get_logger.assert_called_once_with("mock_test")
        mock_logger.setLevel.assert_called_once_with(logging.INFO)
        mock_logger.addHandler.assert_called_once()


class TestSetupFileLogging:
    """setup_file_logging函数测试"""
    
    def test_setup_file_logging_default(self):
        """测试默认文件日志设置"""
        with patch('logging.FileHandler') as mock_file_handler:
            with patch('logging.getLogger') as mock_get_logger:
                mock_handler = MagicMock()
                mock_file_handler.return_value = mock_handler
                mock_root_logger = MagicMock()
                mock_get_logger.return_value = mock_root_logger
                
                setup_file_logging()
                
                # 检查FileHandler创建
                mock_file_handler.assert_called_once_with(Path("voice_came.log"), encoding='utf-8')
                
                # 检查处理器配置
                mock_handler.setLevel.assert_called_once_with(logging.DEBUG)
                mock_handler.setFormatter.assert_called_once()
                
                # 检查添加到根日志器
                mock_root_logger.addHandler.assert_called_once_with(mock_handler)
    
    def test_setup_file_logging_custom_path(self):
        """测试自定义文件路径"""
        custom_path = Path("custom_log.log")
        
        with patch('logging.FileHandler') as mock_file_handler:
            with patch('logging.getLogger') as mock_get_logger:
                mock_handler = MagicMock()
                mock_file_handler.return_value = mock_handler
                mock_root_logger = MagicMock()
                mock_get_logger.return_value = mock_root_logger
                
                setup_file_logging(custom_path)
                
                mock_file_handler.assert_called_once_with(custom_path, encoding='utf-8')
    
    def test_setup_file_logging_formatter(self):
        """测试文件日志格式器"""
        with patch('logging.FileHandler') as mock_file_handler:
            with patch('logging.getLogger') as mock_get_logger:
                with patch('logging.Formatter') as mock_formatter:
                    mock_handler = MagicMock()
                    mock_file_handler.return_value = mock_handler
                    mock_root_logger = MagicMock()
                    mock_get_logger.return_value = mock_root_logger
                    mock_formatter_instance = MagicMock()
                    mock_formatter.return_value = mock_formatter_instance
                    
                    setup_file_logging()
                    
                    # 检查格式器创建
                    expected_format = '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
                    mock_formatter.assert_called_once_with(expected_format)
                    mock_handler.setFormatter.assert_called_once_with(mock_formatter_instance)
    
    def test_setup_file_logging_with_temp_file(self):
        """测试使用临时文件的文件日志"""
        # 使用mock而不是真实文件，避免Windows文件锁定问题
        with patch('logging.FileHandler') as mock_file_handler:
            with patch('logging.getLogger') as mock_get_logger:
                mock_handler = MagicMock()
                mock_file_handler.return_value = mock_handler
                mock_root_logger = MagicMock()
                mock_get_logger.return_value = mock_root_logger
                
                temp_path = Path("test_temp.log")
                setup_file_logging(temp_path)
                
                # 验证添加了处理器
                mock_root_logger.addHandler.assert_called_once()
                mock_file_handler.assert_called_once_with(temp_path, encoding='utf-8')


class TestLoggerIntegration:
    """日志器集成测试"""
    
    def test_logger_actual_logging(self):
        """测试实际的日志记录功能"""
        with patch('sys.stdout') as mock_stdout:
            logger = get_logger("integration_test", "DEBUG")
            
            logger.debug("Debug message")
            logger.info("Info message")
            logger.warning("Warning message")
            logger.error("Error message")
            
            # 验证有输出到stdout
            assert mock_stdout.write.called
    
    def test_multiple_loggers_independence(self):
        """测试多个日志器的独立性"""
        logger1 = get_logger("logger1", "DEBUG")
        logger2 = get_logger("logger2", "WARNING")
        
        assert logger1.name != logger2.name
        assert logger1.level == logging.DEBUG
        assert logger2.level == logging.WARNING
        
        # 每个日志器都有自己的处理器
        assert len(logger1.handlers) > 0
        assert len(logger2.handlers) > 0
    
    def test_logger_hierarchy(self):
        """测试日志器层次结构"""
        parent_logger = get_logger("parent")
        child_logger = get_logger("parent.child")
        
        assert child_logger.parent.name == "parent"
    
    def test_file_and_console_logging_together(self):
        """测试文件和控制台日志同时使用"""
        # 创建控制台日志器（不使用mock）
        console_logger = get_logger("console_test")
        
        # 使用mock设置文件日志
        with patch('logging.FileHandler') as mock_file_handler:
            with patch('logging.getLogger') as mock_get_logger:
                mock_handler = MagicMock()
                mock_file_handler.return_value = mock_handler
                mock_root_logger = MagicMock()
                mock_get_logger.return_value = mock_root_logger
                
                # 设置文件日志
                setup_file_logging()
                
                # 验证各自独立工作
                assert isinstance(console_logger, logging.Logger)
                mock_root_logger.addHandler.assert_called_once()


class TestLoggerEdgeCases:
    """日志器边界情况测试"""
    
    def test_empty_logger_name(self):
        """测试空日志器名称"""
        logger = get_logger("")
        # 空字符串会被转换为root日志器
        assert logger.name == "root"
        assert isinstance(logger, logging.Logger)
    
    def test_special_characters_in_name(self):
        """测试名称中的特殊字符"""
        special_name = "test.logger-with_special@chars"
        logger = get_logger(special_name)
        assert logger.name == special_name
    
    def test_unicode_logger_name(self):
        """测试Unicode日志器名称"""
        unicode_name = "测试日志器名称"
        logger = get_logger(unicode_name)
        assert logger.name == unicode_name
    
    def test_very_long_logger_name(self):
        """测试超长日志器名称"""
        long_name = "x" * 1000
        logger = get_logger(long_name)
        assert logger.name == long_name
    
    def test_none_log_file_path(self):
        """测试None日志文件路径"""
        with patch('logging.FileHandler') as mock_file_handler:
            with patch('logging.getLogger') as mock_get_logger:
                mock_handler = MagicMock()
                mock_file_handler.return_value = mock_handler
                mock_root_logger = MagicMock()
                mock_get_logger.return_value = mock_root_logger
                
                setup_file_logging(None)
                
                # 应该使用默认路径
                mock_file_handler.assert_called_once_with(Path("voice_came.log"), encoding='utf-8')
    
    def test_pathlib_path_object(self):
        """测试Path对象作为参数"""
        custom_path = Path("test_path.log")
        
        with patch('logging.FileHandler') as mock_file_handler:
            with patch('logging.getLogger') as mock_get_logger:
                mock_handler = MagicMock()
                mock_file_handler.return_value = mock_handler
                mock_root_logger = MagicMock()
                mock_get_logger.return_value = mock_root_logger
                
                setup_file_logging(custom_path)
                
                mock_file_handler.assert_called_once_with(custom_path, encoding='utf-8')


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 