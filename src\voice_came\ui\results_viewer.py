#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4.4: 翻译结果查看器组件 (TDD-Green最小实现)

提供翻译结果的查看和编辑界面，包括原文译文对比、
质量指标可视化、交互式编辑等功能。
"""

from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
import logging

from ..translation.models import TranslationResult, QualityScore

logger = logging.getLogger(__name__)


@dataclass
class ComparisonData:
    """翻译对比数据"""
    original: str
    translation: str
    quality_score: float
    terminology_applied: List[str] = field(default_factory=list)
    confidence: float = 0.0
    processing_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EditHistory:
    """编辑历史记录"""
    timestamp: datetime
    original_text: str
    edited_text: str
    editor: str
    reason: str


class TranslationResultsViewer:
    """翻译结果查看器 - TDD Green最小实现"""
    
    def __init__(self):
        self.current_results: Dict[str, TranslationResult] = {}
        self.comparison_data: Optional[ComparisonData] = None
        self.edit_history: Dict[str, List[EditHistory]] = {}
        self.display_callbacks: List[Callable] = []
        self.current_job_id: Optional[str] = None
    
    def load_translation_results(self, job_id: str, results: List[TranslationResult]) -> None:
        """加载翻译结果"""
        # TDD Green: 最小实现，存储结果
        self.current_job_id = job_id
        
        for result in results:
            self.current_results[result.job_id] = result
        
        logger.info(f"Loaded {len(results)} translation results for job {job_id}")
        self._notify_display_callbacks()
    
    def display_translation_comparison(self, comparison_data: Dict[str, Any]) -> None:
        """显示翻译对比"""
        # TDD Green: 最小实现，存储对比数据
        self.comparison_data = ComparisonData(
            original=comparison_data.get("original", ""),
            translation=comparison_data.get("translation", ""),
            quality_score=comparison_data.get("quality_score", 0.0),
            terminology_applied=comparison_data.get("terminology_applied", []),
            confidence=comparison_data.get("confidence", 0.0),
            processing_time=comparison_data.get("processing_time", 0.0),
            metadata=comparison_data.get("metadata", {})
        )
        
        logger.info("Translation comparison data updated")
        self._notify_display_callbacks()
    
    def get_comparison_data(self) -> Optional[Dict[str, Any]]:
        """获取对比数据"""
        # TDD Green: 最小实现，返回对比数据
        if not self.comparison_data:
            return None
        
        return {
            "original": self.comparison_data.original,
            "translation": self.comparison_data.translation,
            "quality_score": self.comparison_data.quality_score,
            "terminology_applied": self.comparison_data.terminology_applied,
            "confidence": self.comparison_data.confidence,
            "processing_time": self.comparison_data.processing_time,
            "metadata": self.comparison_data.metadata
        }
    
    def visualize_quality_metrics(self, quality_metrics: Dict[str, float]) -> Dict[str, Any]:
        """可视化质量指标"""
        # TDD Green: 最小实现，处理质量指标数据
        visualization_data = {
            "metrics": quality_metrics,
            "overall_score": quality_metrics.get("overall_score", 0.0),
            "chart_data": [],
            "recommendations": []
        }
        
        # 生成图表数据
        for metric_name, score in quality_metrics.items():
            visualization_data["chart_data"].append({
                "name": metric_name,
                "value": score,
                "color": self._get_score_color(score)
            })
        
        # 生成改进建议
        if quality_metrics.get("fluency", 0.0) < 0.8:
            visualization_data["recommendations"].append("建议改进翻译流畅度")
        
        if quality_metrics.get("terminology_consistency", 0.0) < 0.9:
            visualization_data["recommendations"].append("建议检查术语一致性")
        
        if quality_metrics.get("tone_appropriateness", 0.0) < 0.8:
            visualization_data["recommendations"].append("建议调整语调以更适合助眠内容")
        
        logger.info("Quality metrics visualization data generated")
        return visualization_data
    
    def _get_score_color(self, score: float) -> str:
        """根据分数获取颜色"""
        # TDD Green: 最小实现，简单的颜色映射
        if score >= 0.9:
            return "green"
        elif score >= 0.7:
            return "yellow"
        else:
            return "red"
    
    def edit_translation(self, original_translation: str, editor: str = "user") -> str:
        """编辑翻译结果"""
        # TDD Green: 最小实现，模拟编辑功能
        # 在实际实现中，这里会打开编辑界面
        # 现在只是返回一个改进的版本
        
        edited_translation = original_translation
        
        # 简单的自动改进
        corrections = {
            "thinking": "meditation",
            "loosening": "relaxation",
            "sleeping aid": "sleep aid"
        }
        
        for incorrect, correct in corrections.items():
            if incorrect in edited_translation:
                edited_translation = edited_translation.replace(incorrect, correct)
        
        # 记录编辑历史
        if self.current_job_id:
            if self.current_job_id not in self.edit_history:
                self.edit_history[self.current_job_id] = []
            
            edit_record = EditHistory(
                timestamp=datetime.now(),
                original_text=original_translation,
                edited_text=edited_translation,
                editor=editor,
                reason="术语纠正"
            )
            
            self.edit_history[self.current_job_id].append(edit_record)
        
        logger.info(f"Translation edited by {editor}")
        return edited_translation
    
    def save_edited_translation(self, job_id: str, edited_translation: str) -> bool:
        """保存编辑后的翻译"""
        # TDD Green: 最小实现，保存编辑结果
        if job_id not in self.current_results:
            logger.warning(f"Job {job_id} not found in current results")
            return False
        
        # 更新翻译结果
        result = self.current_results[job_id]
        original_translation = result.translated_text
        result.translated_text = edited_translation
        
        # 更新元数据
        if "edit_history" not in result.metadata:
            result.metadata["edit_history"] = []
        
        result.metadata["edit_history"].append({
            "timestamp": datetime.now().isoformat(),
            "original": original_translation,
            "edited": edited_translation
        })
        
        logger.info(f"Saved edited translation for job {job_id}")
        self._notify_display_callbacks()
        return True
    
    def get_edit_history(self, job_id: str) -> List[Dict[str, Any]]:
        """获取编辑历史"""
        # TDD Green: 最小实现，返回编辑历史
        if job_id not in self.edit_history:
            return []
        
        return [
            {
                "timestamp": edit.timestamp.isoformat(),
                "original_text": edit.original_text,
                "edited_text": edit.edited_text,
                "editor": edit.editor,
                "reason": edit.reason
            }
            for edit in self.edit_history[job_id]
        ]
    
    def export_results(self, job_id: str, export_format: str = "json") -> Optional[str]:
        """导出翻译结果"""
        # TDD Green: 最小实现，模拟导出功能
        if job_id not in self.current_results:
            return None
        
        result = self.current_results[job_id]
        
        # 生成导出文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_filename = f"translation_result_{job_id}_{timestamp}.{export_format}"
        
        # 在实际实现中会生成真实的导出文件
        logger.info(f"Exported translation result to {export_filename}")
        return export_filename
    
    def compare_translations(self, job_id1: str, job_id2: str) -> Dict[str, Any]:
        """比较两个翻译结果"""
        # TDD Green: 最小实现，基本比较功能
        if job_id1 not in self.current_results or job_id2 not in self.current_results:
            return {"error": "One or both translation results not found"}
        
        result1 = self.current_results[job_id1]
        result2 = self.current_results[job_id2]
        
        comparison = {
            "job_id1": job_id1,
            "job_id2": job_id2,
            "original_text": result1.original_text,
            "translation1": result1.translated_text,
            "translation2": result2.translated_text,
            "quality_score1": result1.quality_score,
            "quality_score2": result2.quality_score,
            "quality_difference": result2.quality_score - result1.quality_score,
            "better_translation": job_id2 if result2.quality_score > result1.quality_score else job_id1,
            "differences": self._find_text_differences(result1.translated_text, result2.translated_text)
        }
        
        return comparison
    
    def _find_text_differences(self, text1: str, text2: str) -> List[Dict[str, str]]:
        """查找文本差异"""
        # TDD Green: 最小实现，简单的差异检测
        words1 = text1.split()
        words2 = text2.split()
        
        differences = []
        
        # 简单的单词级别差异检测
        for i, (word1, word2) in enumerate(zip(words1, words2)):
            if word1 != word2:
                differences.append({
                    "position": i,
                    "text1": word1,
                    "text2": word2,
                    "type": "substitution"
                })
        
        return differences
    
    def add_display_callback(self, callback: Callable) -> None:
        """添加显示回调"""
        # TDD Green: 最小实现，添加回调
        if callback not in self.display_callbacks:
            self.display_callbacks.append(callback)
    
    def remove_display_callback(self, callback: Callable) -> None:
        """移除显示回调"""
        # TDD Green: 最小实现，移除回调
        if callback in self.display_callbacks:
            self.display_callbacks.remove(callback)
    
    def _notify_display_callbacks(self) -> None:
        """通知显示回调"""
        # TDD Green: 最小实现，调用所有回调
        for callback in self.display_callbacks:
            try:
                callback(self.get_current_display_data())
            except Exception as e:
                logger.error(f"Error in display callback: {e}")
    
    def get_current_display_data(self) -> Dict[str, Any]:
        """获取当前显示数据"""
        # TDD Green: 最小实现，返回当前显示数据
        return {
            "current_job_id": self.current_job_id,
            "results_count": len(self.current_results),
            "comparison_data": self.get_comparison_data(),
            "has_edit_history": len(self.edit_history) > 0
        }
