"""
CloseAI related classes
"""

from httpx import AsyncClient
from tqdm.asyncio import tqdm
from time import time
from GalTransl import LOGGER
from GalTransl.ConfigHelper import CProjectConfig, CProxy
from typing import Optional, Tuple
from random import choice

TRANSLATOR_ENGINE = {
    "gpt35": "gpt-3.5-turbo",
    "gpt35-0613": "gpt-3.5-turbo-0613",
    "gpt35-1106": "gpt-3.5-turbo-1106",
    "gpt35-0125": "gpt-3.5-turbo-0125",
    "gpt4": "gpt-4",
    "gpt4-turbo": "gpt-4-0125-preview",
}


class COpenAIToken:
    """
    OpenAI 令牌
    """

    def __init__(self, token: str, domain: str, gpt3: bool, gpt4: bool) -> None:
        self.token: str = token
        # it's domain, not endpoint address..
        self.domain: str = domain
        self.isGPT35Available: bool = gpt3
        self.isGPT4Available: bool = gpt4

    def maskToken(self) -> str:
        """
        返回脱敏后的 sk-
        """
        return self.token[:6] + "*" * 17


def initGPTToken(config: CProjectConfig, eng_type: str) -> Optional[list[COpenAIToken]]:
    """
    处理 GPT Token 设置项
    """
    result: list[dict] = []
    degradeBackend: bool = False

    if val := config.getKey("gpt.degradeBackend"):
        degradeBackend = val

    defaultEndpoint = config.getBackendConfigSection("GPT35")["defaultEndpoint"]
    gpt35_tokens = config.getBackendConfigSection("GPT35").get("tokens")
    if "gpt35" in eng_type and gpt35_tokens:
        for tokenEntry in gpt35_tokens:
            token = tokenEntry["token"]
            domain = (
                tokenEntry["endpoint"]
                if tokenEntry.get("endpoint")
                else defaultEndpoint
            )
            domain = domain[:-1] if domain.endswith("/") else domain
            result.append(COpenAIToken(token, domain, True, False))
            pass

        if not degradeBackend:
            return result

    defaultEndpoint = config.getBackendConfigSection("GPT4")["defaultEndpoint"]
    if gpt4_tokens := config.getBackendConfigSection("GPT4").get("tokens"):
        for tokenEntry in gpt4_tokens:
            token = tokenEntry["token"]
            domain = (
                tokenEntry["endpoint"]
                if tokenEntry.get("endpoint")
                else defaultEndpoint
            )
            domain = domain[:-1] if domain.endswith("/") else domain
            result.append(
                COpenAIToken(token, domain, True if degradeBackend else False, True)
            )
            pass

    return result


class COpenAITokenPool:
    """
    OpenAI 令牌池
    """

    def __init__(self, config: CProjectConfig, eng_type: str) -> None:
        self.tokens: list[tuple[bool, COpenAIToken]] = []
        for token in initGPTToken(config, eng_type):
            self.tokens.append((False, token))
        if "gpt35" in eng_type:
            section = config.getBackendConfigSection("GPT35")
        elif "gpt4" in eng_type:
            section = config.getBackendConfigSection("GPT4")
        self.force_eng_name = section.get("rewriteModelName", "")

    async def _isTokenAvailable(
        self, token: COpenAIToken, proxy: CProxy = None, eng_type: str = ""
    ) -> Tuple[bool, bool, bool, COpenAIToken]:
        # returns isAvailable,isGPT3Available,isGPT4Available,token
        # todo: do not remove token directly, we can score the token
        try:
            st = time()
            async with AsyncClient(
                proxies={"https://": proxy.addr} if proxy else None
            ) as client:
                auth = {"Authorization": "Bearer " + token.token}
                model_name = TRANSLATOR_ENGINE.get(eng_type, "gpt-3.5-turbo")
                if self.force_eng_name:
                    model_name = self.force_eng_name
                # test if have balance
                api_address = token.domain + "/v1/chat/completions"
                if 'bigmodel' in api_address:
                    api_address = api_address.replace('v1', 'v4')
                if 'minimax' in api_address:
                    api_address = api_address.replace('/chat/completions', '/text/chatcompletion_v2')
                if 'ark.cn' in api_address:
                    api_address = api_address.replace('v1', 'v3')
                if 'google' in api_address:
                    api_address = api_address.replace('v1', 'v1beta/openai')
                chatResponse = await client.post(
                    api_address,
                    headers=auth,
                    json={
                        "model": model_name,
                        "messages": [{"role": "user", "content": "Echo OK"}],
                        "temperature": 0.7,
                    },
                    timeout=10,
                )
                if chatResponse.status_code != 200:
                    # token not available, may token has been revoked
                    return False, False, False, token
                else:
                    isGPT3Available = False
                    isGPT4Available = False

                    if "gpt-4" in model_name:
                        isGPT4Available = True
                    elif "gpt-3.5" in model_name:
                        isGPT3Available = True
                    else:
                        isGPT4Available, isGPT3Available = True, True

                    return True, isGPT3Available, isGPT4Available, token
        except:
            LOGGER.debug(
                "we got exception in testing OpenAI token %s", token.maskToken()
            )
            return False, False, False, token
        finally:
            et = time()
            LOGGER.debug("tested OpenAI token %s in %s", token.maskToken(), et - st)
            pass

    async def checkTokenAvailablity(
        self, proxy: CProxy = None, eng_type: str = ""
    ) -> None:
        """
        检测令牌有效性
        """
        model_name = TRANSLATOR_ENGINE.get(eng_type, "gpt-3.5-turbo")
        if self.force_eng_name:
            model_name = self.force_eng_name
        if model_name == "gpt-3.5-turbo-0125":
            raise RuntimeError("gpt-3.5-turbo-0125质量太差，请更换其他模型！")
        LOGGER.info(f"测试key是否能调用{model_name}模型...")
        fs = []
        for _, token in self.tokens:
            fs.append(self._isTokenAvailable(token, proxy if proxy else None, eng_type))
        result: list[tuple[bool, bool, bool, COpenAIToken]] = await tqdm.gather(
            *fs, ncols=80
        )

        # replace list with new one
        newList: list[tuple[bool, COpenAIToken]] = []
        for isAvailable, isGPT3Available, isGPT4Available, token in result:
            if isAvailable != True:
                LOGGER.warning(
                    "%s is not available for %s, will be removed",
                    token.maskToken(),
                    eng_type,
                )
            else:
                newList.append((True, token))

        self.tokens = newList

    def reportTokenProblem(self, token: COpenAIToken) -> None:
        """
        报告令牌无效
        """
        for id, tokenPair in enumerate(self.tokens):
            if tokenPair[1] == token:
                self.tokens.pop(id)
            pass
        pass

    def getToken(self, needGPT3: bool, needGPT4: bool) -> COpenAIToken:
        """
        获取一个有效的 token
        """
        rounds: int = 0
        while True:
            if rounds > 20:
                raise RuntimeError(
                    "COpenAITokenPool::getToken: 可用的OpenAI token耗尽！"
                )
            try:
                available, token = choice(self.tokens)
                if not available:
                    continue
                if needGPT3 and token.isGPT35Available:
                    return token
                if needGPT4 and token.isGPT4Available:
                    return token
                rounds += 1
            except IndexError:
                raise RuntimeError("没有可用的 OpenAI token！")
