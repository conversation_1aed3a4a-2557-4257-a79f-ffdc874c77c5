#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件上传UI模块

TDD-Red阶段：先写测试用例，测试各种UI交互功能
包含文件拖拽区域测试和上传组件测试
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import os
from typing import List

# 导入待测试的模块
from voice_came.ui.file_drop_area import FileDropArea, DropAreaState, FileDropAreaConfig
from voice_came.ui.upload_widget import UploadWidget


class TestFileDropArea:
    """测试文件拖拽区域组件"""
    
    @pytest.fixture
    def drop_area(self):
        """创建测试用的拖拽区域实例"""
        return FileDropArea()
    
    @pytest.fixture
    def drop_area_with_config(self):
        """创建带自定义配置的拖拽区域实例"""
        config = FileDropAreaConfig()
        config.max_files_per_drop = 10
        return FileDropArea(config)
    
    @pytest.fixture
    def sample_files(self):
        """创建测试文件"""
        temp_dir = tempfile.mkdtemp()
        files = []
        
        # 创建不同类型的测试文件
        for i, (name, content) in enumerate([
            ("test_video.mp4", b"fake video content"),
            ("test_audio.wav", b"fake audio content"),
            ("document.txt", b"text content")
        ]):
            file_path = Path(temp_dir) / name
            file_path.write_bytes(content)
            files.append(file_path)
        
        yield files
        
        # 清理文件
        for file_path in files:
            try:
                file_path.unlink()
            except:
                pass
        try:
            os.rmdir(temp_dir)
        except:
            pass
    
    def test_drag_enter_highlights_drop_area(self, drop_area):
        """测试拖拽进入时高亮显示拖拽区域"""
        # 模拟拖拽进入事件
        mock_event = Mock()
        mock_event.mimeData().hasUrls.return_value = True
        
        drop_area.dragEnterEvent(mock_event)
        
        # 验证区域状态变为高亮
        assert drop_area.state == DropAreaState.HIGHLIGHTED
        assert drop_area.drag_in_progress is True
        assert drop_area.get_css_class() == "drop-area-highlighted"
        
        # 验证事件被接受
        mock_event.accept.assert_called_once()
    
    def test_drag_leave_removes_highlight(self, drop_area):
        """测试拖拽离开时移除高亮"""
        # 先设置高亮状态
        drop_area.state = DropAreaState.HIGHLIGHTED
        drop_area.drag_in_progress = True
        
        # 模拟拖拽离开事件  
        mock_event = Mock()
        drop_area.dragLeaveEvent(mock_event)
        
        # 验证高亮被移除
        assert drop_area.state == DropAreaState.NORMAL
        assert drop_area.drag_in_progress is False
        assert drop_area.get_css_class() == "drop-area-normal"
    
    def test_drag_over_maintains_highlight(self, drop_area):
        """测试拖拽悬停时保持高亮状态"""
        # 先进入高亮状态
        drop_area.state = DropAreaState.HIGHLIGHTED
        
        mock_event = Mock()
        mock_event.mimeData().hasUrls.return_value = True
        
        drop_area.dragMoveEvent(mock_event)
        
        # 验证状态切换为拖拽中
        assert drop_area.state == DropAreaState.DRAGGING
        mock_event.accept.assert_called_once()
    
    def test_drag_enter_rejects_non_file_data(self, drop_area):
        """测试拖拽非文件数据时拒绝"""
        mock_event = Mock()
        mock_event.mimeData().hasUrls.return_value = False
        
        drop_area.dragEnterEvent(mock_event)
        
        # 验证区域状态保持正常
        assert drop_area.state == DropAreaState.NORMAL
        # 验证事件被拒绝
        mock_event.ignore.assert_called_once()
    
    def test_drop_single_file_success(self, drop_area, sample_files):
        """测试拖拽单个文件成功"""
        mock_event = Mock()
        mock_url = Mock()
        mock_url.toLocalFile.return_value = str(sample_files[0])
        mock_event.mimeData().urls.return_value = [mock_url]
        
        # 模拟文件拖拽事件
        drop_area.dropEvent(mock_event)
        
        # 验证文件被处理
        assert len(drop_area.dropped_files) == 1
        assert str(sample_files[0]) in drop_area.dropped_files
        
        # 验证状态恢复正常
        assert drop_area.state == DropAreaState.NORMAL
        assert drop_area.drag_in_progress is False
        
        # 验证事件被接受
        mock_event.accept.assert_called_once()
    
    def test_drop_multiple_files_success(self, drop_area, sample_files):
        """测试拖拽多个文件成功"""
        mock_event = Mock()
        mock_urls = []
        for file_path in sample_files:
            mock_url = Mock()
            mock_url.toLocalFile.return_value = str(file_path)
            mock_urls.append(mock_url)
        
        mock_event.mimeData().urls.return_value = mock_urls
        
        drop_area.dropEvent(mock_event)
        
        # 验证所有文件都被处理
        assert len(drop_area.dropped_files) == len(sample_files)
        for file_path in sample_files:
            assert str(file_path) in drop_area.dropped_files
        
        # 验证性能指标
        metrics = drop_area.get_metrics()
        assert metrics["successful_drops"] == 1
        assert metrics["total_files_processed"] == len(sample_files)
    
    def test_drop_filters_invalid_paths(self, drop_area):
        """测试拖拽时过滤无效路径"""
        mock_event = Mock()
        
        # 创建包含无效路径的URL列表
        mock_urls = []
        for path in ["/non/existent/file.mp4", "/another/invalid/path.avi"]:
            mock_url = Mock()
            mock_url.toLocalFile.return_value = path
            mock_urls.append(mock_url)
        
        mock_event.mimeData().urls.return_value = mock_urls
        
        drop_area.dropEvent(mock_event)
        
        # 验证无效文件被过滤
        assert len(drop_area.dropped_files) == 0
        
        # 验证性能指标
        metrics = drop_area.get_metrics()
        assert metrics["failed_drops"] == 1
    
    def test_visual_feedback_during_drag(self, drop_area):
        """测试拖拽过程中的视觉反馈"""
        # 拖拽进入
        mock_enter_event = Mock()
        mock_enter_event.mimeData().hasUrls.return_value = True
        drop_area.dragEnterEvent(mock_enter_event)
        
        # 检查视觉状态变化
        assert drop_area.get_css_class() == "drop-area-highlighted"
        assert drop_area.get_border_style() == "dashed"
        assert drop_area.get_border_color() == "#007bff"
        assert drop_area.get_background_color() == "#f0f8ff"
        
        # 拖拽移动
        mock_move_event = Mock()
        mock_move_event.mimeData().hasUrls.return_value = True
        drop_area.dragMoveEvent(mock_move_event)
        
        # 检查拖拽中状态
        assert drop_area.get_css_class() == "drop-area-dragging"
        assert drop_area.get_border_color() == "#28a745"
        assert drop_area.get_background_color() == "#f8fff9"
    
    def test_drag_feedback_with_animation(self, drop_area):
        """测试拖拽反馈动画"""
        # 测试动画高亮功能
        with patch.object(drop_area, 'animate_highlight') as mock_animate:
            mock_event = Mock()
            mock_event.mimeData().hasUrls.return_value = True
            
            drop_area.dragEnterEvent(mock_event)
            
            # 验证动画被触发
            mock_animate.assert_called_with(True)
    
    def test_drag_cancel_restores_state(self, drop_area):
        """测试拖拽取消后恢复原始状态"""
        # 模拟拖拽开始
        mock_enter_event = Mock()
        mock_enter_event.mimeData().hasUrls.return_value = True
        drop_area.dragEnterEvent(mock_enter_event)
        
        original_files = drop_area.dropped_files.copy()
        
        # 模拟拖拽取消（直接离开而不drop）
        mock_leave_event = Mock()
        drop_area.dragLeaveEvent(mock_leave_event)
        
        # 验证状态恢复
        assert drop_area.state == DropAreaState.NORMAL
        assert drop_area.drag_in_progress is False
        assert drop_area.get_css_class() == "drop-area-normal"
        
        # 验证文件列表未改变
        assert drop_area.dropped_files == original_files
    
    def test_ui_responsiveness_during_large_file_drop(self, drop_area):
        """测试处理大文件时的UI响应性"""
        # 这个测试确保在处理大文件时UI不会冻结
        # 实际应用中可能需要异步处理或进度条
        
        mock_event = Mock()
        mock_url = Mock()
        mock_url.toLocalFile.return_value = "/path/to/large/file.mp4"
        mock_event.mimeData().urls.return_value = [mock_url]
        
        # 模拟大文件处理
        with patch('pathlib.Path.exists', return_value=True):
            with patch('pathlib.Path.is_file', return_value=True):
                drop_area.dropEvent(mock_event)
        
        # 验证状态正确处理
        assert drop_area.state == DropAreaState.NORMAL
        assert len(drop_area.dropped_files) == 1
    
    def test_concurrent_drag_operations(self, drop_area):
        """测试并发拖拽操作处理"""
        # 这个测试确保同时多个拖拽操作不会造成状态混乱
        
        # 模拟第一个拖拽开始
        mock_event1 = Mock()
        mock_event1.mimeData().hasUrls.return_value = True
        drop_area.dragEnterEvent(mock_event1)
        
        # 模拟第二个拖拽（应该被忽略或正确处理）
        mock_event2 = Mock()
        mock_event2.mimeData().hasUrls.return_value = True
        drop_area.dragEnterEvent(mock_event2)
        
        # 验证状态一致性
        assert drop_area.state == DropAreaState.HIGHLIGHTED
        assert drop_area.drag_in_progress is True
    
    def test_error_handling_and_recovery(self, drop_area):
        """测试错误处理和状态恢复"""
        # 添加错误回调
        error_messages = []
        drop_area.add_error_callback(lambda msg: error_messages.append(msg))
        
        # 模拟错误条件
        mock_event = Mock()
        mock_event.mimeData().hasUrls.return_value = False
        
        drop_area.dragEnterEvent(mock_event)
        
        # 验证错误被记录
        assert len(error_messages) > 0
        assert "不支持的拖拽内容类型" in error_messages[0]
        
        # 验证状态恢复
        assert drop_area.state == DropAreaState.NORMAL
    
    def test_file_limit_enforcement(self, drop_area_with_config):
        """测试文件数量限制"""
        drop_area = drop_area_with_config
        
        # 创建超过限制的文件列表
        mock_event = Mock()
        mock_urls = []
        for i in range(15):  # 超过配置的10个文件限制
            mock_url = Mock()
            mock_url.toLocalFile.return_value = f"/path/to/file_{i}.mp4"
            mock_urls.append(mock_url)
        
        mock_event.mimeData().urls.return_value = mock_urls
        
        drop_area.dropEvent(mock_event)
        
        # 验证超限请求被拒绝
        assert len(drop_area.dropped_files) == 0
        mock_event.ignore.assert_called_once()
        
        # 验证错误被记录
        assert "一次最多只能拖拽 10 个文件" in drop_area.last_error_message
    
    def test_state_change_callbacks(self, drop_area):
        """测试状态变化回调"""
        state_changes = []
        
        def track_state_change(old_state, new_state):
            state_changes.append((old_state, new_state))
        
        drop_area.add_state_change_callback(track_state_change)
        
        # 触发状态变化
        mock_event = Mock()
        mock_event.mimeData().hasUrls.return_value = True
        drop_area.dragEnterEvent(mock_event)
        
        # 验证回调被触发
        assert len(state_changes) > 0
        assert state_changes[0] == (DropAreaState.NORMAL, DropAreaState.HIGHLIGHTED)
    
    def test_metrics_tracking(self, drop_area, sample_files):
        """测试性能指标跟踪"""
        # 重置指标
        drop_area.reset_metrics()
        
        # 执行成功的拖拽操作
        mock_event = Mock()
        mock_url = Mock()
        mock_url.toLocalFile.return_value = str(sample_files[0])
        mock_event.mimeData().urls.return_value = [mock_url]
        
        drop_area.dropEvent(mock_event)
        
        # 验证指标
        metrics = drop_area.get_metrics()
        assert metrics["total_drops"] == 1
        assert metrics["successful_drops"] == 1
        assert metrics["failed_drops"] == 0
        assert metrics["total_files_processed"] == 1
        assert metrics["success_rate"] == 100.0
        assert metrics["average_processing_time"] >= 0  # 处理时间可能为0（很快）
    
    def test_manual_file_addition(self, drop_area, sample_files):
        """测试手动添加文件"""
        # 添加有效文件
        result = drop_area.add_file(str(sample_files[0]))
        assert result is True
        assert str(sample_files[0]) in drop_area.dropped_files
        
        # 尝试添加重复文件
        result = drop_area.add_file(str(sample_files[0]))
        assert result is False
        assert len(drop_area.dropped_files) == 1
        
        # 尝试添加不存在的文件
        result = drop_area.add_file("/non/existent/file.mp4")
        assert result is False
        assert len(drop_area.dropped_files) == 1
    
    def test_availability_check(self, drop_area):
        """测试可用性检查"""
        # 正常状态应该可用
        assert drop_area.is_available() is True
        
        # 错误状态不可用
        drop_area.state = DropAreaState.ERROR
        assert drop_area.is_available() is False
        
        # 处理状态不可用
        drop_area.state = DropAreaState.PROCESSING
        assert drop_area.is_available() is False
    
    def test_state_info_retrieval(self, drop_area):
        """测试状态信息获取"""
        info = drop_area.get_state_info()
        
        assert "state" in info
        assert "previous_state" in info
        assert "drag_in_progress" in info
        assert "file_count" in info
        assert "last_error" in info
        assert "error_timestamp" in info
        
        assert info["state"] == "normal"
        assert info["file_count"] == 0


class TestUploadWidget:
    """测试上传组件"""
    
    @pytest.fixture
    def upload_widget(self):
        """创建测试用的上传组件实例"""
        return UploadWidget()
    
    @pytest.fixture
    def sample_files(self):
        """创建测试文件"""
        temp_dir = tempfile.mkdtemp()
        files = []
        
        for i, (name, content) in enumerate([
            ("video1.mp4", b"video content 1"),
            ("video2.avi", b"video content 2"),
            ("audio.wav", b"audio content")
        ]):
            file_path = Path(temp_dir) / name
            file_path.write_bytes(content)
            files.append(file_path)
        
        yield files
        
        # 清理
        for file_path in files:
            try:
                file_path.unlink()
            except:
                pass
        try:
            os.rmdir(temp_dir)
        except:
            pass
    
    def test_upload_widget_initialization(self, upload_widget):
        """测试上传组件初始化"""
        assert upload_widget.files == []
        assert upload_widget.drop_area is not None
        assert upload_widget.file_list is not None
        assert upload_widget.upload_button is not None
        assert upload_widget.clear_button is not None
        assert upload_widget.progress_bar is not None
    
    def test_widget_layout_structure(self, upload_widget):
        """测试组件布局结构"""
        # 验证组件层次结构
        assert hasattr(upload_widget, 'drop_area')
        assert hasattr(upload_widget, 'file_list')
        assert hasattr(upload_widget, 'button_layout')
        assert hasattr(upload_widget, 'progress_bar')
        
        # 验证布局配置
        assert upload_widget.layout is not None
    
    def test_add_files_from_drop_area(self, upload_widget, sample_files):
        """测试从拖拽区域添加文件"""
        # 模拟从拖拽区域添加文件
        for file_path in sample_files:
            upload_widget.add_file_from_drop(str(file_path))
        
        # 验证文件被添加
        assert len(upload_widget.files) == len(sample_files)
        for file_path in sample_files:
            assert str(file_path) in [f.file_path for f in upload_widget.files]
    
    def test_add_multiple_files_from_drop(self, upload_widget, sample_files):
        """测试一次性添加多个文件"""
        file_paths = [str(f) for f in sample_files]
        
        # 模拟批量添加
        for path in file_paths:
            upload_widget.add_file_from_drop(path)
        
        # 验证所有文件都被添加
        assert len(upload_widget.files) == len(sample_files)
        
        # 验证文件列表UI更新
        assert upload_widget.file_list.item_count == len(sample_files)
    
    def test_duplicate_file_handling(self, upload_widget, sample_files):
        """测试重复文件处理"""
        file_path = str(sample_files[0])
        
        # 添加文件两次
        upload_widget.add_file_from_drop(file_path)
        upload_widget.add_file_from_drop(file_path)
        
        # 验证只添加了一个文件
        assert len(upload_widget.files) == 1
        assert upload_widget.files[0].file_path == file_path
    
    def test_file_validation_on_add(self, upload_widget, sample_files):
        """测试添加文件时的验证"""
        # 添加有效文件
        valid_file = str(sample_files[0])
        upload_widget.add_file_from_drop(valid_file)
        assert len(upload_widget.files) == 1
        
        # 尝试添加无效文件（不存在）
        invalid_file = "/non/existent/file.mp4"
        upload_widget.add_file_from_drop(invalid_file)
        assert len(upload_widget.files) == 1  # 应该保持不变
    
    def test_invalid_file_rejection(self, upload_widget):
        """测试拒绝无效文件"""
        # 测试各种无效文件场景
        invalid_files = [
            "/non/existent/file.mp4",
            "",
            "/path/to/directory/",
            "invalid_extension.xyz"
        ]
        
        for invalid_file in invalid_files:
            initial_count = len(upload_widget.files)
            upload_widget.add_file_from_drop(invalid_file)
            assert len(upload_widget.files) == initial_count
    
    def test_remove_single_file(self, upload_widget, sample_files):
        """测试移除单个文件"""
        # 先添加文件
        for file_path in sample_files:
            upload_widget.add_file_from_drop(str(file_path))
        
        initial_count = len(upload_widget.files)
        
        # 移除第一个文件
        upload_widget.remove_file(0)
        
        # 验证文件被移除
        assert len(upload_widget.files) == initial_count - 1
        assert upload_widget.file_list.item_count == initial_count - 1
    
    def test_clear_all_files(self, upload_widget, sample_files):
        """测试清空所有文件"""
        # 先添加文件
        for file_path in sample_files:
            upload_widget.add_file_from_drop(str(file_path))
        
        # 清空所有文件
        upload_widget.clear_all_files()
        
        # 验证所有文件被清空
        assert len(upload_widget.files) == 0
        assert upload_widget.file_list.item_count == 0
    
    def test_upload_button_state_management(self, upload_widget, sample_files):
        """测试上传按钮状态管理"""
        # 初始状态：没有文件，按钮应该禁用
        assert upload_widget.upload_button.is_enabled() is False
        
        # 添加文件后，按钮应该启用
        upload_widget.add_file_from_drop(str(sample_files[0]))
        assert upload_widget.upload_button.is_enabled() is True
        
        # 清空文件后，按钮应该禁用
        upload_widget.clear_all_files()
        assert upload_widget.upload_button.is_enabled() is False