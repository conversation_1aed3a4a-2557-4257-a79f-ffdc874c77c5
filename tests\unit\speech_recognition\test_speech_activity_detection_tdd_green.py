#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音活动检测(SAD)测试用例 - TDD Green阶段

Task 3.5: 语音活动检测最小实现
状态：TDD-Green阶段（实现最小功能，确保所有测试通过）

测试覆盖：
1. 语音片段识别准确性验证
2. 静音区间检测验证
3. 时间戳精度验证
4. 配置参数验证
5. 基础功能验证
"""

import os
import pytest
import tempfile
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple

from voice_came.speech_recognition.speech_activity_detector import SpeechActivityDetector
from voice_came.speech_recognition.whisperx_engine import WhisperXConfig


class TestSpeechActivityDetectionGreen:
    """语音活动检测测试类 - TDD Green阶段"""
    
    @pytest.fixture
    def sad_config(self):
        """SAD测试配置"""
        return WhisperXConfig(
            model_name="tiny",
            device="cpu",
            vad_threshold=0.5,
            vad_min_speech_duration_ms=250,
            vad_max_silence_duration_ms=2000,
            asmr_mode=True
        )
    
    @pytest.fixture
    def mock_audio_samples(self):
        """模拟音频样本数据"""
        return {
            "speech_only": np.random.randn(16000 * 5),  # 5秒纯语音
            "silence_only": np.zeros(16000 * 3),        # 3秒静音
            "speech_with_silence": np.concatenate([
                np.random.randn(16000 * 2),  # 2秒语音
                np.zeros(16000 * 1),         # 1秒静音
                np.random.randn(16000 * 2)   # 2秒语音
            ]),
            "long_audio": np.random.randn(16000 * 300),  # 5分钟长音频
            "noisy_speech": np.random.randn(16000 * 4),  # 4秒含噪语音
            "very_short": np.random.randn(int(16000 * 0.1)),  # 0.1秒极短音频
            "asmr_whisper": np.random.randn(16000 * 10) * 0.1  # 10秒ASMR低音量
        }

    # =================================================================
    # 1. 基础功能验证测试
    # =================================================================
    
    def test_detector_initialization(self, sad_config):
        """测试检测器初始化"""
        detector = SpeechActivityDetector(sad_config)
        
        assert detector.config == sad_config
        assert detector.vad_threshold == sad_config.vad_threshold
        assert detector.sample_rate == 16000
    
    def test_detect_speech_segments_basic(self, sad_config, mock_audio_samples):
        """测试基础语音片段检测"""
        detector = SpeechActivityDetector(sad_config)
        segments = detector.detect_speech_segments(mock_audio_samples["speech_with_silence"])
        
        # 验证基本结构
        assert isinstance(segments, list)
        assert len(segments) == 2  # 期望检测到2个语音片段
        
        # 验证时间戳结构
        for segment in segments:
            assert "start" in segment
            assert "end" in segment
            assert isinstance(segment["start"], float)
            assert isinstance(segment["end"], float)
            assert segment["end"] > segment["start"]
    
    def test_detect_speech_segments_accuracy(self, sad_config, mock_audio_samples):
        """测试语音片段识别的准确性"""
        detector = SpeechActivityDetector(sad_config)
        segments = detector.detect_speech_segments(mock_audio_samples["speech_with_silence"])
        
        # 期望检测到2个语音片段
        assert len(segments) == 2
        assert segments[0]["start"] == pytest.approx(0.0, abs=0.1)
        assert segments[0]["end"] == pytest.approx(2.0, abs=0.1)
        assert segments[1]["start"] == pytest.approx(3.0, abs=0.1)
        assert segments[1]["end"] == pytest.approx(5.0, abs=0.1)

    def test_detect_speech_confidence_scores(self, sad_config, mock_audio_samples):
        """测试语音检测的置信度分数"""
        detector = SpeechActivityDetector(sad_config)
        segments = detector.detect_speech_segments(
            mock_audio_samples["speech_only"], 
            return_confidence=True
        )
        
        # 期望每个片段都有置信度分数
        assert len(segments) > 0
        for segment in segments:
            assert "confidence" in segment
            assert 0.0 <= segment["confidence"] <= 1.0

    def test_detect_speech_word_level_alignment(self, sad_config, mock_audio_samples):
        """测试词级别的语音对齐"""
        detector = SpeechActivityDetector(sad_config)
        segments = detector.detect_speech_segments(
            mock_audio_samples["speech_only"],
            word_level=True
        )
        
        # 期望有词级别的时间戳
        assert len(segments) > 0
        for segment in segments:
            assert "words" in segment
            assert isinstance(segment["words"], list)
            
            for word in segment["words"]:
                assert "start" in word
                assert "end" in word
                assert "word" in word

    # =================================================================
    # 2. 静音区间检测测试
    # =================================================================
    
    def test_detect_silence_intervals(self, sad_config, mock_audio_samples):
        """测试静音区间检测"""
        detector = SpeechActivityDetector(sad_config)
        silence_intervals = detector.detect_silence_intervals(
            mock_audio_samples["speech_with_silence"]
        )
        
        # 期望检测到静音区间
        assert len(silence_intervals) >= 1
        silence = silence_intervals[0]
        assert silence["start"] == pytest.approx(2.0, abs=0.1)
        assert silence["end"] == pytest.approx(3.0, abs=0.1)

    def test_silence_duration_filtering(self, sad_config, mock_audio_samples):
        """测试静音持续时间过滤"""
        detector = SpeechActivityDetector(sad_config)
        
        # 设置最小静音持续时间为500ms
        silence_intervals = detector.detect_silence_intervals(
            mock_audio_samples["speech_with_silence"],
            min_silence_duration_ms=500
        )
        
        # 期望过滤掉短静音
        for silence in silence_intervals:
            duration = silence["end"] - silence["start"]
            assert duration >= 0.5  # 至少500ms

    def test_pure_silence_detection(self, sad_config, mock_audio_samples):
        """测试纯静音音频检测"""
        detector = SpeechActivityDetector(sad_config)
        segments = detector.detect_speech_segments(mock_audio_samples["silence_only"])
        
        # 期望纯静音音频不检测到语音片段
        assert len(segments) == 0

    # =================================================================
    # 3. 时间戳精度测试
    # =================================================================
    
    def test_timestamp_precision_millisecond(self, sad_config, mock_audio_samples):
        """测试毫秒级时间戳精度"""
        detector = SpeechActivityDetector(sad_config)
        segments = detector.detect_speech_segments(
            mock_audio_samples["speech_only"],
            precision="millisecond"
        )
        
        # 期望时间戳精确到毫秒
        assert len(segments) > 0
        for segment in segments:
            assert isinstance(segment["start"], float)
            assert isinstance(segment["end"], float)
            # 验证精度为3位小数（毫秒级）
            start_decimal = str(segment["start"]).split(".")
            end_decimal = str(segment["end"]).split(".")
            if len(start_decimal) > 1:
                assert len(start_decimal[1]) <= 3
            if len(end_decimal) > 1:
                assert len(end_decimal[1]) <= 3

    # =================================================================
    # 4. 配置和边界条件测试
    # =================================================================
    
    def test_empty_audio_input(self, sad_config):
        """测试空音频输入"""
        detector = SpeechActivityDetector(sad_config)
        segments = detector.detect_speech_segments(np.array([]))
        
        # 期望空输入返回空结果
        assert len(segments) == 0

    def test_invalid_audio_format(self, sad_config):
        """测试无效音频格式"""
        detector = SpeechActivityDetector(sad_config)
        
        # 测试无效输入类型
        with pytest.raises(ValueError):
            detector.detect_speech_segments("invalid_input")

    def test_asmr_low_volume_detection(self, sad_config, mock_audio_samples):
        """测试ASMR低音量检测"""
        # 配置ASMR模式
        asmr_config = WhisperXConfig(
            model_name="tiny",
            device="cpu",
            vad_threshold=0.3,  # 降低阈值
            vad_min_speech_duration_ms=100,
            asmr_mode=True
        )
        
        detector = SpeechActivityDetector(asmr_config)
        segments = detector.detect_speech_segments(mock_audio_samples["asmr_whisper"])
        
        # 期望能检测到低音量ASMR内容
        assert len(segments) > 0

    def test_configuration_validation(self):
        """测试配置验证"""
        # 测试无效配置
        invalid_config = WhisperXConfig(
            vad_threshold=-1.0,  # 无效阈值
            vad_min_speech_duration_ms=-100  # 无效持续时间
        )
        
        with pytest.raises(ValueError):
            SpeechActivityDetector(invalid_config)
    
    def test_snr_calculation(self, sad_config, mock_audio_samples):
        """测试信噪比计算"""
        detector = SpeechActivityDetector(sad_config)
        segments = detector.detect_speech_segments(
            mock_audio_samples["noisy_speech"],
            min_snr_db=10  # 最小信噪比10dB
        )
        
        # 验证SNR字段存在且合理
        for segment in segments:
            if "snr" in segment:
                assert isinstance(segment["snr"], (int, float))
                assert segment["snr"] >= 10

    # =================================================================
    # 5. 高级功能测试
    # =================================================================
    
    def test_additional_parameters(self, sad_config, mock_audio_samples):
        """测试新增参数支持"""
        detector = SpeechActivityDetector(sad_config)
        
        # 测试额外参数不会报错
        segments = detector.detect_speech_segments(
            mock_audio_samples["speech_only"],
            min_segment_duration_ms=100,
            chunk_length_seconds=30
        )
        
        assert isinstance(segments, list)

    def test_concurrent_detection_safe(self, sad_config, mock_audio_samples):
        """测试并发检测安全性"""
        import threading
        
        detector = SpeechActivityDetector(sad_config)
        results = []
        
        def detect_worker(audio_data):
            segments = detector.detect_speech_segments(audio_data)
            results.append(len(segments))
        
        # 创建多个并发线程
        threads = []
        for i in range(3):
            thread = threading.Thread(
                target=detect_worker,
                args=(mock_audio_samples["speech_only"],)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 期望所有请求都能正常处理
        assert len(results) == 3
        assert all(isinstance(r, int) and r >= 0 for r in results) 