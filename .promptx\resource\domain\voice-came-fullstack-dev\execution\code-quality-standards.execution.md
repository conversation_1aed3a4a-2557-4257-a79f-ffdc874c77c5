<execution>
  <constraint>
    ## 代码质量的客观技术限制
    - **Python语言特性**：受Python语言本身的性能和类型系统限制
    - **第三方库依赖**：WhisperX、torch等库的API稳定性和版本兼容性
    - **静态分析工具限制**：mypy、flake8等工具的检查能力边界
    - **测试框架约束**：pytest框架的功能范围和性能特性
    - **CI/CD环境限制**：自动化检查的执行时间和资源消耗
    - **跨平台差异**：不同操作系统下的代码行为一致性挑战
  </constraint>

  <rule>
    ## 代码质量强制执行规则
    - **代码格式化强制**：所有代码必须通过black格式化，无例外
    - **类型注解强制**：所有公共函数和方法必须有完整的类型注解
    - **文档字符串强制**：所有公共类、函数必须有docstring，遵循Google风格
    - **测试覆盖率强制**：新增代码测试覆盖率必须≥90%，不得降低整体覆盖率
    - **代码审查强制**：所有代码变更必须经过至少一人的代码审查
    - **静态检查强制**：必须通过flake8、mypy、bandit安全检查
    - **命名规范强制**：严格遵循PEP 8命名规范，不得使用缩写或拼音
    - **复杂度控制强制**：函数圈复杂度≤10，类复杂度≤20
  </rule>

  <guideline>
    ## 代码质量指导原则
    - **可读性优先**：代码应该像文档一样易读，优先选择清晰的表达方式
    - **简洁性原则**：避免过度设计，优先选择简单直接的解决方案
    - **一致性维护**：在整个项目中保持编码风格和模式的一致性
    - **性能意识**：在保证可读性的前提下，考虑代码的性能影响
    - **错误处理完整**：预期所有可能的错误情况，提供合适的处理机制
    - **测试友好设计**：编写易于测试的代码，避免紧耦合和硬依赖
    - **文档同步**：代码变更时同步更新相关文档和注释
  </guideline>

  <process>
    ## 代码质量保证执行流程

    ### Phase 1: 开发前准备
    ```
    1.1 开发环境配置
    - 安装并配置代码质量工具链
      * black (代码格式化)
      * isort (导入排序)
      * flake8 (代码风格检查)
      * mypy (类型检查)
      * bandit (安全检查)
      * pytest (测试框架)
      * pytest-cov (覆盖率统计)
    
    1.2 IDE/编辑器配置
    - 配置自动格式化（保存时运行black）
    - 配置实时类型检查（mypy集成）
    - 配置代码提示和自动补全
    - 配置Git hooks（pre-commit检查）
    
    1.3 项目配置文件设置
    - pyproject.toml: black、isort配置
    - setup.cfg: flake8、mypy配置
    - .pre-commit-config.yaml: Git hooks配置
    - pytest.ini: 测试配置
    ```

    ### Phase 2: 编码规范执行
    ```
    2.1 代码编写规范
    
    命名规范：
    - 类名：PascalCase (WhisperXEngine)
    - 函数/变量：snake_case (process_audio_file)
    - 常量：UPPER_SNAKE_CASE (MAX_BATCH_SIZE)
    - 私有成员：_leading_underscore (_internal_method)
    - 特殊方法：__double_underscore__ (__init__)
    
    类型注解规范：
    ```python
    from typing import Dict, List, Optional, Union, Callable
    from pathlib import Path
    
    def process_batch_videos(
        video_paths: List[Path],
        output_dir: Path,
        config: Dict[str, Any],
        callback: Optional[Callable[[str], None]] = None
    ) -> Dict[str, ProcessResult]:
        """处理批量视频文件"""
        pass
    ```
    
    文档字符串规范（Google风格）：
    ```python
    def translate_text(
        text: str, 
        source_lang: str, 
        target_lang: str,
        use_terminology: bool = True
    ) -> TranslationResult:
        """翻译文本内容
        
        Args:
            text: 待翻译的源文本
            source_lang: 源语言代码（如'zh'）
            target_lang: 目标语言代码（如'en'）
            use_terminology: 是否使用术语词典
            
        Returns:
            TranslationResult: 包含翻译结果和质量评分的对象
            
        Raises:
            ValueError: 当语言代码不支持时
            TranslationError: 当翻译过程失败时
            
        Example:
            >>> result = translate_text("你好", "zh", "en")
            >>> print(result.translated_text)
            "Hello"
        """
        pass
    ```
    
    2.2 代码结构规范
    
    文件结构模板：
    ```python
    #!/usr/bin/env python3
    # -*- coding: utf-8 -*-
    """模块功能描述
    
    详细的模块说明，包括主要功能、使用场景等
    """
    
    # 标准库导入
    import os
    import sys
    from pathlib import Path
    
    # 第三方库导入
    import torch
    import numpy as np
    
    # 本地模块导入
    from voice_came.core.base_engine import BaseEngine
    from voice_came.utils.logger import get_logger
    
    # 模块级常量
    DEFAULT_MODEL_PATH = Path("models/whisperx")
    MAX_AUDIO_LENGTH = 3600 * 12  # 12小时
    
    # 模块级变量
    logger = get_logger(__name__)
    
    # 类和函数定义
    class WhisperXEngine(BaseEngine):
        """WhisperX语音识别引擎"""
        pass
    ```
    
    2.3 错误处理规范
    ```python
    # 自定义异常类
    class VoiceCameError(Exception):
        """Voice-came基础异常类"""
        pass
    
    class WhisperXError(VoiceCameError):
        """WhisperX相关异常"""
        pass
    
    # 错误处理模式
    def process_audio_file(audio_path: Path) -> ProcessResult:
        """处理音频文件"""
        try:
            # 参数验证
            if not audio_path.exists():
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
            if not audio_path.suffix.lower() in SUPPORTED_FORMATS:
                raise ValueError(f"不支持的音频格式: {audio_path.suffix}")
            
            # 核心处理逻辑
            result = self._process_internal(audio_path)
            return result
            
        except FileNotFoundError as e:
            logger.error(f"文件错误: {e}")
            raise
        except ValueError as e:
            logger.error(f"参数错误: {e}")
            raise
        except Exception as e:
            logger.error(f"处理音频文件时发生未知错误: {e}")
            raise WhisperXError(f"音频处理失败: {e}") from e
    ```
    ```

    ### Phase 3: 测试驱动开发
    ```
    3.1 测试用例编写规范
    
    测试文件命名：test_[模块名].py
    测试类命名：Test[类名]
    测试方法命名：test_[功能描述]_[预期结果]
    
    ```python
    import pytest
    from unittest.mock import Mock, patch
    from pathlib import Path
    
    from voice_came.engines.whisperx_engine import WhisperXEngine
    from voice_came.exceptions import WhisperXError
    
    class TestWhisperXEngine:
        """WhisperX引擎测试类"""
        
        @pytest.fixture
        def engine(self):
            """测试引擎实例"""
            return WhisperXEngine(model_path="test_model", device="cpu")
        
        @pytest.fixture
        def sample_audio_path(self, tmp_path):
            """示例音频文件路径"""
            audio_file = tmp_path / "sample.wav"
            audio_file.write_bytes(b"fake audio data")
            return audio_file
        
        def test_process_audio_file_success(self, engine, sample_audio_path):
            """测试音频文件处理成功场景"""
            # Arrange
            expected_result = {"text": "hello world", "confidence": 0.95}
            
            with patch.object(engine, '_process_internal') as mock_process:
                mock_process.return_value = expected_result
                
                # Act
                result = engine.process_audio_file(sample_audio_path)
                
                # Assert
                assert result == expected_result
                mock_process.assert_called_once_with(sample_audio_path)
        
        def test_process_audio_file_not_found(self, engine):
            """测试音频文件不存在的错误场景"""
            # Arrange
            non_existent_path = Path("non_existent.wav")
            
            # Act & Assert
            with pytest.raises(FileNotFoundError, match="音频文件不存在"):
                engine.process_audio_file(non_existent_path)
        
        @pytest.mark.parametrize("invalid_format", [".txt", ".doc", ".pdf"])
        def test_process_audio_file_invalid_format(self, engine, tmp_path, invalid_format):
            """测试不支持的音频格式"""
            # Arrange
            invalid_file = tmp_path / f"test{invalid_format}"
            invalid_file.write_text("fake content")
            
            # Act & Assert
            with pytest.raises(ValueError, match="不支持的音频格式"):
                engine.process_audio_file(invalid_file)
    ```
    
    3.2 测试覆盖率要求
    - 单元测试覆盖率 ≥ 90%
    - 分支覆盖率 ≥ 85%
    - 关键路径覆盖率 = 100%
    - 异常处理覆盖率 ≥ 80%
    
    3.3 集成测试规范
    ```python
    class TestWhisperXIntegration:
        """WhisperX集成测试"""
        
        @pytest.mark.integration
        def test_full_audio_processing_pipeline(self):
            """测试完整的音频处理流程"""
            # 测试真实的音频文件处理
            pass
        
        @pytest.mark.slow
        def test_large_file_processing(self):
            """测试大文件处理性能"""
            # 测试长时间音频处理
            pass
    ```
    ```

    ### Phase 4: 代码审查流程
    ```
    4.1 自动化检查
    
    Pre-commit hooks配置：
    ```yaml
    repos:
      - repo: https://github.com/psf/black
        rev: 23.1.0
        hooks:
          - id: black
            language_version: python3.8
      
      - repo: https://github.com/pycqa/isort
        rev: 5.12.0
        hooks:
          - id: isort
      
      - repo: https://github.com/pycqa/flake8
        rev: 6.0.0
        hooks:
          - id: flake8
      
      - repo: https://github.com/pre-commit/mirrors-mypy
        rev: v1.0.1
        hooks:
          - id: mypy
            additional_dependencies: [types-all]
      
      - repo: https://github.com/PyCQA/bandit
        rev: 1.7.4
        hooks:
          - id: bandit
            args: ['-r', '.']
    ```
    
    CI/CD检查流程：
    ```yaml
    name: Code Quality Check
    on: [push, pull_request]
    
    jobs:
      quality-check:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v3
          - name: Set up Python
            uses: actions/setup-python@v4
            with:
              python-version: 3.8
          
          - name: Install dependencies
            run: |
              pip install -r requirements-dev.txt
          
          - name: Run black
            run: black --check .
          
          - name: Run isort
            run: isort --check-only .
          
          - name: Run flake8
            run: flake8 .
          
          - name: Run mypy
            run: mypy .
          
          - name: Run bandit
            run: bandit -r .
          
          - name: Run tests with coverage
            run: |
              pytest --cov=voice_came --cov-report=xml --cov-fail-under=90
          
          - name: Upload coverage to Codecov
            uses: codecov/codecov-action@v3
    ```
    
    4.2 人工代码审查清单
    
    功能性审查：
    - [ ] 代码实现是否符合需求规格
    - [ ] 算法逻辑是否正确
    - [ ] 边界条件是否处理完整
    - [ ] 错误处理是否充分
    
    可读性审查：
    - [ ] 变量和函数命名是否清晰
    - [ ] 代码结构是否合理
    - [ ] 注释是否充分且准确
    - [ ] 复杂逻辑是否有说明
    
    性能审查：
    - [ ] 是否存在明显的性能问题
    - [ ] 内存使用是否合理
    - [ ] 是否有不必要的计算
    - [ ] 并发安全性是否考虑
    
    安全性审查：
    - [ ] 输入验证是否充分
    - [ ] 是否存在安全漏洞
    - [ ] 敏感信息是否妥善处理
    - [ ] 权限控制是否正确
    ```

    ### Phase 5: 持续改进
    ```
    5.1 代码质量监控
    - 定期生成代码质量报告
    - 跟踪技术债务变化趋势
    - 监控测试覆盖率变化
    - 分析代码复杂度演进
    
    5.2 团队培训和分享
    - 定期举行代码质量培训
    - 分享最佳实践案例
    - 讨论代码审查发现的问题
    - 更新编码规范和工具配置
    
    5.3 工具和流程优化
    - 评估新的代码质量工具
    - 优化CI/CD流程效率
    - 改进代码审查流程
    - 更新质量标准和指标
    ```
  </process>

  <criteria>
    ## 代码质量评价标准

    ### 静态代码质量指标
    - ✅ Black格式化通过率 = 100%
    - ✅ Flake8检查通过率 = 100%
    - ✅ MyPy类型检查通过率 ≥ 95%
    - ✅ Bandit安全检查无高危问题
    - ✅ 代码重复率 ≤ 5%
    - ✅ 圈复杂度 ≤ 10（函数级别）
    - ✅ 认知复杂度 ≤ 15（函数级别）
    - ✅ 类复杂度 ≤ 20

    ### 测试质量指标
    - ✅ 单元测试覆盖率 ≥ 90%
    - ✅ 分支覆盖率 ≥ 85%
    - ✅ 测试通过率 = 100%
    - ✅ 测试执行时间 ≤ 5分钟（单元测试）
    - ✅ 测试稳定性 ≥ 99%（无随机失败）
    - ✅ 关键路径测试覆盖率 = 100%
    - ✅ 异常处理测试覆盖率 ≥ 80%

    ### 文档质量指标
    - ✅ 公共API文档覆盖率 = 100%
    - ✅ 文档字符串覆盖率 ≥ 90%
    - ✅ 类型注解覆盖率 ≥ 95%
    - ✅ 代码注释密度 10-20%
    - ✅ README文档完整性 ≥ 95%
    - ✅ API文档准确性 ≥ 95%

    ### 代码审查质量指标
    - ✅ 代码审查覆盖率 = 100%
    - ✅ 审查发现问题修复率 = 100%
    - ✅ 审查响应时间 ≤ 24小时
    - ✅ 审查质量评分 ≥ 4.0/5.0
    - ✅ 重大问题发现率 ≥ 90%
    - ✅ 审查意见采纳率 ≥ 85%

    ### 维护性指标
    - ✅ 技术债务比例 ≤ 5%
    - ✅ 代码重构频率适中（每月1-2次）
    - ✅ Bug修复时间 ≤ 48小时（高优先级）
    - ✅ 新功能开发效率稳定
    - ✅ 代码可读性评分 ≥ 4.0/5.0
    - ✅ 新人上手时间 ≤ 1周

    ### 性能质量指标
    - ✅ 代码执行效率符合性能要求
    - ✅ 内存使用合理，无明显泄漏
    - ✅ CPU使用率在合理范围内
    - ✅ 并发安全性验证通过
    - ✅ 资源释放及时完整
    - ✅ 性能回归测试通过
  </criteria>
</execution> 