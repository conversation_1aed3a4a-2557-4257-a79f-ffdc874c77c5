# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Python specific
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Testing and TDD
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
pytest.xml
bandit-report.json

# Type checking
.mypy_cache/
.dmypy.json
dmypy.json

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files and directories
temp_test/
output_test/
models_test/
logs_test/
.temp/

# Model files (large)
*.bin
*.gguf
*.safetensors
models/
checkpoints/

# Audio files (if not needed in repo)
*.wav
*.mp3
*.flac
*.ogg
!tests/data/sample.*

# Documentation build
docs/_build/
site/