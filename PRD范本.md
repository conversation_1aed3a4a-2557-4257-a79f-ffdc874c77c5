# ImgxAI - 产品需求文档
## 1. 简介
本文档概述了 ImgxAI 的全面产品需求。ImgxAI 是一款网络应用程序，可让用户通过 OpenAI 的图像生成 API 生成图像。该产品需求文档（PRD）为开发团队提供参考，详细说明了功能、用户界面、技术要求和设计准则，以确保产品愿景的一致实现。
## 2. 产品概述
ImgxAI 是一款用户友好的网络应用程序，用户可以通过向 OpenAI 的图像生成 API 提交文本提示来创建 AI 生成的图像。该应用程序提供了一个简单直观的界面，用户可以在其中输入提示、自定义生成参数、以时间线格式查看他们的生成历史，并下载生成的图像。ImgxAI 在保持简约设计美学的同时，注重易用性、性能和可访问性。
## 3. 目标
### 3.1 主要目标
- 为使用 OpenAI 的 `gpt-image-1` 模型生成图像创建一个简化的界面。
- 为用户提供其图像生成历史的持久时间线。
- 在保持简单性的同时，允许对图像参数进行自定义。
- 确保在所有设备类型上都能实现响应式性能。
- 提供简约、可访问的用户体验。
## 4. 目标受众
### 4.1 主要用户
- 寻求灵感或素材的创意专业人士。
- 探索视觉概念的设计师。
- 需要自定义图像的内容创作者。
- 尝试 AI 图像生成的爱好者。
- 任何拥有 OpenAI API 密钥并对创建图像感兴趣的人。
### 4.2 用户需求
- 用于提交提示的简单直观界面。
- 快速访问先前生成的图像。
- 能够自定义图像参数。
- 离线访问先前生成的图像。
- 轻松下载生成的图像。
## 5. 功能和需求
### 5.1 核心集成
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| CI - 101 | 图像模型集成 | 实现与 OpenAI `gpt-image-1` 模型的集成 | 高 |
| CI - 102 | API 调用实现 | 配置应用程序以调用 OpenAI 图像 API | 高 |
| CI - 103 | 默认审核 | 确保所有提示提交都包含 `moderation="low"` 参数 | 高 |
### 5.2 用户界面
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| UI - 101 | 统一布局 | 实现单页界面，底部固定聊天输入框，内容区域为可滚动的时间线网格 | 高 |
| UI - 102 | 响应式时间线网格 | 创建响应式网格布局：1 列（移动设备）、2 列（平板设备）、4 列（桌面设备） | 高 |
| UI - 103 | 无限滚动 | 实现分页功能，当滚动到页面底部时自动加载旧结果 | 中 |
| UI - 104 | 时间线过滤器 | 添加日期范围、状态、纵横比和排序顺序（最新/最旧）的过滤器，且界面简洁 | 中 |
| UI - 105 | 图像图块显示 | 在方形缩略图中按原始纵横比显示图像，点击可打开详细视图 | 高 |
| UI - 106 | 详细视图 | 创建模态框，显示完整提示、所有相关图像、元数据、重新运行按钮和下载选项 | 高 |
| UI - 107 | 导航 | 实现顶部栏，包含应用程序标题和设置按钮 | 中 |
### 5.3 提示生命周期
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| PL - 101 | 提示提交 | 允许用户输入带有可选参数的文本提示 | 高 |
| PL - 102 | 任务处理 | 将提交的任务作为后台任务处理，并立即显示占位符 | 高 |
| PL - 103 | 结果显示 | 任务完成后，用实际图像更新占位符 | 高 |
| PL - 104 | 重新运行功能 | 允许用户将任何先前的提示作为新任务重新运行 | 中 |
### 5.4 提示选项
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| PO - 101 | 质量选择 | 允许选择“低”、“中”和“高”质量 | 高 |
| PO - 102 | 纵横比选择 | 提供“1024x1024”、“1536x1024”和“1024x1536”纵横比选项 | 高 |
| PO - 103 | 输出压缩 | 启用 0 - 100 的压缩设置，默认值为 50 | 中 |
| PO - 104 | 固定参数 | 设置输出格式（“webp”）、审核（“low”）、响应格式（“b64_json”）和模型（“gpt-image-1”）的固定值 | 高 |
| PO - 105 | 图像数量 | 允许用户指定要生成的图像数量（n ≥ 1） | 中 |
### 5.5 存储
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| ST - 101 | API 密钥存储 | 将 OpenAI API 密钥安全地存储在浏览器本地存储中 | 高 |
| ST - 102 | 图像存储 | 将生成的图像以 Base - 64 字符串形式与提示元数据一起存储 | 高 |
| ST - 103 | IndexedDB 实现 | 通过包装器使用 IndexedDB 进行持久存储，以任务 ID 作为键 | 高 |
| ST - 104 | 离线访问 | 支持离线访问先前生成的图像 | 中 |
### 5.6 下载
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| DL - 101 | 客户端下载 | 实现 WebP 图像的客户端下载功能 | 高 |
| DL - 102 | 遵循压缩设置 | 对下载的图像应用指定的输出压缩设置 | 中 |
## 6. 用户故事和验收标准
### 6.1 用户设置和配置
**ST - 101：首次设置**
- 作为新用户，我希望设置我的 API 密钥，以便开始生成图像。
- **验收标准**：
  - 设置模态框包含用于输入 OpenAI API 密钥的字段。
  - API 密钥安全地存储在浏览器存储中。
  - 验证确认 API 密钥格式正确。
  - 密钥保存成功时，用户会收到确认信息。
**ST - 102：更新设置**
- 作为用户，我希望在需要时更新我的 API 密钥和偏好设置。
- **验收标准**：
  - 可以从顶部导航栏访问设置模态框。
  - 显示之前保存的 API 密钥（掩码形式）以供查看。
  - 仅在明确提交时才保存更改。
  - 设置更新时，用户会收到确认信息。
### 6.2 图像生成
**IG - 101：基本提示提交**
- 作为用户，我希望输入文本提示以生成图像。
- **验收标准**：
  - 输入字段显著显示在屏幕底部。
  - 提交按钮或回车键触发生成。
  - 有视觉指示表明正在生成。
  - 生成的图像完成后会显示在时间线中。
**IG - 102：高级参数配置**
- 作为用户，我希望自定义图像生成参数。
- **验收标准**：
  - 提供质量、纵横比、压缩和数量的选项。
  - 用户界面清晰显示当前选择和默认值。
  - 参数更改在当前会话期间保持有效。
  - 选定的参数正确随 API 请求发送。
**IG - 103：错误处理**
- 作为用户，我希望在生成过程中出现错误时得到通知。
- **验收标准**：
  - API 调用失败时，清晰显示错误消息。
  - 不同类型的错误（身份验证、内容策略、服务器错误）显示相应的消息。
  - 提供重试失败任务的选项。
  - 错误信息中包含有用的故障排除建议。
### 6.3 时间线和历史记录
**TL - 101：查看生成历史**
- 作为用户，我希望在时间线中查看我所有先前生成的图像。
- **验收标准**：
  - 时间线以网格布局显示所有过去的生成记录。
  - 网格根据设备大小规格进行响应式调整。
  - 通过懒加载实现图像高效加载。
  - 从详细视图返回时，时间线保持原位置。
**TL - 102：过滤和排序**
- 作为用户，我希望对我的图像历史记录进行过滤和排序。
- **验收标准**：
  - 提供日期范围、状态和纵横比的过滤选项。
  - 排序控件允许在最新和最旧优先之间切换。
  - 选择更改时，过滤视图立即更新。
  - 直观显示当前的过滤/排序状态。
**TL - 103：无限滚动**
- 作为用户，我希望无缝浏览我的整个生成历史记录。
- **验收标准**：
  - 滚动到底部附近时，自动加载更多历史记录项。
  - 分页获取数据时显示加载指示器。
  - 新项加载时，保持滚动位置。
  - 即使历史记录很多，性能也能保持流畅。
### 6.4 图像交互
**II - 101：查看图像详细信息**
- 作为用户，我希望查看生成图像的详细信息。
- **验收标准**：
  - 点击图像打开详细视图。
  - 详细视图显示完整提示文本、所有相关图像和元数据。
  - 信息包括任务 ID、创建时间、状态、尺寸和其他参数。
  - 导航控件允许在同一提示生成的图像之间浏览。
**II - 102：下载图像**
- 作为用户，我希望将生成的图像下载到我的设备上。
- **验收标准**：
  - 详细视图中的每个图像都有下载按钮。
  - 下载的图像根据压缩设置保持适当的质量。
  - 文件以 WebP 格式下载，并具有适当的命名。
  - 下载功能在所有支持的设备和浏览器上均可使用。
**II - 103：重新运行提示**
- 作为用户，我希望重新运行先前的提示以获得新的变体。
- **验收标准**：
  - 详细视图中提供重新运行按钮。
  - 重新运行使用与原始生成完全相同的参数。
  - 新任务在时间线中作为单独的条目显示。
  - 有视觉指示区分原始任务和重新运行的任务。
### 6.5 边缘情况
**EC - 101：离线使用**
- 作为用户，我希望在离线时访问我先前生成的图像。
- **验收标准**：
  - 离线时可以查看先前生成的图像。
  - 明确指示离线时无法进行新的生成。
  - 应用程序能够优雅地处理在线和离线状态之间的转换。
  - 连接恢复时处理排队的操作。
**EC - 102：存储限制**
- 作为用户，我希望能够在设置页面上清除我的存储。
## 7. 技术要求
### 7.1 技术栈
技术栈已经确定，因此以下要求完整，但可作为实现功能的参考。
| 需求编号 | 需求 | 描述 |
|----------------|-------------|-------------|
| TS - 101 | 编程语言 | 使用 TypeScript 实现，以保证类型安全并提升开发体验 |
| TS - 102 | 框架 | 适当使用带有应用路由器和 React 服务器组件的 Next.js 15 |
| TS - 103 | 样式 | 使用 TailwindCSS 和 Shadcn UI 组件实现样式；使用 Lucide 图标进行操作 |
| TS - 104 | 表单 | 使用 React Hook Form 和 Zod 验证实现表单 |
| TS - 105 | 状态管理 | 使用 React Query 或 SWR 进行 API 调用和缓存；使用 Context/Store 管理全局设置 |
### 7.2 API 集成
| 需求编号 | 需求 | 描述 |
|----------------|-------------|-------------|
| API - 101 | OpenAI 客户端 | 实现用于 OpenAI 图像 API 的安全客户端 |
| API - 102 | 参数映射 | 正确将用户界面参数映射到 API 请求参数 |
| API - 103 | 错误处理 | 为 API 响应实现全面的错误处理 |
| API - 104 | 速率限制 | 支持管理 API 速率限制，包括向用户反馈 |
### 7.3 存储实现
| 需求编号 | 需求 | 描述 |
|----------------|-------------|-------------|
| SI - 101 | IndexedDB 包装器 | 实现一个围绕 IndexedDB 的轻量级包装器，以便更轻松地访问数据 |
| SI - 102 | 数据架构 | 定义用于存储任务、图像和元数据的架构 |
| SI - 103 | 查询优化 | 优化用于时间线显示和过滤的查询 |
| SI - 104 | 数据迁移 | 支持未来更新的架构迁移 |
### 7.4 性能要求
| 需求编号 | 需求 | 描述 |
|----------------|-------------|-------------|
| PR - 101 | 图像优化 | 为时间线图像实现懒加载和适当的压缩 |
## 8. 设计和用户界面
### 8.1 视觉设计原则
- 简约界面，注重内容而非界面装饰。
- 整个应用程序的间距和大小保持一致。
- 简洁的排版，具有清晰的层次结构。
- 通过 TailwindCSS 支持浅色和深色模式。
- 高对比度以确保可访问性。
- 为所有用户交互提供视觉反馈。
### 8.2 用户界面组件
#### 8.2.1 主布局
- 单页应用程序，顶部有固定的导航栏。
- 聊天式输入框固定在屏幕底部。
- 可滚动的时间线网格占据主要内容区域。
- 用于详细视图和设置的模态覆盖层。
#### 8.2.2 时间线网格
- 基于设备大小的响应式网格布局。
- 每个单元格显示带有最少元数据的图像缩略图。
- 直观显示任务状态（待处理、已完成、失败）。
- 项目之间的填充和间距一致。
#### 8.2.3 输入区域
- 用于输入提示的显著文本输入框。
- 可展开的面板，用于显示额外参数。
- 带有适当加载状态的提交按钮。
- 内联显示错误反馈。
#### 8.2.4 详细视图
- 带有关闭按钮的全屏模态框。
- 大图像显示，可在相关图像之间导航。
- 完整的元数据面板。
- 用于下载和重新运行的操作按钮。
### 8.3 可访问性要求
- 所有界面元素符合 WCAG 2.1 AA 标准。
- 整个应用程序支持键盘导航。
- 所有功能与屏幕阅读器兼容。
- 从提示中获取适当的 alt 文本。
- 为键盘导航提供焦点指示器。
### 8.4 响应式设计规范
| 断点 | 网格列数 | UI 调整 |
|------------|--------------|----------------|
| 小（< sm） | 1 列 | 简化选项 UI，垂直布局 |
| 中（sm - lg） | 2 列 | 紧凑的元数据显示，水平选项 |
| 大（> lg） | 4 列 | 完整功能显示，扩展的详细视图 |
### 8.5 错误状态和反馈
- 为 API 和身份验证问题提供清晰的错误横幅。
- 对表单输入进行内联验证。
- 为正在进行的操作提供加载指示器。
- 为已完成的操作提供成功确认。
- 为解决常见问题提供有用的指导。