name: voice-came
channels:
  - conda-forge
  - pytorch
  - nvidia
  - defaults

dependencies:
  - python=3.10
  
  # PyTorch ecosystem
  - pytorch>=2.0.0
  - torchvision>=0.15.0
  - torchaudio>=2.0.0
  - pytorch-cuda=12.1  # For NVIDIA GPU support
  
  # Audio processing
  - librosa>=0.10.0
  - soundfile>=0.12.1
  - ffmpeg
  
  # Scientific computing
  - numpy>=1.24.0
  - scipy>=1.10.0
  - pandas>=2.0.0
  
  # Development tools
  - pip
  - git
  
  # Additional dependencies via pip
  - pip:
    - whisperx>=3.1.1
    - transformers>=4.30.0
    - accelerate>=0.20.0
    - datasets>=2.12.0
    - llama-cpp-python>=0.2.56
    - pydub>=0.25.1
    - webrtcvad>=2.0.10
    - tqdm>=4.65.0
    - rich>=13.4.0
    - pyyaml>=6.0
    - python-dotenv>=1.0.0
    - hydra-core>=1.3.0
    - typing-extensions>=4.5.0
    - requests>=2.31.0
    - httpx>=0.24.0
    - pyttsx3>=2.90
    - polyglot>=16.7.4
    - langdetect>=1.0.9
    - loguru>=0.7.0
    - psutil>=5.9.0
    - memory-profiler>=0.61.0
    - ffmpeg-normalize>=1.28.2 