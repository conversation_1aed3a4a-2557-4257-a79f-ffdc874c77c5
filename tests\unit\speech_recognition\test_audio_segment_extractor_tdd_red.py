#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音片段提取器测试用例 - TDD Red阶段

Task 3.7: 语音片段提取测试设计
状态：TDD-Red阶段（编写完整测试用例，所有测试初始状态必须为FAIL）

测试覆盖：
1. 音频分割准确性测试
2. 片段质量评估测试  
3. 噪声过滤效果测试
4. 批量处理测试
5. 输出格式验证测试
6. 性能基准测试
7. 边界条件测试

目标：从3-12小时长视频中提取15-60分钟高质量语音片段
"""

import os
import pytest
import tempfile
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import asyncio
import time
from unittest.mock import Mock, patch

# 这些导入在Red阶段应该失败，因为AudioSegmentExtractor还未实现
from voice_came.speech_recognition.audio_segment_extractor import AudioSegmentExtractor
from voice_came.speech_recognition.speech_activity_detector import SpeechActivityDetector
from voice_came.speech_recognition.whisperx_engine import WhisperXConfig
from voice_came.core.file_processor import FileProcessor


class TestAudioSegmentExtractorRed:
    """语音片段提取器测试类 - TDD Red阶段"""
    
    @pytest.fixture
    def extractor_config(self):
        """片段提取器测试配置"""
        return {
            "whisperx_config": WhisperXConfig(
                model_name="small",
                device="cpu",
                vad_threshold=0.3,
                vad_min_speech_duration_ms=500,
                vad_max_silence_duration_ms=1500,
                asmr_mode=True
            ),
            "quality_threshold": 0.7,
            "min_segment_duration": 15.0,  # 最小15秒
            "max_segment_duration": 3600.0,  # 最大60分钟
            "target_segment_duration": 1800.0,  # 目标30分钟
            "noise_reduction_level": 0.5,
            "output_format": "wav",
            "sample_rate": 16000,
            "max_concurrent_extractions": 4
        }
    
    @pytest.fixture
    def mock_long_audio_files(self, tmp_path):
        """模拟长音频文件数据"""
        audio_files = {}
        
        # 3小时音频（模拟）
        audio_files["short_video"] = {
            "path": tmp_path / "short_video_3h.wav",
            "duration": 3 * 3600,  # 3小时
            "expected_segments": 6,  # 期望提取6个30分钟片段
            "speech_ratio": 0.6  # 60%是语音
        }
        
        # 8小时音频（模拟）
        audio_files["medium_video"] = {
            "path": tmp_path / "medium_video_8h.wav", 
            "duration": 8 * 3600,  # 8小时
            "expected_segments": 12,  # 期望提取12个片段
            "speech_ratio": 0.4  # 40%是语音
        }
        
        # 12小时音频（模拟）
        audio_files["long_video"] = {
            "path": tmp_path / "long_video_12h.wav",
            "duration": 12 * 3600,  # 12小时
            "expected_segments": 16,  # 期望提取16个片段
            "speech_ratio": 0.3  # 30%是语音
        }
        
        # 含噪音频
        audio_files["noisy_video"] = {
            "path": tmp_path / "noisy_video_6h.wav",
            "duration": 6 * 3600,  # 6小时
            "expected_segments": 4,  # 噪声较多，提取较少
            "speech_ratio": 0.2,  # 20%是干净语音
            "noise_level": 0.8  # 高噪声
        }
        
        # ASMR低音量音频
        audio_files["asmr_video"] = {
            "path": tmp_path / "asmr_video_5h.wav",
            "duration": 5 * 3600,  # 5小时
            "expected_segments": 8,  # ASMR通常片段较多
            "speech_ratio": 0.8,  # 80%是有效内容
            "volume_level": 0.1  # 低音量
        }
        
        # 创建实际的模拟文件
        for file_info in audio_files.values():
            file_path = file_info["path"]
            file_path.touch()  # 创建空文件
        
        return audio_files

    # =================================================================
    # 1. 音频分割准确性测试
    # =================================================================
    
    def test_extract_segments_from_short_video(self, extractor_config, mock_long_audio_files):
        """测试从3小时视频中提取语音片段的准确性"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["short_video"]
        
        segments = extractor.extract_audio_segments(
            video_file["path"],
            target_duration=1800  # 30分钟片段
        )
        
        # 验证提取的片段数量
        assert len(segments) == video_file["expected_segments"]
        
        # 验证每个片段的时长
        for segment in segments:
            assert 900 <= segment["duration"] <= 3600  # 15分钟到60分钟
            assert segment["start_time"] >= 0
            assert segment["end_time"] > segment["start_time"]
    
    def test_extract_segments_from_long_video(self, extractor_config, mock_long_audio_files):
        """测试从12小时长视频中提取语音片段"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["long_video"]
        
        segments = extractor.extract_audio_segments(
            video_file["path"],
            max_segments=20  # 限制最大片段数
        )
        
        # 验证提取数量合理
        assert 10 <= len(segments) <= 20
        
        # 验证片段时间不重叠
        segments_sorted = sorted(segments, key=lambda x: x["start_time"])
        for i in range(len(segments_sorted) - 1):
            current_end = segments_sorted[i]["end_time"]
            next_start = segments_sorted[i + 1]["start_time"]
            assert current_end <= next_start  # 无重叠
    
    def test_segment_boundary_detection_accuracy(self, extractor_config, mock_long_audio_files):
        """测试语音片段边界检测的准确性"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["medium_video"]
        
        segments = extractor.extract_audio_segments(
            video_file["path"],
            boundary_precision="high"  # 高精度边界检测
        )
        
        # 验证边界检测精度
        for segment in segments:
            # 验证片段开始和结束都在语音区域
            assert segment["start_boundary_type"] == "speech_start"
            assert segment["end_boundary_type"] == "speech_end"
            
            # 验证边界处的音频质量指标
            assert segment["start_quality_score"] >= 0.6
            assert segment["end_quality_score"] >= 0.6

    # =================================================================
    # 2. 片段质量评估测试
    # =================================================================
    
    def test_segment_quality_scoring(self, extractor_config, mock_long_audio_files):
        """测试语音片段质量评分"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["short_video"]
        
        segments = extractor.extract_audio_segments(
            video_file["path"],
            quality_analysis=True
        )
        
        # 验证每个片段都有质量评分
        for segment in segments:
            assert "quality_score" in segment
            assert 0.0 <= segment["quality_score"] <= 1.0
            
            # 验证质量指标
            assert "signal_to_noise_ratio" in segment
            assert "speech_clarity" in segment
            assert "audio_consistency" in segment
            assert segment["signal_to_noise_ratio"] >= 10  # 至少10dB SNR
    
    def test_low_quality_segment_filtering(self, extractor_config, mock_long_audio_files):
        """测试低质量片段过滤"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["noisy_video"]
        
        # 设置高质量阈值
        segments_high_quality = extractor.extract_audio_segments(
            video_file["path"],
            quality_threshold=0.8
        )
        
        # 设置低质量阈值
        segments_low_quality = extractor.extract_audio_segments(
            video_file["path"],
            quality_threshold=0.3
        )
        
        # 高质量阈值应该产生更少但更好的片段
        assert len(segments_high_quality) < len(segments_low_quality)
        
        # 验证高质量片段确实质量更好
        if segments_high_quality:
            avg_quality_high = sum(s["quality_score"] for s in segments_high_quality) / len(segments_high_quality)
            avg_quality_low = sum(s["quality_score"] for s in segments_low_quality) / len(segments_low_quality)
            assert avg_quality_high > avg_quality_low
    
    def test_speech_content_analysis(self, extractor_config, mock_long_audio_files):
        """测试语音内容分析和评估"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["asmr_video"]
        
        segments = extractor.extract_audio_segments(
            video_file["path"],
            content_analysis=True
        )
        
        # 验证内容分析结果
        for segment in segments:
            assert "speech_density" in segment  # 语音密度
            assert "pause_ratio" in segment     # 停顿比例
            assert "word_count_estimate" in segment  # 估计词数
            assert "language_confidence" in segment  # 语言识别置信度
            
            # ASMR视频特征验证
            assert 0.3 <= segment["speech_density"] <= 0.9  # 适中的语音密度
            assert segment["pause_ratio"] <= 0.4  # 停顿不太多

    # =================================================================
    # 3. 噪声过滤效果测试
    # =================================================================
    
    def test_noise_reduction_effectiveness(self, extractor_config, mock_long_audio_files):
        """测试噪声降低效果"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["noisy_video"]
        
        # 不启用噪声降低
        segments_raw = extractor.extract_audio_segments(
            video_file["path"],
            noise_reduction=False
        )
        
        # 启用噪声降低
        segments_filtered = extractor.extract_audio_segments(
            video_file["path"], 
            noise_reduction=True,
            noise_reduction_level=0.7
        )
        
        # 验证噪声降低效果
        if segments_raw and segments_filtered:
            raw_avg_snr = sum(s["signal_to_noise_ratio"] for s in segments_raw) / len(segments_raw)
            filtered_avg_snr = sum(s["signal_to_noise_ratio"] for s in segments_filtered) / len(segments_filtered)
            assert filtered_avg_snr > raw_avg_snr  # 信噪比应该提升
    
    def test_background_noise_detection(self, extractor_config, mock_long_audio_files):
        """测试背景噪声检测和分类"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["noisy_video"]
        
        segments = extractor.extract_audio_segments(
            video_file["path"],
            noise_analysis=True
        )
        
        # 验证噪声分析结果
        for segment in segments:
            assert "noise_profile" in segment
            assert "dominant_noise_type" in segment  # 主要噪声类型
            assert "noise_level_db" in segment       # 噪声水平
            assert "noise_reduction_applied" in segment  # 是否应用了降噪
            
            # 噪声分类验证
            noise_types = ["white_noise", "ambient", "mechanical", "voices", "music", "unknown"]
            assert segment["dominant_noise_type"] in noise_types
    
    def test_adaptive_noise_filtering(self, extractor_config, mock_long_audio_files):
        """测试自适应噪声过滤"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["noisy_video"]
        
        segments = extractor.extract_audio_segments(
            video_file["path"],
            adaptive_filtering=True
        )
        
        # 验证自适应过滤效果
        for segment in segments:
            assert "adaptive_filter_applied" in segment
            assert "filter_strength" in segment
            assert 0.0 <= segment["filter_strength"] <= 1.0
            
            # 高噪声区域应该应用更强的过滤
            if segment["noise_level_db"] > 30:  # 高噪声
                assert segment["filter_strength"] >= 0.5

    # =================================================================
    # 4. 批量处理测试
    # =================================================================
    
    def test_batch_extraction_multiple_files(self, extractor_config, mock_long_audio_files):
        """测试多文件批量提取"""
        extractor = AudioSegmentExtractor(extractor_config)
        
        video_files = [
            mock_long_audio_files["short_video"]["path"],
            mock_long_audio_files["medium_video"]["path"],
            mock_long_audio_files["asmr_video"]["path"]
        ]
        
        batch_results = extractor.extract_batch(
            video_files,
            max_concurrent=2
        )
        
        # 验证批量处理结果
        assert len(batch_results) == len(video_files)
        
        for file_path, result in batch_results.items():
            assert "segments" in result
            assert "processing_time" in result
            assert "status" in result
            assert result["status"] in ["success", "failed", "partial"]
            
            if result["status"] == "success":
                assert len(result["segments"]) > 0
    
    def test_concurrent_extraction_performance(self, extractor_config, mock_long_audio_files):
        """测试并发提取的性能"""
        extractor = AudioSegmentExtractor(extractor_config)
        
        video_files = [
            mock_long_audio_files["short_video"]["path"],
            mock_long_audio_files["medium_video"]["path"]
        ]
        
        # 串行处理
        start_time = time.time()
        serial_results = []
        for video_file in video_files:
            result = extractor.extract_audio_segments(video_file)
            serial_results.append(result)
        serial_time = time.time() - start_time
        
        # 并行处理
        start_time = time.time()
        parallel_results = extractor.extract_batch(
            video_files,
            max_concurrent=2
        )
        parallel_time = time.time() - start_time
        
        # 并行处理应该更快（在多文件情况下）
        if len(video_files) > 1:
            assert parallel_time < serial_time * 0.8  # 至少快20%
    
    def test_batch_error_handling(self, extractor_config, mock_long_audio_files, tmp_path):
        """测试批量处理中的错误处理"""
        extractor = AudioSegmentExtractor(extractor_config)
        
        # 混合有效和无效文件
        video_files = [
            mock_long_audio_files["short_video"]["path"],  # 有效
            tmp_path / "nonexistent.wav",                   # 不存在
            mock_long_audio_files["medium_video"]["path"]  # 有效
        ]
        
        batch_results = extractor.extract_batch(
            video_files,
            continue_on_error=True
        )
        
        # 验证错误处理
        assert len(batch_results) == len(video_files)
        
        # 有效文件应该成功
        valid_files = [video_files[0], video_files[2]]
        for valid_file in valid_files:
            assert str(valid_file) in batch_results
            assert batch_results[str(valid_file)]["status"] == "success"
        
        # 无效文件应该失败
        invalid_file = str(video_files[1])
        assert invalid_file in batch_results
        assert batch_results[invalid_file]["status"] == "failed"
        assert "error" in batch_results[invalid_file]

    # =================================================================
    # 5. 输出格式验证测试
    # =================================================================
    
    def test_output_format_wav(self, extractor_config, mock_long_audio_files, tmp_path):
        """测试WAV格式输出"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["short_video"]
        
        output_dir = tmp_path / "wav_output"
        segments = extractor.extract_audio_segments(
            video_file["path"],
            output_dir=output_dir,
            output_format="wav"
        )
        
        # 验证输出文件
        for i, segment in enumerate(segments):
            output_file = output_dir / f"segment_{i:03d}.wav"
            assert output_file.exists()
            assert output_file.suffix == ".wav"
            
            # 验证音频元数据
            assert segment["output_file"] == str(output_file)
            assert segment["file_size"] > 0
            assert segment["sample_rate"] == extractor_config["sample_rate"]
    
    def test_output_format_mp3(self, extractor_config, mock_long_audio_files, tmp_path):
        """测试MP3格式输出"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["asmr_video"]
        
        output_dir = tmp_path / "mp3_output"
        segments = extractor.extract_audio_segments(
            video_file["path"],
            output_dir=output_dir,
            output_format="mp3",
            bitrate=128
        )
        
        # 验证MP3输出
        for segment in segments:
            output_file = Path(segment["output_file"])
            assert output_file.exists()
            assert output_file.suffix == ".mp3"
            assert segment["bitrate"] == 128
    
    def test_metadata_generation(self, extractor_config, mock_long_audio_files, tmp_path):
        """测试元数据生成"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["medium_video"]
        
        output_dir = tmp_path / "metadata_output"
        segments = extractor.extract_audio_segments(
            video_file["path"],
            output_dir=output_dir,
            generate_metadata=True
        )
        
        # 验证元数据文件
        metadata_file = output_dir / "extraction_metadata.json"
        assert metadata_file.exists()
        
        # 验证每个片段的元数据
        for segment in segments:
            required_fields = [
                "segment_id", "start_time", "end_time", "duration",
                "quality_score", "signal_to_noise_ratio", "output_file",
                "extraction_timestamp", "source_file"
            ]
            for field in required_fields:
                assert field in segment
    
    def test_transcription_output(self, extractor_config, mock_long_audio_files, tmp_path):
        """测试转录输出生成"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["short_video"]
        
        output_dir = tmp_path / "transcription_output"
        segments = extractor.extract_audio_segments(
            video_file["path"],
            output_dir=output_dir,
            include_transcription=True
        )
        
        # 验证转录文件
        for segment in segments:
            if "transcription_file" in segment:
                trans_file = Path(segment["transcription_file"])
                assert trans_file.exists()
                assert trans_file.suffix in [".txt", ".srt"]
                
                # 验证转录内容
                assert "transcription_text" in segment
                assert len(segment["transcription_text"]) > 0

    # =================================================================
    # 6. 性能基准测试
    # =================================================================
    
    def test_extraction_speed_benchmark(self, extractor_config, mock_long_audio_files):
        """测试提取速度基准"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["medium_video"]
        
        start_time = time.time()
        segments = extractor.extract_audio_segments(video_file["path"])
        processing_time = time.time() - start_time
        
        # 性能基准验证
        video_duration = video_file["duration"]
        speed_ratio = video_duration / processing_time  # 实时倍速
        
        # 期望处理速度至少是实时的10倍（CPU模式）
        assert speed_ratio >= 10.0
        
        # 验证内存使用合理
        assert extractor.get_memory_usage() < 4 * 1024 * 1024 * 1024  # 小于4GB
    
    def test_large_file_memory_efficiency(self, extractor_config, mock_long_audio_files):
        """测试大文件内存效率"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["long_video"]  # 12小时文件
        
        initial_memory = extractor.get_memory_usage()
        
        segments = extractor.extract_audio_segments(
            video_file["path"],
            streaming_mode=True  # 流式处理模式
        )
        
        peak_memory = extractor.get_peak_memory_usage()
        
        # 内存使用应该保持在合理范围
        memory_increase = peak_memory - initial_memory
        assert memory_increase < 2 * 1024 * 1024 * 1024  # 增加不超过2GB
        
        # 验证提取结果仍然有效
        assert len(segments) > 0
    
    def test_gpu_acceleration_performance(self, extractor_config, mock_long_audio_files):
        """测试GPU加速性能（如果可用）"""
        # CPU模式
        cpu_config = extractor_config.copy()
        cpu_config["whisperx_config"].device = "cpu"
        cpu_extractor = AudioSegmentExtractor(cpu_config)
        
        video_file = mock_long_audio_files["short_video"]
        
        # CPU处理时间
        start_time = time.time()
        cpu_segments = cpu_extractor.extract_audio_segments(video_file["path"])
        cpu_time = time.time() - start_time
        
        # GPU模式（如果可用）
        if extractor_config["whisperx_config"].device == "cuda":
            gpu_config = extractor_config.copy()
            gpu_extractor = AudioSegmentExtractor(gpu_config)
            
            start_time = time.time()
            gpu_segments = gpu_extractor.extract_audio_segments(video_file["path"])
            gpu_time = time.time() - start_time
            
            # GPU应该更快
            assert gpu_time < cpu_time * 0.5  # 至少快50%
            
            # 结果应该一致
            assert len(gpu_segments) == len(cpu_segments)

    # =================================================================
    # 7. 边界条件和错误处理测试
    # =================================================================
    
    def test_empty_audio_file(self, extractor_config, tmp_path):
        """测试空音频文件处理"""
        extractor = AudioSegmentExtractor(extractor_config)
        
        # 创建空音频文件
        empty_file = tmp_path / "empty.wav"
        empty_file.touch()
        
        segments = extractor.extract_audio_segments(empty_file)
        
        # 应该返回空结果而不是错误
        assert segments == []
    
    def test_corrupted_audio_file(self, extractor_config, tmp_path):
        """测试损坏音频文件处理"""
        extractor = AudioSegmentExtractor(extractor_config)
        
        # 创建损坏的音频文件
        corrupted_file = tmp_path / "corrupted.wav"
        corrupted_file.write_bytes(b"not a valid audio file")
        
        # 应该抛出适当的异常
        with pytest.raises(Exception) as exc_info:
            extractor.extract_audio_segments(corrupted_file)
        
        assert "corrupted" in str(exc_info.value).lower() or "invalid" in str(exc_info.value).lower()
    
    def test_unsupported_audio_format(self, extractor_config, tmp_path):
        """测试不支持的音频格式"""
        extractor = AudioSegmentExtractor(extractor_config)
        
        # 创建不支持格式的文件
        unsupported_file = tmp_path / "audio.xyz"
        unsupported_file.write_bytes(b"fake audio data")
        
        with pytest.raises(ValueError) as exc_info:
            extractor.extract_audio_segments(unsupported_file)
        
        assert "unsupported" in str(exc_info.value).lower()
    
    def test_extremely_long_audio(self, extractor_config, tmp_path):
        """测试极长音频文件处理"""
        extractor = AudioSegmentExtractor(extractor_config)
        
        # 模拟24小时超长音频
        ultra_long_audio = {
            "path": tmp_path / "ultra_long_24h.wav",
            "duration": 24 * 3600  # 24小时
        }
        
        segments = extractor.extract_audio_segments(
            ultra_long_audio["path"],
            max_processing_time=3600  # 最大处理1小时
        )
        
        # 应该在合理时间内完成或优雅降级
        assert isinstance(segments, list)  # 至少返回列表
    
    def test_concurrent_access_safety(self, extractor_config, mock_long_audio_files):
        """测试并发访问安全性"""
        extractor = AudioSegmentExtractor(extractor_config)
        video_file = mock_long_audio_files["short_video"]
        
        import threading
        results = {}
        errors = []
        
        def extract_worker(worker_id):
            try:
                segments = extractor.extract_audio_segments(
                    video_file["path"],
                    output_dir=f"worker_{worker_id}_output"
                )
                results[worker_id] = segments
            except Exception as e:
                errors.append(e)
        
        # 启动多个并发提取
        threads = []
        for i in range(3):
            thread = threading.Thread(target=extract_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证并发安全性
        assert len(errors) == 0  # 不应该有错误
        assert len(results) == 3  # 所有工作线程都应该成功
    
    def test_invalid_configuration(self):
        """测试无效配置处理"""
        # 无效的质量阈值
        with pytest.raises(ValueError):
            invalid_config = {
                "quality_threshold": 1.5,  # 超过1.0
                "min_segment_duration": -10  # 负数
            }
            AudioSegmentExtractor(invalid_config)
        
        # 无效的时长设置
        with pytest.raises(ValueError):
            invalid_config = {
                "min_segment_duration": 3600,  # 1小时
                "max_segment_duration": 1800   # 30分钟，小于最小值
            }
            AudioSegmentExtractor(invalid_config)