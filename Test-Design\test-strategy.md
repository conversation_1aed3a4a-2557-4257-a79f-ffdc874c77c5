# Voice-came 测试策略文档 v1.0

## 文档信息
- **项目名称**: Voice-came (基于VoiceTransl的睡眠内容全球化工具)
- **文档版本**: v1.0
- **制定日期**: 2025年1月
- **依据文档**: Voice-came_PRD_v4.1.md, Voice-came_开发规范_v1.0.md
- **适用范围**: Voice-came项目全体测试成员

## 1. 测试策略概述

### 1.1 项目背景
Voice-came是基于VoiceTransl架构的睡眠内容全球化处理工具，专为助眠故事创作者提供：
- **长视频语音提取**：从3-12小时视频中提取15-60分钟有效语音
- **WhisperX语音识别**：集成WhisperX引擎，实现高精度语音转文字
- **本地翻译服务**：使用Gemma3-12B-Q4/Qwen3模型进行多语种翻译
- **专业术语管理**：100词助眠术语库，确保翻译专业性
- **批量处理能力**：支持批量视频处理，提升工作效率

### 1.2 测试方法
**测试驱动开发 (TDD)** - 红绿重构循环
- 🔴 **红色阶段**：先写失败的测试用例，明确预期行为
- 🟢 **绿色阶段**：编写最小代码使测试通过，快速验证
- 🔵 **重构阶段**：优化代码结构，保持测试通过，提升质量

### 1.3 测试金字塔架构
```
        /\     E2E Tests (10%)
       /  \    端到端测试 - 用户场景验证
      /____\   用户工作流完整性测试
     /      \  Integration Tests (20%) 
    /        \ 集成测试 - 模块间交互
   /__________\ WhisperX/翻译引擎/数据库集成
  /            \ Unit Tests (70%)
 /              \ 单元测试 - 功能逻辑验证
/________________\ 语音处理/翻译/术语管理/文件操作
```

### 1.4 质量目标（符合开发规范v1.0）
- **代码覆盖率**: ≥90% (超过开发规范80%要求)
- **测试通过率**: 100%
- **圈复杂度**: ≤10 (符合开发规范)
- **函数长度**: ≤50行 (符合开发规范)
- **性能基准**: 3小时视频处理≤30分钟 (符合PRD要求)

## 2. 测试分层策略

### 2.1 单元测试 (70% 覆盖度目标)
**测试范围**（基于开发规范v1.0项目结构）：
- **WhisperX引擎模块** (`voice_came/engines/whisperx/`)
  - whisperx_wrapper.py：WhisperX包装器功能
  - model_loader.py：模型加载和管理
  - 语音活动检测算法验证
  - 音频格式转换处理
  - 批量处理队列管理

- **翻译引擎模块** (`voice_came/engines/translation/`)
  - gemma_translator.py：Gemma3-12B模型调用
  - qwen_translator.py：Qwen3模型调用
  - 翻译结果格式化和验证
  - 模型切换和降级机制
  - 错误处理和重试逻辑

- **核心处理模块** (`voice_came/core/`)
  - batch_processor.py：批量处理控制器
  - translation_manager.py：翻译管理器
  - 业务逻辑正确性验证
  - 工作流状态管理

- **术语管理模块** (`voice_came/utils/`)
  - text_utils.py：术语处理工具
  - 100词术语库加载和匹配
  - 自动替换算法准确性
  - 一致性检查机制
  - 自定义术语处理

- **文件管理模块** (`voice_came/utils/`)
  - file_utils.py：文件操作工具
  - audio_utils.py：音频处理工具
  - 批量文件处理逻辑
  - 文件格式验证和转换
  - 目录结构创建和管理
  - 多格式导出功能

**测试策略**：
- 每个函数都有对应的单元测试
- 覆盖率目标：90%+
- 使用模拟对象(Mock)隔离外部依赖
- 快速执行（<5分钟全量运行）

### 2.2 集成测试 (20% 覆盖度目标)
**测试范围**：
- **WhisperX集成测试**
  - WhisperX引擎配置和调用
  - 语音处理流水线集成
  - 处理结果数据流

- **翻译模型集成测试**
  - Gemma3-12B模型加载和调用
  - Qwen3模型切换功能
  - 模型性能基准测试

- **数据库集成测试**
  - 术语库数据存储和检索
  - 处理历史记录管理
  - 用户配置持久化

- **文件系统集成测试**
  - 大文件上传和处理
  - 批量操作的事务性
  - 磁盘空间管理

**测试策略**：
- 使用测试数据库和临时文件系统
- 模拟真实的外部服务交互
- 验证模块间数据传递正确性
- 执行时间控制在10分钟内

### 2.3 端到端测试 (10% 覆盖度目标)
**测试范围**：
- **完整用户工作流**
  - 视频上传→语音提取→翻译→导出
  - 批量处理完整流程
  - 错误恢复和重试场景

- **性能和稳定性测试**
  - 3小时视频处理性能
  - 连续处理100个文件稳定性
  - 内存和GPU资源使用验证

- **用户体验测试**
  - 界面交互完整性
  - 进度显示准确性
  - 错误提示友好性

**测试策略**：
- 使用真实大小的测试数据
- 模拟真实用户操作场景  
- 自动化执行关键路径
- 手动执行复杂交互场景

## 3. 测试数据策略

### 3.1 测试数据分类
```
测试数据/
├── video_samples/           # 视频测试文件
│   ├── short_video.mp4     # 5分钟测试视频
│   ├── medium_video.mp4    # 1小时测试视频  
│   ├── long_video.mp4      # 3小时测试视频
│   ├── invalid_format.avi  # 无效格式文件
│   └── corrupted_file.mp4  # 损坏文件
├── audio_samples/          # 音频测试文件
│   ├── clean_speech.wav    # 清晰语音
│   ├── noisy_speech.wav    # 带噪音语音
│   ├── mixed_content.wav   # 语音+音乐混合
│   └── silence.wav         # 静音文件
├── translation_samples/    # 翻译测试数据
│   ├── sleep_content_cn.txt # 中文助眠内容
│   ├── technical_terms.txt  # 专业术语集
│   └── edge_cases.txt       # 边界情况文本
└── terminology/            # 术语库测试数据
    ├── sleep_terms_100.json # 100词术语表
    ├── custom_terms.json    # 用户自定义术语
    └── conflict_terms.json  # 冲突术语测试
```

### 3.2 模拟数据生成策略
- **视频文件生成器**：创建不同时长和质量的测试视频
- **语音内容生成器**：生成带有特定特征的音频片段
- **翻译文本生成器**：生成包含术语的多语言测试文本
- **错误情况模拟器**：生成各种异常文件和数据

## 4. 错误模拟策略

### 4.1 系统级错误模拟
- **内存不足**：模拟大文件处理时内存耗尽
- **磁盘空间不足**：模拟临时文件存储失败
- **GPU资源不可用**：模拟GPU驱动问题或资源占用
- **网络中断**：模拟在线API调用失败

### 4.2 数据级错误模拟  
- **文件损坏**：模拟视频文件损坏或不完整
- **格式不支持**：模拟不支持的视频编码格式
- **编码错误**：模拟字符编码问题
- **数据库连接失败**：模拟术语库访问异常

### 4.3 业务级错误模拟
- **翻译服务异常**：模拟本地模型加载失败
- **语音识别失败**：模拟WhisperX处理异常
- **术语冲突**：模拟术语表不一致问题
- **批量处理中断**：模拟长时间任务中断恢复

## 5. 测试环境配置

### 5.1 单元测试环境
```python
# 依赖项
pytest==7.4.0           # 测试框架
pytest-cov==4.1.0       # 覆盖率统计
pytest-mock==3.11.1     # Mock对象支持
pytest-xdist==3.3.1     # 并行测试执行
factory-boy==3.3.0      # 测试数据工厂
faker==19.3.0            # 假数据生成
```

### 5.2 集成测试环境
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  voice-came-test:
    build: .
    environment:
      - TEST_MODE=integration
      - GPU_MEMORY_LIMIT=4GB
    volumes:
      - ./test_data:/app/test_data
      - ./test_results:/app/test_results
    depends_on:
      - test-db
  
  test-db:
    image: sqlite:latest
    environment:
      - SQLITE_DB=test_voice_came.db
```

### 5.3 性能测试环境
- **硬件要求**：16GB RAM, GTX 1060以上GPU
- **监控工具**：内存使用监控、GPU利用率监控
- **基准数据**：3小时视频处理<30分钟

## 6. 测试执行计划

### 6.1 TDD开发循环
```
每个功能开发循环：
1. 分析需求 (5分钟)
2. 编写失败测试 (15分钟)  
3. 运行测试确认失败 (2分钟)
4. 编写最小实现代码 (30分钟)
5. 运行测试直到通过 (5分钟)
6. 重构优化代码 (15分钟)
7. 回归测试确认 (3分钟)
总计：75分钟/功能
```

### 6.2 持续集成测试
```
代码提交触发：
1. 单元测试套件 (5分钟)
2. 代码质量检查 (2分钟)
3. 集成测试子集 (10分钟)

每日构建触发：
1. 完整测试套件 (30分钟)
2. 性能基准测试 (60分钟)
3. 测试报告生成 (5分钟)
```

## 7. 质量门禁标准

### 7.1 代码质量门禁
- **单元测试覆盖率** ≥ 90%
- **集成测试通过率** = 100%
- **代码复杂度** ≤ 10 (圈复杂度)
- **代码重复率** ≤ 3%

### 7.2 功能质量门禁
- **核心功能测试** 100%通过
- **性能基准测试** 满足PRD要求
- **错误处理测试** 覆盖所有异常场景
- **用户验收测试** 满意度≥4.0/5.0

### 7.3 发布质量门禁
- **完整测试套件** 100%通过
- **性能压力测试** 满足连续处理100文件
- **兼容性测试** 支持Windows/macOS/Linux
- **用户手册验证** 新用户30分钟完成配置

## 8. 测试度量和报告

### 8.1 关键测试指标
```
缺陷指标：
- 缺陷发现率 = 测试发现缺陷 / 总缺陷 × 100%
- 缺陷逃逸率 = 生产缺陷 / 总缺陷 × 100%
- 缺陷修复率 = 已修复缺陷 / 发现缺陷 × 100%

覆盖率指标：
- 代码覆盖率 = 被执行代码行 / 总代码行 × 100%
- 需求覆盖率 = 被测试需求 / 总需求 × 100%
- 用例覆盖率 = 已执行用例 / 总用例 × 100%

效率指标：
- 测试执行效率 = 自动化用例 / 总用例 × 100%
- 缺陷发现效率 = 发现缺陷数 / 测试工时
- 回归测试效率 = 自动化回归 / 总回归 × 100%
```

### 8.2 测试报告模板
- **执行摘要**：测试完成情况概述
- **测试结果统计**：通过率、覆盖率、缺陷统计
- **风险评估**：质量风险分析和建议
- **性能测试结果**：性能指标达成情况
- **下一步行动**：需要关注的问题和改进建议

此测试策略将确保Voice-came项目在TDD模式下的高质量交付，每个功能都经过充分的测试验证。 