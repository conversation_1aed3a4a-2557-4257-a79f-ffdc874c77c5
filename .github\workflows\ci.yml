name: CI

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11"]
        exclude:
          # 排除一些组合以减少构建时间
          - os: macos-latest
            python-version: "3.8"
          - os: windows-latest
            python-version: "3.8"

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip packages
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -e .[dev]

    - name: Lint with flake8
      run: |
        flake8 src/ tests/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 src/ tests/ --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Type check with mypy
      run: |
        mypy src/voice_came --ignore-missing-imports

    - name: Format check with black
      run: |
        black --check --diff src/ tests/

    - name: Import sort check with isort
      run: |
        isort --check-only --diff src/ tests/

    - name: Security check with bandit
      run: |
        bandit -r src/ -f json -o bandit-report.json || true

    - name: Test with pytest
      run: |
        pytest --cov=src/voice_came --cov-report=xml --cov-report=term-missing --junitxml=pytest.xml

    - name: Upload coverage reports to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.python-version == '3.10'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: true

    - name: Archive test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.os }}-${{ matrix.python-version }}
        path: |
          pytest.xml
          coverage.xml
          bandit-report.json
          htmlcov/

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build

    - name: Build package
      run: python -m build

    - name: Archive build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/ 