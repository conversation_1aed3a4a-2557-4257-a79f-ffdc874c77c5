# Voice-came 接口契约文档

## 概述

本文档定义Voice-came项目中Task 4（VoiceTransl翻译服务集成接口层）和Task 5（翻译业务逻辑层）之间的接口契约，确保系统集成和业务逻辑的清晰分离。

## 架构分层

```
┌─────────────────────────────────────┐
│           Voice-came UI层            │
├─────────────────────────────────────┤
│      Task 5: 翻译业务逻辑层          │
│  - 术语管理                          │
│  - 翻译质量优化                      │
│  - 批量处理控制                      │
├─────────────────────────────────────┤
│      Task 4: 系统集成接口层          │
│  - 进程间通信                        │
│  - 数据格式转换                      │
│  - 错误处理恢复                      │
├─────────────────────────────────────┤
│          VoiceTransl服务             │
└─────────────────────────────────────┘
```

## Task 4: 系统集成接口层

### 职责边界
- ✅ VoiceTransl子进程生命周期管理
- ✅ 数据格式转换适配
- ✅ 进程间通信协议
- ✅ 集成层错误处理
- ❌ 不涉及术语管理业务逻辑
- ❌ 不涉及翻译质量优化

### 对外接口

#### VoiceTranslIntegrationService
```python
class VoiceTranslIntegrationService:
    """VoiceTransl集成服务接口"""
    
    def start_translation_process(self, 
                                audio_data: AudioData, 
                                config: TranslationConfig) -> TranslationJob:
        """启动翻译任务
        
        Args:
            audio_data: 音频数据（来自WhisperX）
            config: 翻译配置参数
            
        Returns:
            TranslationJob: 翻译任务对象
        """
        pass
    
    def get_translation_status(self, job_id: str) -> JobStatus:
        """获取翻译状态
        
        Args:
            job_id: 任务ID
            
        Returns:
            JobStatus: 任务状态信息
        """
        pass
    
    def retrieve_translation_result(self, job_id: str) -> TranslationResult:
        """获取翻译结果
        
        Args:
            job_id: 任务ID
            
        Returns:
            TranslationResult: 翻译结果
        """
        pass
    
    def cancel_translation(self, job_id: str) -> bool:
        """取消翻译任务"""
        pass
```

#### ProcessManager
```python
class ProcessManager:
    """VoiceTransl进程管理器"""
    
    def start_voicetransl_service(self) -> ServiceStatus:
        """启动VoiceTransl服务"""
        pass
    
    def stop_voicetransl_service(self) -> bool:
        """停止VoiceTransl服务"""
        pass
    
    def check_service_health(self) -> HealthStatus:
        """检查服务健康状态"""
        pass
    
    def restart_on_failure(self) -> bool:
        """服务异常时自动重启"""
        pass
```

#### DataAdapter
```python
class DataAdapter:
    """数据格式转换适配器"""
    
    def convert_whisperx_to_voicetransl(self, 
                                      whisper_output: WhisperXOutput) -> VoiceTranslInput:
        """WhisperX输出转换为VoiceTransl输入格式"""
        pass
    
    def convert_voicetransl_to_result(self, 
                                    voicetransl_output: VoiceTranslOutput) -> TranslationResult:
        """VoiceTransl输出转换为标准翻译结果"""
        pass
```

## Task 5: 翻译业务逻辑层

### 职责边界
- ✅ 助眠专业术语管理
- ✅ 翻译前预处理
- ✅ 翻译后质量优化
- ✅ 批量翻译流程控制
- ✅ 翻译结果验证
- ❌ 不涉及进程间通信
- ❌ 不涉及数据格式转换

### 对外接口

#### TerminologyService
```python
class TerminologyService:
    """术语管理服务"""
    
    def get_terminology_rules(self, text: str, language: str) -> TerminologyRules:
        """获取文本相关的术语规则"""
        pass
    
    def apply_preprocessing(self, text: str, rules: TerminologyRules) -> str:
        """应用术语预处理"""
        pass
    
    def validate_translation(self, 
                           original: str, 
                           translated: str, 
                           rules: TerminologyRules) -> ValidationResult:
        """验证翻译术语一致性"""
        pass
    
    def force_terminology_replacement(self, 
                                    translated: str, 
                                    rules: TerminologyRules) -> str:
        """强制术语替换"""
        pass
```

#### QualityOptimizer
```python
class QualityOptimizer:
    """翻译质量优化器"""
    
    def analyze_translation_quality(self, 
                                  original: str, 
                                  translated: str) -> QualityScore:
        """分析翻译质量"""
        pass
    
    def suggest_improvements(self, 
                           translation_result: TranslationResult) -> List[Improvement]:
        """提供改进建议"""
        pass
    
    def optimize_for_sleep_content(self, 
                                 translated: str) -> str:
        """针对助眠内容优化"""
        pass
```

#### BatchTranslationController
```python
class BatchTranslationController:
    """批量翻译控制器"""
    
    def create_batch_job(self, 
                        audio_files: List[AudioFile], 
                        config: BatchConfig) -> BatchJob:
        """创建批量翻译任务"""
        pass
    
    def monitor_batch_progress(self, batch_id: str) -> BatchProgress:
        """监控批量任务进度"""
        pass
    
    def handle_batch_errors(self, batch_id: str) -> ErrorReport:
        """处理批量任务错误"""
        pass
```

## 数据模型

### 核心数据类型
```python
@dataclass
class AudioData:
    """音频数据"""
    file_path: str
    segments: List[AudioSegment]
    metadata: Dict[str, Any]

@dataclass  
class TranslationConfig:
    """翻译配置"""
    source_language: str
    target_language: str
    terminology_enabled: bool
    quality_optimization: bool

@dataclass
class TranslationJob:
    """翻译任务"""
    job_id: str
    status: JobStatus
    created_at: datetime
    config: TranslationConfig

@dataclass
class TranslationResult:
    """翻译结果"""
    job_id: str
    original_text: str
    translated_text: str
    quality_score: float
    terminology_applied: List[str]
```

## 接口交互流程

### 标准翻译流程
```
1. Voice-came UI → Task 5: 请求翻译
2. Task 5 → TerminologyService: 获取术语规则
3. Task 5 → Task 4: 调用集成接口翻译
4. Task 4 → VoiceTransl: 进程间通信翻译
5. Task 4 → Task 5: 返回原始翻译结果
6. Task 5 → QualityOptimizer: 质量优化
7. Task 5 → Voice-came UI: 返回优化后结果
```

### 错误处理流程
```
1. Task 4: 检测VoiceTransl进程异常
2. Task 4: 自动重启或故障转移
3. Task 4 → Task 5: 通知业务层错误状态
4. Task 5: 根据业务规则决定重试或终止
```

## 测试策略

### Task 4集成层测试
- 进程管理测试
- 数据转换测试
- 通信协议测试
- 错误恢复测试

### Task 5业务层测试  
- 术语管理测试
- 质量优化测试
- 批量处理测试
- 业务逻辑测试

### 接口契约测试
- 接口兼容性测试
- 数据格式验证测试
- 错误传播测试
- 性能基准测试

## 开发规范

### 接口版本管理
- 使用语义化版本号
- 向后兼容性保证
- 接口变更审核流程

### 错误码规范
- Task 4: 4xxx错误码（集成层错误）
- Task 5: 5xxx错误码（业务层错误）
- 统一错误格式和处理

### 日志规范
- 结构化日志格式
- 跨层日志关联ID
- 性能监控指标

---

**注意**: 本接口契约为Voice-came项目的核心架构文档，所有相关开发必须严格遵循此契约进行实现。 