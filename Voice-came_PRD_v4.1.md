# Voice-came - 产品需求文档

## 1. 简介
本文档概述了Voice-came的全面产品需求。Voice-came是基于VoiceTransl的睡眠内容全球化处理工具，专为助眠故事创作者提供低成本、高效率的多语种翻译服务。该产品需求文档（PRD）为开发团队提供参考，详细说明了功能、用户界面、技术要求和设计准则，以确保产品愿景的一致实现。

## 2. 产品概述
Voice-came是一款专业的睡眠内容处理工具，帮助助眠故事创作者从3-12小时的长视频中提取15-60分钟的有效语音内容，并将其翻译成多种语言以实现全球化传播。该应用程序基于成熟的VoiceTransl架构，集成WhisperX语音处理技术和本地多语种翻译模型，在保持专业术语准确性的同时，显著降低翻译成本和处理时间。

## 3. 目标

### 3.1 主要目标
- 为助眠内容创作者提供高效的语音提取和翻译解决方案
- 实现95%的时间和成本节省（相比手动处理）
- 支持5种核心语言的专业翻译服务
- 保持助眠专业术语的准确性和一致性
- 提供简单易用的批量处理界面

### 3.2 商业目标
- 帮助中国助眠内容创作者拓展全球市场
- 降低内容本土化的技术门槛和成本
- 建立睡眠内容垂直领域的技术优势

## 4. 目标受众

### 4.1 主要用户
- **助眠故事创作者**：拥有大量中文助眠视频内容
- **冥想内容制作者**：需要多语种冥想引导内容
- **睡眠音频工作室**：专业的睡眠内容制作团队
- **内容分发商**：负责睡眠内容全球化分发的机构

### 4.2 用户特征
- 拥有100+个3-12小时的中文助眠视频
- 视频中实际有效语音仅占5-15%
- 希望面向亚洲、欧洲、美洲市场传播
- 对翻译成本和质量都有较高要求
- 技术背景有限，需要简单易用的工具

### 4.3 用户需求
- 快速从长视频中提取有效语音内容
- 保持助眠专业术语的翻译准确性
- 支持批量处理大量视频文件
- 本地化翻译降低API调用成本
- 简单直观的操作界面

## 5. 功能和需求

### 5.1 核心语音处理
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| VP-101 | WhisperX集成 | 集成WhisperX进行语音活动检测和识别 | 高 |
| VP-102 | 语音分离 | 从3-12小时视频中提取15-60分钟有效语音 | 高 |
| VP-103 | 格式支持 | 支持MP4、AVI、MOV等主流视频格式 | 高 |
| VP-104 | 批量处理 | 支持同时处理多个视频文件 | 中 |

### 5.2 翻译引擎
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| TE-101 | 本地翻译 | 配置和使用Gemma3-12B-Q4本地翻译模型 | 高 |
| TE-102 | 在线备选 | 保留VoiceTransl现有在线API作为备选 | 中 |
| TE-103 | 语言支持 | 支持中、英、西、法、德5种核心语言 | 高 |
| TE-104 | 术语处理 | 实现100词助眠术语强制替换 | 高 |
| TE-105 | 接口标准化 | 建立标准化翻译引擎接口，支持多引擎切换 | 高 |

### 5.3 用户界面
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| UI-101 | 文件上传 | 支持拖拽上传和批量选择视频文件 | 高 |
| UI-102 | 处理进度 | 实时显示语音提取和翻译进度 | 高 |
| UI-103 | 结果预览 | 提供翻译结果的预览和编辑功能 | 中 |
| UI-104 | 导出功能 | 支持多种格式的翻译结果导出 | 高 |

### 5.4 术语管理
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| TM-101 | 术语库 | 建立100词核心助眠术语对照表 | 高 |
| TM-102 | 自动替换 | 翻译后自动进行术语强制替换 | 高 |
| TM-103 | 术语编辑 | 允许用户自定义和编辑术语表 | 中 |
| TM-104 | 一致性检查 | 检查同一文档内术语翻译的一致性 | 中 |

### 5.5 质量控制
| 需求编号 | 需求 | 描述 | 优先级 |
|----------------|-------------|-------------|----------|
| QC-101 | 语音质量检测 | 检测提取语音的清晰度和完整性 | 中 |
| QC-102 | 翻译质量评估 | 提供基础的翻译质量评分 | 低 |
| QC-103 | 错误处理 | 完善的错误提示和恢复机制 | 高 |

## 6. 用户故事和验收标准

### 6.1 视频上传和处理

**VP-101：批量视频上传**
- 作为助眠内容创作者，我希望能够批量上传多个视频文件，以便高效处理大量内容。
- **验收标准**：
  - 支持拖拽上传多个视频文件
  - 显示上传进度和文件列表
  - 支持MP4、AVI、MOV等主流格式
  - 文件大小限制提示和验证

**VP-102：语音提取**
- 作为用户，我希望系统能够自动从长视频中提取有效语音内容。
- **验收标准**：
  - WhisperX准确识别语音活动段
  - 过滤掉背景音乐和静音部分
  - 显示提取的语音时长和片段数量
  - 提供语音片段的预览功能

### 6.2 翻译处理

**TE-101：多语种翻译**
- 作为内容创作者，我希望将中文内容翻译成多种语言，以便全球化传播。
- **验收标准**：
  - 支持中译英、西、法、德等5种语言
  - 正确配置和调用本地Gemma3-12B模型
  - 翻译过程显示实时进度
  - 完成后提供翻译质量概览

**TE-102：术语准确性**
- 作为专业内容创作者，我希望助眠专业术语能够准确翻译。
- **验收标准**：
  - 100词核心术语表自动生效
  - 术语替换后显示替换统计
  - 支持用户自定义术语对照
  - 保持术语在同一文档内的一致性

### 6.3 结果管理

**UI-103：结果预览和编辑**
- 作为用户，我希望能够预览和编辑翻译结果。
- **验收标准**：
  - 分段显示原文和译文对照
  - 支持在线编辑译文内容
  - 提供术语高亮显示
  - 保存编辑历史和版本

**UI-104：导出功能**
- 作为用户，我希望能够导出翻译结果用于后续制作。
- **验收标准**：
  - 支持TXT、SRT、JSON等格式导出
  - 保持时间轴信息完整
  - 支持单语言或多语言对照导出
  - 文件命名规则清晰

### 6.4 错误处理

**QC-103：异常情况处理**
- 作为用户，我希望在处理过程中遇到错误时能够得到清晰的提示。
- **验收标准**：
  - 视频格式不支持时显示明确错误信息
  - 翻译失败时提供重试选项
  - 网络中断时保存处理进度
  - 提供常见问题的解决建议

## 7. 技术要求

### 7.1 技术架构
| 需求编号 | 需求 | 描述 |
|----------------|-------------|-------------|
| TA-101 | 基础架构 | 基于VoiceTransl现有架构进行扩展 |
| TA-102 | 语音处理 | 集成WhisperX进行语音识别和分离 |
| TA-103 | 翻译引擎 | 通过标准化接口集成多种翻译引擎，支持本地和在线模式切换 |
| TA-104 | 存储系统 | 支持大文件的临时存储和处理 |

### 7.2 性能要求
| 需求编号 | 需求 | 描述 |
|----------------|-------------|-------------|
| PR-101 | 处理速度 | 3小时视频处理时间不超过30分钟 |
| PR-102 | 并发处理 | 支持同时处理3-5个视频文件 |
| PR-103 | 内存管理 | 合理管理大文件处理时的内存占用 |
| PR-104 | 错误恢复 | 支持处理中断后的断点续传 |

### 7.3 兼容性要求
| 需求编号 | 需求 | 描述 |
|----------------|-------------|-------------|
| CR-101 | 操作系统 | 支持Windows、macOS、Linux |
| CR-102 | 硬件要求 | 最低8GB内存，推荐16GB |
| CR-103 | GPU支持 | 支持NVIDIA、AMD、Intel显卡加速 |
| CR-104 | 文件格式 | 支持主流视频和音频格式 |



## 8. 成功指标

### 8.1 性能指标
- **处理效率**：相比手动处理节省90%以上时间
- **翻译准确率**：核心术语翻译准确率达到90%以上
- **系统稳定性**：连续处理10个文件无崩溃
- **用户满意度**：测试用户满意度达到4.0/5.0以上

### 8.2 商业指标
- **成本节省**：相比纯在线API节省70%以上费用
- **用户采用率**：测试用户中80%愿意正式使用
- **处理量提升**：用户内容处理量提升5倍以上

---

## 说明

## 9. MVP迭代计划

### 9.1 MVP功能范围

#### 核心功能（必须实现）
- **WhisperX语音处理集成**：集成WhisperX引擎，支持3-12小时长视频处理
- **批量视频处理工作流**：批量导入、队列化串行处理、进度显示
- **本地翻译引擎**：支持Gemma3-12B-Q4和Qwen3模型切换

#### 简化功能（基础实现）
- **智能文件管理系统**：按视频标题创建文件夹，SRT/TXT格式分类存储
- **批量字幕导出功能**：支持SRT、TXT格式，原字幕和翻译字幕独立导出

#### 延后功能（v1.1版本）
- **简化的模型管理系统**：MVP阶段手动配置，提供安装指南
- **Windows平台专用优化**：MVP阶段确保基本兼容性

### 9.2 4周迭代计划

#### Week 1：核心引擎集成
**目标**：建立技术基础，集成WhisperX引擎

**开发任务**：
- 搭建开发环境，安装WhisperX依赖
- 创建whisperx目录结构，编写包装脚本
- 修改app.py集成WhisperX引擎
- 实现批量视频导入和队列化处理
- 添加基础进度显示

**验收标准**：
- WhisperX处理单个视频文件
- 批量导入功能正常
- 队列串行处理可用

#### Week 2：翻译引擎集成
**目标**：建立标准化翻译接口，集成本地翻译模型

**开发任务**：
- 设计和实现翻译引擎标准接口
- 开发VoiceTransl集成适配器
- 集成Gemma3-12B-Q4和Qwen3本地模型
- 实现翻译引擎切换和降级机制
- 端到端功能测试和性能优化

**验收标准**：
- 翻译接口标准化实现
- 本地翻译模型正常工作
- 支持多引擎切换和降级
- 完整处理流程可用

#### Week 3：文件管理和导出
**目标**：完善用户体验，实现文件组织和导出

**开发任务**：
- 实现按视频标题自动创建文件夹
- 开发SRT/TXT格式分类存储
- 实现批量字幕导出功能
- 优化用户界面
- 完整端到端测试

**验收标准**：
- 文件自动组织功能正常
- 支持多格式字幕导出
- 用户界面友好
- 处理100个视频批量任务

#### Week 4：优化和发布
**目标**：性能优化、bug修复、用户验收

**开发任务**：
- 内存使用和处理速度优化
- 完善错误处理和恢复机制
- 全面功能验收测试
- 编写用户指南
- MVP版本发布

**验收标准**：
- 所有核心功能稳定可用
- 性能指标达到预期
- 用户独立完成操作流程
- 具备基本错误处理能力

### 9.3 关键里程碑

| 里程碑 | 时间 | 交付物 | 验收标准 |
|--------|------|--------|----------|
| M1 | Week 1 | WhisperX集成版本 | 单文件处理+批量导入 |
| M2 | Week 2 | 翻译功能集成版本 | 完整处理流程可用 |
| M3 | Week 3 | 功能完整版本 | 所有MVP功能实现 |
| M4 | Week 4 | MVP正式版本 | 用户验收通过 |

### 9.4 成功指标

#### 技术指标
- 处理速度：比VoiceTransl提升3倍以上
- 稳定性：连续处理100个视频无崩溃
- 资源使用：GPU内存使用<8GB

#### 用户体验指标
- 学习成本：新用户30分钟内完成首次配置
- 操作效率：批量处理设置时间<5分钟
- 错误恢复：处理失败后能够快速重试

## 10. 测试方案

### 10.1 测试策略概述

本项目采用**测试驱动开发(TDD)**模式，遵循"红绿重构"循环，确保高质量代码交付。

#### TDD开发流程
- 🔴 **红色阶段**：先编写失败的测试用例
- 🟢 **绿色阶段**：编写最小代码使测试通过  
- 🔵 **重构阶段**：优化代码结构，保持测试通过

#### 测试金字塔架构
```
        /\     E2E Tests (10%)
       /  \    端到端测试 - 用户场景验证
      /____\   
     /      \  Integration Tests (20%) 
    /        \ 集成测试 - 模块间交互
   /__________\
  /            \ Unit Tests (70%)
 /              \ 单元测试 - 功能逻辑验证
/________________\
```

### 10.2 测试分层设计

#### 单元测试 (70% 覆盖目标)
**测试范围**：
- **语音处理模块**：WhisperX集成、语音分离、音频格式转换
- **翻译引擎模块**：本地模型调用、翻译格式化、错误处理
- **术语管理模块**：术语库管理、自动替换、一致性检查
- **文件管理模块**：批量处理、格式验证、导出功能

**质量标准**：
- 代码覆盖率 ≥ 90%
- 执行时间 < 5分钟
- 使用Mock对象隔离外部依赖

#### 集成测试 (20% 覆盖目标)
**测试范围**：
- WhisperX引擎集成测试
- 翻译模型集成测试
- 数据库集成测试
- 文件系统集成测试

**执行策略**：
- 使用测试数据库和临时文件系统
- 模拟真实外部服务交互
- 执行时间控制在10分钟内

#### 端到端测试 (10% 覆盖目标)
**测试场景**：
- 完整用户工作流：视频上传→语音提取→翻译→导出
- 批量处理完整流程
- 错误恢复和重试场景
- 性能和稳定性测试

### 10.3 测试用例覆盖

#### 核心功能测试用例 (175个详细测试用例)

| 模块 | 测试用例数量 | 重点覆盖 |
|------|-------------|---------|
| WhisperX处理 | 45个 | 语音识别、噪音处理、长视频处理 |
| 翻译引擎 | 38个 | 多语种翻译、模型切换、API降级 |
| 术语管理 | 25个 | 术语库管理、自动替换、冲突处理 |
| 用户界面 | 32个 | 文件上传、进度显示、结果预览 |
| 质量控制 | 20个 | 语音质量检测、错误处理、系统恢复 |
| 性能测试 | 15个 | 处理速度、内存使用、并发能力 |

#### 错误场景模拟
- **系统级错误**：内存不足、磁盘空间不足、GPU不可用
- **数据级错误**：文件损坏、格式不支持、编码错误
- **业务级错误**：翻译服务异常、语音识别失败、术语冲突

### 10.4 测试数据管理

#### 测试数据生成器
自动生成多类型测试数据：
- **视频样本**：5分钟-3小时不同规格测试视频
- **音频样本**：清晰语音、带噪音语音、混合内容音频
- **翻译样本**：中文助眠内容、专业术语集、边界情况文本
- **术语库**：100词核心术语表、自定义术语、冲突术语
- **批量处理数据**：10个不同规格视频文件
- **无效文件**：损坏文件、错误格式、空文件

#### 数据目录结构
```
test_data/
├── video_samples/           # 视频测试文件
├── audio_samples/          # 音频测试文件
├── translation_samples/    # 翻译测试数据
├── terminology/            # 术语库测试数据
├── batch_processing/       # 批量处理测试数据
├── invalid_files/          # 无效文件测试数据
└── performance_test/       # 性能测试数据
```

### 10.5 测试执行计划

#### TDD开发循环 (每功能75分钟)
1. 分析需求 (5分钟)
2. 编写失败测试 (15分钟)  
3. 运行测试确认失败 (2分钟)
4. 编写最小实现代码 (30分钟)
5. 运行测试直到通过 (5分钟)
6. 重构优化代码 (15分钟)
7. 回归测试确认 (3分钟)

#### 持续集成测试
**代码提交触发**：
- 单元测试套件 (5分钟)
- 代码质量检查 (2分钟)
- 集成测试子集 (10分钟)

**每日构建触发**：
- 完整测试套件 (30分钟)
- 性能基准测试 (60分钟)
- 测试报告生成 (5分钟)

### 10.6 质量门禁标准

#### 代码质量门禁
- **单元测试覆盖率** ≥ 90%
- **集成测试通过率** = 100%
- **代码复杂度** ≤ 10 (圈复杂度)
- **代码重复率** ≤ 3%

#### 功能质量门禁
- **核心功能测试** 100%通过
- **性能基准测试** 满足PRD要求（3小时视频≤30分钟处理）
- **错误处理测试** 覆盖所有异常场景
- **用户验收测试** 满意度≥4.0/5.0

#### 发布质量门禁
- **完整测试套件** 100%通过
- **性能压力测试** 满足连续处理100文件
- **兼容性测试** 支持Windows/macOS/Linux
- **用户手册验证** 新用户30分钟完成配置

### 10.7 测试工具和环境

#### 测试框架
- **pytest** - 主要测试框架
- **pytest-cov** - 覆盖率统计
- **pytest-mock** - Mock对象支持
- **pytest-benchmark** - 性能基准测试

#### 测试环境配置
```bash
# 环境变量
export TEST_MODE=true
export GPU_MEMORY_LIMIT=4GB
export MAX_CONCURRENT_JOBS=3

# 运行测试
pytest Test-Design/unit-tests/ -v --cov=src
pytest Test-Design/integration-tests/ -v
pytest Test-Design/performance-tests/ --benchmark-only
```

#### 错误模拟工具
- **内存不足模拟器**：模拟大文件处理时内存耗尽
- **网络故障模拟器**：模拟网络中断和连接失败
- **GPU不可用模拟器**：模拟GPU驱动问题
- **文件损坏模拟器**：模拟各种文件异常情况

### 10.8 测试文档结构

测试设计文档位于 `Test-Design/` 目录：
- **test-strategy.md** - 详细测试策略和方法论
- **test-cases.md** - 175个详细测试用例
- **mock-data-generator.py** - 测试数据生成器
- **error-simulation.py** - 错误模拟测试工具
- **unit-tests/** - 单元测试代码
- **integration-tests/** - 集成测试代码
- **performance-tests/** - 性能测试代码

### 10.9 测试成功指标

#### 测试效率指标
- **缺陷发现率** ≥ 85%
- **缺陷逃逸率** ≤ 10%
- **回归测试自动化率** ≥ 80%
- **测试执行效率** = 自动化用例 / 总用例 × 100%

#### 质量保证指标
- **代码覆盖率** ≥ 90%
- **测试通过率** = 100%
- **性能基准达成率** = 100%
- **用户验收满意度** ≥ 4.0/5.0

TDD测试方案确保Voice-came项目在每个开发阶段都有充分的质量保证，通过全面的测试覆盖和严格的质量门禁，保障产品的高质量交付。

---

本PRD基于当前已知信息编写，所有未确认的技术能力和性能数据都已明确标注。开发计划将根据第1周技术验证结果进行调整，确保项目的可行性和成功交付。 