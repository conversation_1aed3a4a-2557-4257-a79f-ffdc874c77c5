#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件验证器覆盖率测试用例

专门针对未覆盖的代码行编写测试，确保达到100%覆盖率。
包括：文件头检查、验证摘要、成功验证路径等。
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from voice_came.core.file_validator import FileValidator, ValidationResult


class TestFileValidatorCoverage:
    """文件验证器覆盖率测试类"""
    
    @pytest.fixture
    def validator_with_header_check(self):
        """启用文件头检查的验证器"""
        config = {'check_file_header': True}
        return FileValidator(config=config)
    
    @pytest.fixture
    def validator_no_header_check(self):
        """禁用文件头检查的验证器"""
        config = {'check_file_header': False}
        return FileValidator(config=config)
    
    # === 测试成功验证路径 (覆盖129-136行) ===
    
    def test_successful_validation_path(self, validator_no_header_check, tmp_path):
        """测试成功验证的完整路径，覆盖logger.debug调用"""
        # 创建一个有效的测试文件
        test_file = tmp_path / "valid_test.mp4"
        test_file.write_bytes(b"test content for valid file")
        
        # 使用日志记录来确保覆盖logger.debug调用
        with patch('voice_came.core.file_validator.logger') as mock_logger:
            result = validator_no_header_check.validate_file(test_file)
            
            # 验证debug方法被调用
            mock_logger.debug.assert_called_once()
            assert f"文件验证通过: {test_file}" in mock_logger.debug.call_args[0][0]
        
        assert result.is_valid is True
        assert result.file_format == "mp4"
        assert result.file_size > 0
        assert len(result.errors) == 0
    
    def test_successful_validation_with_header_check(self, validator_with_header_check, tmp_path):
        """测试启用文件头检查的成功验证路径"""
        # 创建一个有正确文件头的有效文件
        test_file = tmp_path / "valid_with_header.wav"
        # WAV文件正确的RIFF头
        wav_header = b'RIFF' + b'\x00' * 100
        test_file.write_bytes(wav_header)
        
        # 确保覆盖成功的验证路径
        with patch('voice_came.core.file_validator.logger') as mock_logger:
            result = validator_with_header_check.validate_file(test_file)
            
            # 验证debug方法被调用
            mock_logger.debug.assert_called_once()
        
        assert result.is_valid is True
        assert result.file_format == "wav"
        assert result.file_size > 0
        assert len(result.errors) == 0
    
    # === 测试文件头检查功能 (覆盖195-211行) ===
    
    def test_file_header_check_no_signatures(self, validator_with_header_check, tmp_path):
        """测试没有签名要求的格式默认通过"""
        # 创建一个没有签名要求的格式文件 (假设flac没有签名)
        test_file = tmp_path / "test.flac"
        test_file.write_bytes(b"fake flac content")
        
        # 临时添加flac到支持格式但不添加签名
        validator_with_header_check.SUPPORTED_FORMATS.append('flac')
        
        result = validator_with_header_check.validate_file(test_file)
        
        assert result.is_valid is True  # 没有签名要求应该通过
        
        # 清理
        validator_with_header_check.SUPPORTED_FORMATS.remove('flac')
    
    def test_file_header_check_valid_signature(self, validator_with_header_check, tmp_path):
        """测试有效文件头签名"""
        # 创建一个MP4文件，使用正确的文件头
        test_file = tmp_path / "test.mp4"
        # 使用正确的MP4签名：b'\x00\x00\x00\x18ftypisom'
        mp4_header = b'\x00\x00\x00\x18ftypisom' + b'\x00' * 100
        test_file.write_bytes(mp4_header)
        
        result = validator_with_header_check.validate_file(test_file)
        
        assert result.is_valid is True
    
    def test_file_header_check_invalid_signature(self, validator_with_header_check, tmp_path):
        """测试无效文件头签名"""
        # 创建一个MP4文件，但使用错误的文件头
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"invalid mp4 header content")
        
        result = validator_with_header_check.validate_file(test_file)
        
        assert result.is_valid is False
        assert "文件头验证失败" in result.errors[0]
    
    def test_file_header_check_read_exception(self, validator_with_header_check, tmp_path):
        """测试文件头检查时的读取异常"""
        test_file = tmp_path / "test.mp4"
        test_file.write_bytes(b"some content")
        
        # Mock文件读取异常
        with patch('builtins.open', side_effect=IOError("Read error")):
            # 直接调用内部方法测试异常处理
            result = validator_with_header_check._check_file_header(test_file, "mp4")
            
            assert result is False  # 异常时应该返回False
    
    def test_file_header_multiple_signatures(self, validator_with_header_check, tmp_path):
        """测试多个签名的匹配"""
        # 测试MP3文件的多个可能签名
        test_file = tmp_path / "test.mp3"
        # 使用ID3签名
        mp3_header = b'ID3' + b'\x03\x00' + b'\x00' * 100
        test_file.write_bytes(mp3_header)
        
        result = validator_with_header_check.validate_file(test_file)
        
        assert result.is_valid is True
    
    # === 测试验证摘要功能 (覆盖239-258行) ===
    
    def test_validation_summary_with_errors(self, validator_no_header_check, tmp_path):
        """测试包含错误的验证摘要"""
        # 创建混合结果集
        results = []
        
        # 创建有效文件
        valid_file = tmp_path / "valid.mp4"
        valid_file.write_bytes(b"valid content")
        valid_result = validator_no_header_check.validate_file(valid_file)
        results.append(valid_result)
        
        # 创建无效文件（不存在）
        invalid_file = tmp_path / "nonexistent.mp4"
        invalid_result = validator_no_header_check.validate_file(invalid_file)
        results.append(invalid_result)
        
        # 创建另一个无效文件（无扩展名）
        no_ext_file = tmp_path / "noext"
        no_ext_file.write_bytes(b"content")
        no_ext_result = validator_no_header_check.validate_file(no_ext_file)
        results.append(no_ext_result)
        
        # 获取验证摘要
        summary = validator_no_header_check.get_validation_summary(results)
        
        assert summary['total_files'] == 3
        assert summary['valid_files'] == 1
        assert summary['invalid_files'] == 2
        assert summary['success_rate'] == 1/3
        
        # 检查错误类型统计
        assert '文件不存在' in summary['error_types']
        assert '文件无扩展名' in summary['error_types']
        
        # 检查格式分布
        assert 'mp4' in summary['format_distribution']
        assert summary['format_distribution']['mp4'] == 1
    
    def test_validation_summary_error_with_colon(self, validator_no_header_check):
        """测试带冒号的错误消息统计"""
        # 创建带冒号的错误结果
        results = []
        
        # 模拟一个复杂错误消息
        result = ValidationResult(
            file_path=Path("test.mp4"),
            is_valid=False,
            file_format="mp4",
            file_size=0,
            errors=["验证过程发生错误: 复杂的错误信息"]
        )
        results.append(result)
        
        summary = validator_no_header_check.get_validation_summary(results)
        
        # 检查错误类型提取（冒号前的部分）
        assert '验证过程发生错误' in summary['error_types']
        assert summary['error_types']['验证过程发生错误'] == 1
    
    def test_validation_summary_empty_results(self, validator_no_header_check):
        """测试空结果列表的摘要"""
        summary = validator_no_header_check.get_validation_summary([])
        
        assert summary['total_files'] == 0
        assert summary['valid_files'] == 0
        assert summary['invalid_files'] == 0
        assert summary['success_rate'] == 0
        assert summary['error_types'] == {}
        assert summary['format_distribution'] == {}
    
    def test_validation_summary_all_valid(self, validator_no_header_check, tmp_path):
        """测试全部有效文件的摘要"""
        results = []
        
        # 创建多个不同格式的有效文件
        for ext in ['mp4', 'avi', 'wav']:
            test_file = tmp_path / f"test.{ext}"
            test_file.write_bytes(b"valid content")
            result = validator_no_header_check.validate_file(test_file)
            results.append(result)
        
        summary = validator_no_header_check.get_validation_summary(results)
        
        assert summary['total_files'] == 3
        assert summary['valid_files'] == 3
        assert summary['invalid_files'] == 0
        assert summary['success_rate'] == 1.0
        
        # 检查格式分布
        assert len(summary['format_distribution']) == 3
        assert summary['format_distribution']['mp4'] == 1
        assert summary['format_distribution']['avi'] == 1
        assert summary['format_distribution']['wav'] == 1
    
    # === 测试其他边界情况 ===
    
    def test_file_header_check_partial_match(self, validator_with_header_check, tmp_path):
        """测试部分匹配的文件头"""
        test_file = tmp_path / "test.wav"
        # WAV文件需要RIFF头，但只给部分
        wav_header = b'RI' + b'\x00' * 50  # 不完整的RIFF
        test_file.write_bytes(wav_header)
        
        result = validator_with_header_check.validate_file(test_file)
        
        assert result.is_valid is False
        assert "文件头验证失败" in result.errors[0]
    
    def test_validation_result_errors_initialization(self):
        """测试ValidationResult的错误列表初始化"""
        # 测试默认初始化
        result1 = ValidationResult(file_path=Path("test.mp4"), is_valid=True)
        assert result1.errors == []
        
        # 测试显式None初始化
        result2 = ValidationResult(
            file_path=Path("test.mp4"), 
            is_valid=True, 
            errors=None
        )
        assert result2.errors == []
        
        # 测试预定义错误列表
        result3 = ValidationResult(
            file_path=Path("test.mp4"), 
            is_valid=False, 
            errors=["预定义错误"]
        )
        assert result3.errors == ["预定义错误"] 