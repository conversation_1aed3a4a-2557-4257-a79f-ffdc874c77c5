#!/bin/bash
# Voice-came macOS/Linux 环境设置脚本
# 支持 macOS 和主流 Linux 发行版

set -e  # 遇到错误时退出

echo "==============================================="
echo "  Voice-came 语音翻译系统 - Unix 环境设置"
echo "==============================================="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_info "检测到操作系统：macOS"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        log_info "检测到操作系统：Linux"
        
        # 检测Linux发行版
        if command -v lsb_release &> /dev/null; then
            DISTRO=$(lsb_release -si)
        elif [ -f /etc/os-release ]; then
            DISTRO=$(grep '^NAME=' /etc/os-release | cut -d'"' -f2)
        else
            DISTRO="Unknown"
        fi
        log_info "Linux发行版：$DISTRO"
    else
        log_error "不支持的操作系统：$OSTYPE"
        exit 1
    fi
}

# 检查Python版本
check_python() {
    log_info "检查Python环境..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        log_error "未找到Python，请先安装Python 3.8-3.11"
        if [[ "$OS" == "macos" ]]; then
            log_info "macOS安装命令："
            echo "  brew install python@3.10"
            echo "  或从 https://www.python.org/downloads/ 下载"
        else
            log_info "Linux安装命令："
            echo "  Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip"
            echo "  CentOS/RHEL: sudo yum install python3 python3-pip"
            echo "  Fedora: sudo dnf install python3 python3-pip"
        fi
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    log_success "Python版本：$PYTHON_VERSION"
    
    # 验证版本范围
    if $PYTHON_CMD -c "import sys; exit(0 if 3.8 <= sys.version_info[:2] <= (3, 11) else 1)"; then
        log_success "Python版本检查通过"
    else
        log_error "需要Python 3.8-3.11，当前版本：$PYTHON_VERSION"
        exit 1
    fi
}

# 检查pip
check_pip() {
    log_info "检查pip..."
    
    if $PYTHON_CMD -m pip --version &> /dev/null; then
        log_success "pip可用"
    else
        log_error "pip未正确安装"
        exit 1
    fi
}

# 检查GPU支持
check_gpu() {
    log_info "检查GPU支持..."
    
    if command -v nvidia-smi &> /dev/null; then
        log_success "检测到NVIDIA GPU"
        nvidia-smi | grep "CUDA Version" || true
        GPU_SUPPORT=true
    else
        log_info "未检测到NVIDIA GPU，将使用CPU版本"
        GPU_SUPPORT=false
    fi
}

# 创建虚拟环境和安装依赖
install_dependencies() {
    echo
    echo "==============================================="
    echo "  开始安装Python依赖"
    echo "==============================================="
    echo
    
    read -p "是否创建Python虚拟环境？(推荐, y/n): " create_venv
    if [[ $create_venv == "y" || $create_venv == "Y" ]]; then
        log_info "创建虚拟环境 'voice-came-env'..."
        $PYTHON_CMD -m venv voice-came-env
        
        log_info "激活虚拟环境..."
        source voice-came-env/bin/activate
        log_success "虚拟环境已激活"
        
        VENV_CREATED=true
    else
        VENV_CREATED=false
    fi
    
    log_info "运行Python依赖安装脚本..."
    $PYTHON_CMD scripts/install_dependencies.py
}

# 主函数
main() {
    detect_os
    echo
    
    check_python
    echo
    
    check_pip
    echo
    
    check_gpu
    echo
    
    install_dependencies
    
    echo
    echo "==============================================="
    echo "  安装完成！"
    echo "==============================================="
    echo
    
    if [[ $VENV_CREATED == true ]]; then
        echo "下次使用前，请激活虚拟环境："
        echo "  source voice-came-env/bin/activate"
        echo
    fi
    
    echo "测试安装："
    echo "  python test_installation.py"
    echo
    
    echo "开始使用Voice-came系统！"
    echo
}

# 运行主函数
main "$@" 