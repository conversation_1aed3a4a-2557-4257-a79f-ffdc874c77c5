#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 2.8: 批量处理队列最小实现 (TDD-Green阶段)

TDD-Green阶段验证：
1. 验证实现能够使之前的失败测试通过
2. 验证基础队列数据结构
3. 验证并发控制逻辑
4. 验证状态跟踪机制
5. 验证队列持久化功能

目标：所有测试都应该通过！
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock
from pathlib import Path

# TDD-Green阶段：导入实现的模块
from voice_came.core.enhanced_batch_queue import (
    EnhancedBatchQueue, 
    QueueJob, 
    JobState,
    QueueManager
)


class TestEnhancedBatchQueueTDDGreen:
    """增强批量处理队列TDD-Green阶段测试"""
    
    @pytest.fixture
    def enhanced_queue(self):
        """创建增强批量队列"""
        return EnhancedBatchQueue(max_concurrent=4)
    
    @pytest.fixture
    def test_files(self, tmp_path):
        """创建测试文件"""
        files = []
        for i in range(6):
            file_path = tmp_path / f"test_{i}.mp4"
            file_path.write_bytes(b"test data" * 100)
            files.append(file_path)
        return files
    
    # === 1. 队列添加和移除测试 ===
    
    def test_add_job_to_queue_passes(self, enhanced_queue, test_files):
        """测试添加任务到队列（TDD-Green：应该通过）"""
        job = QueueJob(
            id="test_job_1",
            file_path=test_files[0],
            job_type="video_processing"
        )
        
        # TDD-Green：此操作应该成功
        job_id = enhanced_queue.add_job(job)
        assert job_id == "test_job_1"
        assert enhanced_queue.size() == 1
        assert job.status == JobState.PENDING
    
    def test_remove_job_from_queue_passes(self, enhanced_queue, test_files):
        """测试从队列移除任务（TDD-Green：应该通过）"""
        job = QueueJob(id="removable", file_path=test_files[0])
        enhanced_queue.add_job(job)
        assert enhanced_queue.size() == 1
        
        # TDD-Green：此操作应该成功
        removed = enhanced_queue.remove_job("removable")
        assert removed.id == "removable"
        assert enhanced_queue.size() == 0
    
    def test_remove_nonexistent_job_fails(self, enhanced_queue):
        """测试移除不存在的任务应该失败"""
        with pytest.raises(KeyError):
            enhanced_queue.remove_job("nonexistent")
    
    # === 2. 并发控制测试 ===
    
    def test_concurrent_limit_enforcement_passes(self, enhanced_queue, test_files):
        """测试并发限制执行（TDD-Green：应该通过）"""
        # 添加超过并发限制的任务
        for i, file_path in enumerate(test_files):
            job = QueueJob(f"concurrent_{i}", file_path)
            enhanced_queue.add_job(job)
        
        assert enhanced_queue.size() == 6
        
        enhanced_queue.start()
        time.sleep(0.2)  # 等待任务开始处理
        
        # TDD-Green：并发控制应该正常工作
        assert enhanced_queue.get_running_count() <= 4
        assert enhanced_queue.get_pending_count() >= 2
        
        enhanced_queue.stop()
    
    def test_queue_counts(self, enhanced_queue, test_files):
        """测试队列计数功能"""
        # 初始状态
        assert enhanced_queue.get_running_count() == 0
        assert enhanced_queue.get_pending_count() == 0
        
        # 添加任务
        for i, file_path in enumerate(test_files[:3]):
            job = QueueJob(f"count_test_{i}", file_path)
            enhanced_queue.add_job(job)
        
        assert enhanced_queue.get_pending_count() == 3
    
    # === 3. 队列状态管理测试 ===
    
    def test_job_status_transitions_pass(self, enhanced_queue, test_files):
        """测试任务状态转换（TDD-Green：应该通过）"""
        job = QueueJob("status_test", test_files[0])
        enhanced_queue.add_job(job)
        
        assert job.status == JobState.PENDING
        
        enhanced_queue.start()
        time.sleep(0.1)  # 等待任务开始
        
        # TDD-Green：状态转换应该正常工作
        assert job.status == JobState.RUNNING
        
        time.sleep(0.5)  # 等待任务完成
        assert job.status == JobState.COMPLETED
        
        enhanced_queue.stop()
    
    def test_job_properties(self, test_files):
        """测试任务属性"""
        job = QueueJob("prop_test", test_files[0], job_type="test_type")
        
        assert job.id == "prop_test"
        assert job.file_path == test_files[0]
        assert job.job_type == "test_type"
        assert job.status == JobState.PENDING
        assert job.progress == 0.0
        assert job.created_at > 0
        assert job.started_at is None
        assert job.completed_at is None
    
    # === 4. 进度跟踪测试 ===
    
    def test_progress_tracking_passes(self, enhanced_queue, test_files):
        """测试进度跟踪（TDD-Green：应该通过）"""
        progress_updates = []
        
        def progress_callback(job_id, progress):
            progress_updates.append((job_id, progress))
        
        enhanced_queue.set_progress_callback(progress_callback)
        
        job = QueueJob("progress_test", test_files[0])
        enhanced_queue.add_job(job)
        enhanced_queue.start()
        
        time.sleep(0.6)  # 等待任务完成
        
        # TDD-Green：进度跟踪应该正常工作
        assert len(progress_updates) > 0
        
        # 检查最后的进度是100%
        final_progress = progress_updates[-1][1] if progress_updates else 0
        assert final_progress == 100.0
        
        enhanced_queue.stop()
    
    def test_progress_callback_setting(self, enhanced_queue):
        """测试进度回调设置"""
        callback_mock = Mock()
        enhanced_queue.set_progress_callback(callback_mock)
        
        assert enhanced_queue._progress_callback == callback_mock
    
    # === 5. 队列持久化测试 ===
    
    def test_queue_persistence_passes(self, tmp_path, test_files):
        """测试队列持久化（TDD-Green：应该通过）"""
        state_file = tmp_path / "queue_state.json"
        
        queue = EnhancedBatchQueue(
            max_concurrent=3,
            state_file=state_file
        )
        
        # 添加任务
        for i, file_path in enumerate(test_files[:3]):
            job = QueueJob(f"persist_{i}", file_path)
            queue.add_job(job)
        
        assert queue.size() == 3
        
        # TDD-Green：持久化应该成功
        queue.save_state()
        assert state_file.exists()
        
        # 验证文件内容
        import json
        with open(state_file, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        assert "jobs" in state_data
        assert "metadata" in state_data
        assert len(state_data["jobs"]) == 3
        
        # 创建新队列实例并加载状态
        new_queue = EnhancedBatchQueue(state_file=state_file)
        assert new_queue.size() == 3
        
        # 验证任务已正确加载
        for i in range(3):
            job_id = f"persist_{i}"
            # 通过移除操作验证任务存在
            try:
                job = new_queue.remove_job(job_id)
                assert job.id == job_id
            except KeyError:
                pytest.fail(f"任务 {job_id} 未正确加载")
    
    def test_queue_without_state_file(self):
        """测试不使用状态文件的队列"""
        queue = EnhancedBatchQueue()
        
        # save_state 应该安全返回
        queue.save_state()  # 不应该抛出异常
        
        assert queue.state_file is None


@pytest.mark.tdd_green
class TestTDDGreenValidation:
    """TDD-Green阶段验证"""
    
    def test_modules_implemented(self):
        """验证模块已实现（TDD-Green阶段验证）"""
        # 应该能够成功导入
        from voice_came.core.enhanced_batch_queue import (
            EnhancedBatchQueue, QueueJob, JobState
        )
        
        # 应该能够创建实例
        queue = EnhancedBatchQueue()
        assert queue is not None
        
        job = QueueJob("test", "/tmp/test.mp4")
        assert job is not None
    
    def test_basic_functionality_works(self):
        """验证基础功能工作"""
        queue = EnhancedBatchQueue(max_concurrent=2)
        
        # 测试添加任务
        job1 = QueueJob("job1", "/tmp/test1.mp4")
        job2 = QueueJob("job2", "/tmp/test2.mp4")
        
        queue.add_job(job1)
        queue.add_job(job2)
        
        assert queue.size() == 2
        assert queue.get_pending_count() == 2
        assert queue.get_running_count() == 0
        
        # 测试移除任务
        removed = queue.remove_job("job1")
        assert removed.id == "job1"
        assert queue.size() == 1


if __name__ == "__main__":
    print("🟢 执行TDD-Green阶段：批量处理队列最小实现验证")
    print("预期结果：所有测试都应该通过！")
    pytest.main([__file__, "-v"]) 