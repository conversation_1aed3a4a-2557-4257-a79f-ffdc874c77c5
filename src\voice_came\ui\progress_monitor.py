#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 4.4: 实时进度监控组件 (TDD-Green最小实现)

提供翻译任务的实时进度监控界面，包括整体进度、
阶段进度、错误警告显示等功能。
"""

import asyncio
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


@dataclass
class ProgressUpdate:
    """进度更新数据"""
    timestamp: datetime
    overall_progress: float
    current_file: str
    processing_stage: str
    stage_progress: Dict[str, float] = field(default_factory=dict)


@dataclass
class ErrorMessage:
    """错误消息数据"""
    timestamp: datetime
    level: str  # "error", "warning", "info"
    message: str
    details: Optional[str] = None


class RealTimeProgressMonitor:
    """实时进度监控器 - TDD Green最小实现"""
    
    def __init__(self):
        self.current_progress: ProgressUpdate = ProgressUpdate(
            timestamp=datetime.now(),
            overall_progress=0.0,
            current_file="",
            processing_stage="待开始"
        )
        self.error_history: List[ErrorMessage] = []
        self.is_monitoring = False
        self.update_callbacks: List[Callable] = []
        self.monitoring_task: Optional[asyncio.Task] = None
    
    def update_overall_progress(self, progress: float) -> None:
        """更新整体进度"""
        # TDD Green: 最小实现，更新进度值
        self.current_progress.overall_progress = max(0.0, min(1.0, progress))
        self.current_progress.timestamp = datetime.now()
        
        logger.info(f"Overall progress updated: {progress:.1%}")
        self._notify_callbacks()
    
    def update_current_file(self, filename: str) -> None:
        """更新当前处理文件"""
        # TDD Green: 最小实现，更新文件名
        self.current_progress.current_file = filename
        self.current_progress.timestamp = datetime.now()
        
        logger.info(f"Current file updated: {filename}")
        self._notify_callbacks()
    
    def update_processing_stage(self, stage: str) -> None:
        """更新处理阶段"""
        # TDD Green: 最小实现，更新阶段信息
        self.current_progress.processing_stage = stage
        self.current_progress.timestamp = datetime.now()
        
        logger.info(f"Processing stage updated: {stage}")
        self._notify_callbacks()
    
    def update_stage_progress(self, progress_data: Dict[str, float]) -> None:
        """更新各阶段详细进度"""
        # TDD Green: 最小实现，更新阶段进度
        self.current_progress.stage_progress = progress_data.copy()
        self.current_progress.timestamp = datetime.now()
        
        logger.info(f"Stage progress updated: {progress_data}")
        self._notify_callbacks()
    
    def get_current_progress(self) -> Dict[str, Any]:
        """获取当前进度状态"""
        # TDD Green: 最小实现，返回进度字典
        return {
            "overall_progress": self.current_progress.overall_progress,
            "current_file": self.current_progress.current_file,
            "processing_stage": self.current_progress.processing_stage,
            "stage_progress": self.current_progress.stage_progress,
            "timestamp": self.current_progress.timestamp.isoformat(),
            "is_monitoring": self.is_monitoring
        }
    
    def show_error(self, message: str, details: Optional[str] = None) -> None:
        """显示错误信息"""
        # TDD Green: 最小实现，记录错误
        error = ErrorMessage(
            timestamp=datetime.now(),
            level="error",
            message=message,
            details=details
        )
        
        self.error_history.append(error)
        logger.error(f"Error: {message}")
        
        # 保持错误历史在合理范围内
        if len(self.error_history) > 100:
            self.error_history = self.error_history[-50:]
        
        self._notify_callbacks()
    
    def show_warning(self, message: str, details: Optional[str] = None) -> None:
        """显示警告信息"""
        # TDD Green: 最小实现，记录警告
        warning = ErrorMessage(
            timestamp=datetime.now(),
            level="warning",
            message=message,
            details=details
        )
        
        self.error_history.append(warning)
        logger.warning(f"Warning: {message}")
        
        self._notify_callbacks()
    
    def show_info(self, message: str, details: Optional[str] = None) -> None:
        """显示信息"""
        # TDD Green: 最小实现，记录信息
        info = ErrorMessage(
            timestamp=datetime.now(),
            level="info",
            message=message,
            details=details
        )
        
        self.error_history.append(info)
        logger.info(f"Info: {message}")
        
        self._notify_callbacks()
    
    def get_error_history(self) -> List[Dict[str, Any]]:
        """获取错误历史"""
        # TDD Green: 最小实现，返回错误历史
        return [
            {
                "timestamp": error.timestamp.isoformat(),
                "level": error.level,
                "message": error.message,
                "details": error.details
            }
            for error in self.error_history
        ]
    
    def clear_error_history(self) -> None:
        """清空错误历史"""
        # TDD Green: 最小实现，清空历史
        self.error_history.clear()
        logger.info("Error history cleared")
        self._notify_callbacks()
    
    async def start_real_time_updates(self, job_id: str) -> None:
        """启动实时更新"""
        # TDD Green: 最小实现，启动监控
        if self.is_monitoring:
            logger.warning("Real-time monitoring already active")
            return
        
        self.is_monitoring = True
        logger.info(f"Starting real-time monitoring for job: {job_id}")
        
        # 启动监控任务
        self.monitoring_task = asyncio.create_task(
            self._monitoring_loop(job_id)
        )
    
    async def stop_real_time_updates(self) -> None:
        """停止实时更新"""
        # TDD Green: 最小实现，停止监控
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        logger.info("Stopping real-time monitoring")
        
        # 取消监控任务
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.monitoring_task = None
    
    async def _monitoring_loop(self, job_id: str) -> None:
        """监控循环"""
        # TDD Green: 最小实现，模拟监控循环
        try:
            while self.is_monitoring:
                # 模拟进度更新
                await asyncio.sleep(1.0)
                
                # 这里在实际实现中会从翻译服务获取真实进度
                # 现在只是模拟进度增长
                current = self.current_progress.overall_progress
                if current < 1.0:
                    self.update_overall_progress(min(1.0, current + 0.05))
                
        except asyncio.CancelledError:
            logger.info("Monitoring loop cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
            self.show_error(f"监控循环错误: {e}")
    
    def add_update_callback(self, callback: Callable) -> None:
        """添加更新回调"""
        # TDD Green: 最小实现，添加回调函数
        if callback not in self.update_callbacks:
            self.update_callbacks.append(callback)
    
    def remove_update_callback(self, callback: Callable) -> None:
        """移除更新回调"""
        # TDD Green: 最小实现，移除回调函数
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)
    
    def _notify_callbacks(self) -> None:
        """通知所有回调函数"""
        # TDD Green: 最小实现，调用所有回调
        for callback in self.update_callbacks:
            try:
                callback(self.get_current_progress())
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")
    
    def reset_progress(self) -> None:
        """重置进度状态"""
        # TDD Green: 最小实现，重置所有状态
        self.current_progress = ProgressUpdate(
            timestamp=datetime.now(),
            overall_progress=0.0,
            current_file="",
            processing_stage="待开始"
        )
        self.error_history.clear()
        
        logger.info("Progress monitor reset")
        self._notify_callbacks()
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """获取进度摘要"""
        # TDD Green: 最小实现，返回进度摘要
        recent_errors = [
            error for error in self.error_history 
            if error.level == "error"
        ][-5:]  # 最近5个错误
        
        recent_warnings = [
            error for error in self.error_history 
            if error.level == "warning"
        ][-5:]  # 最近5个警告
        
        return {
            "overall_progress": self.current_progress.overall_progress,
            "current_file": self.current_progress.current_file,
            "processing_stage": self.current_progress.processing_stage,
            "is_monitoring": self.is_monitoring,
            "total_errors": len([e for e in self.error_history if e.level == "error"]),
            "total_warnings": len([e for e in self.error_history if e.level == "warning"]),
            "recent_errors": [e.message for e in recent_errors],
            "recent_warnings": [e.message for e in recent_warnings],
            "last_update": self.current_progress.timestamp.isoformat()
        }
