# Voice-came 测试依赖包 v1.0
# 基于Voice-came_开发规范_v1.0.md制定，符合TDD开发模式

# ==================== 核心测试框架 ====================
pytest==7.4.3                    # 主要测试框架
pytest-cov==4.1.0               # 覆盖率统计 (目标≥90%)
pytest-html==3.2.0              # HTML测试报告
pytest-mock==3.12.0             # Mock对象支持
pytest-xdist==3.3.1             # 并行测试执行
pytest-benchmark==4.0.0         # 性能基准测试
pytest-timeout==2.1.0           # 测试超时控制
pytest-clarity==1.0.1           # 清晰的断言输出

# ==================== 项目特定依赖 ====================
# 语音处理测试
whisperx==3.1.1                 # WhisperX语音识别引擎
librosa==0.10.1                # 音频处理分析
soundfile==0.12.1              # 音频文件读写
pydub==0.25.1                  # 音频格式转换

# 翻译引擎测试
torch==2.0.1                   # PyTorch框架 (Gemma3/Qwen3支持)
transformers==4.35.0           # Transformer模型库
tokenizers==0.14.1             # 文本分词器
accelerate==0.24.1             # 模型加速库

# 视频处理测试
opencv-python==4.8.1.78        # 计算机视觉
ffmpeg-python==0.2.0           # FFmpeg Python绑定
imageio-ffmpeg==0.4.9          # 视频处理支持

# ==================== 测试数据生成 ====================
factory-boy==3.3.0              # 测试数据工厂
faker==20.1.0                   # 假数据生成器
hypothesis==6.88.1              # 属性测试框架

# ==================== Mock和模拟工具 ====================
responses==0.24.1               # HTTP请求模拟
freezegun==1.2.2               # 时间模拟工具
moto==4.2.12                   # AWS服务模拟

# ==================== 性能和系统监控 ====================
psutil==5.9.6                  # 系统资源监控
memory-profiler==0.61.0        # 内存使用分析
py-spy==0.3.14                 # Python性能分析器
nvidia-ml-py3==7.352.0         # NVIDIA GPU监控

# ==================== 错误模拟和混沌测试 ====================
# 网络故障模拟
netifaces==0.11.0              # 网络接口信息
unittest.mock                   # 内置Mock框架

# ==================== 代码质量检查 ====================
black==23.11.0                  # 代码格式化 (符合开发规范)
flake8==6.1.0                  # 代码风格检查
mypy==1.7.1                    # 类型检查
bandit==1.7.5                  # 安全检查
isort==5.12.0                  # 导入排序 (符合开发规范)

# ==================== 测试报告和可视化 ====================
allure-pytest==2.13.2          # Allure测试报告
pytest-json-report==1.5.0      # JSON格式报告
coverage==7.3.2                # 覆盖率工具

# ==================== 配置和日志 ====================
python-dotenv==1.0.0           # 环境变量管理
pydantic==2.5.0                # 数据验证
pyyaml==6.0.1                  # YAML配置文件
loguru==0.7.2                  # 结构化日志

# ==================== 数据处理 ====================
numpy==1.24.4                  # 数值计算
pandas==2.1.3                  # 数据分析
scipy==1.11.4                  # 科学计算

# ==================== 网络和API测试 ====================
requests==2.31.0               # HTTP客户端
httpx==0.25.2                  # 异步HTTP客户端
aiohttp==3.9.1                 # 异步HTTP服务器/客户端

# ==================== 数据库测试 ====================
sqlalchemy==2.0.23             # 数据库ORM
sqlite3                        # 轻量级数据库（内置）

# ==================== 容器化测试 ====================
docker==6.1.3                  # Docker Python SDK
testcontainers==3.7.1          # 容器化测试工具

# ==================== 开发和调试工具 ====================
ipython==8.17.2                # 交互式Python shell
ipdb==0.13.13                  # 交互式调试器
rich==13.7.0                   # 丰富的终端输出

# ==================== 持续集成工具 ====================
tox==4.11.4                    # 多环境测试
pre-commit==3.6.0              # Git提交前检查

# ==================== 性能基准要求 ====================
# 测试环境硬件要求：
# - 内存：最低8GB，推荐16GB
# - GPU：NVIDIA GTX 1060或更高
# - 磁盘：至少20GB可用空间
# 
# 性能测试目标：
# - 3小时视频处理≤30分钟
# - 单元测试执行时间<5分钟
# - 集成测试执行时间<15分钟
# - 测试覆盖率≥90%
# 
# 注意事项：
# 1. GPU相关包需要CUDA 11.8+支持
# 2. WhisperX需要足够的GPU内存（≥8GB推荐）
# 3. 某些依赖需要系统级安装（FFmpeg等）
# 4. 在CI/CD环境中，部分GPU测试可能需要专用runners

# 符合Voice-came开发规范v1.0：
# - 测试覆盖率≥80% (提升到≥90%)
# - 圈复杂度≤10
# - 函数长度≤50行
# - 类长度≤500行

# 注意：
# 1. 某些包需要系统级依赖，请参考各包的安装文档
# 2. GPU相关包(如torch)需要根据系统CUDA版本选择合适版本
# 3. 部分包可能需要编译，建议使用conda或预编译wheel
# 4. 在CI/CD环境中，某些包可能需要不同的配置 