#!/usr/bin/env python3
"""
Voice-came 语音翻译系统 - 依赖安装脚本

此脚本自动检测系统环境并安装必要的依赖包。
支持Windows、macOS和Linux系统。
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("  Voice-came 语音翻译系统 - 依赖安装脚本")
    print("=" * 60)
    print()

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"📍 检测到Python版本：{version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 8 or version.minor > 11:
        print("❌ 错误：需要Python 3.8-3.11版本")
        print("   请安装支持的Python版本后重试")
        sys.exit(1)
    else:
        print("✅ Python版本检查通过")
    print()

def check_system():
    """检查操作系统"""
    system = platform.system()
    print(f"📍 检测到操作系统：{system}")
    return system

def check_cuda():
    """检查CUDA支持"""
    print("📍 检查CUDA支持...")
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 检测到NVIDIA GPU，将安装CUDA版本的PyTorch")
            return True
        else:
            print("ℹ️  未检测到NVIDIA GPU，将安装CPU版本的PyTorch")
            return False
    except FileNotFoundError:
        print("ℹ️  未找到nvidia-smi，将安装CPU版本的PyTorch")
        return False

def check_conda():
    """检查Conda环境"""
    if shutil.which('conda'):
        print("✅ 检测到Conda环境")
        return True
    else:
        print("ℹ️  未检测到Conda，将使用pip安装")
        return False

def install_with_conda(has_cuda=False):
    """使用Conda安装依赖"""
    print("🔧 使用Conda创建环境...")
    
    try:
        # 创建conda环境
        cmd = ['conda', 'env', 'create', '-f', 'environment.yml', '--force']
        subprocess.run(cmd, check=True)
        
        print("✅ Conda环境创建成功")
        print("📌 激活环境：conda activate voice-came")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Conda安装失败：{e}")
        return False
    
    return True

def install_with_pip(has_cuda=False):
    """使用pip安装依赖"""
    print("🔧 使用pip安装依赖...")
    
    try:
        # 升级pip
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)
        
        # 安装PyTorch
        if has_cuda:
            print("📦 安装CUDA版本的PyTorch...")
            pytorch_cmd = [
                sys.executable, '-m', 'pip', 'install', 
                'torch', 'torchvision', 'torchaudio', 
                '--index-url', 'https://download.pytorch.org/whl/cu121'
            ]
        else:
            print("📦 安装CPU版本的PyTorch...")
            pytorch_cmd = [
                sys.executable, '-m', 'pip', 'install', 
                'torch', 'torchvision', 'torchaudio'
            ]
        
        subprocess.run(pytorch_cmd, check=True)
        
        # 安装其他依赖
        print("📦 安装其他依赖包...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
        
        print("✅ pip安装完成")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ pip安装失败：{e}")
        return False
    
    return True

def verify_installation():
    """验证安装"""
    print("🔍 验证安装...")
    
    try:
        # 测试核心包导入
        import torch
        print(f"✅ PyTorch {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA可用，设备数量：{torch.cuda.device_count()}")
        else:
            print("ℹ️  CUDA不可用，将使用CPU")
        
        # 测试其他核心包
        test_packages = [
            ('whisperx', 'WhisperX'),
            ('transformers', 'Transformers'),
            ('librosa', 'Librosa'),
            ('soundfile', 'SoundFile')
        ]
        
        for package, name in test_packages:
            try:
                __import__(package)
                print(f"✅ {name}")
            except ImportError:
                print(f"⚠️  {name} 未安装或安装失败")
        
    except ImportError as e:
        print(f"❌ 验证失败：{e}")
        return False
    
    return True

def create_test_script():
    """创建测试脚本"""
    test_script = '''#!/usr/bin/env python3
"""
Voice-came 系统测试脚本
"""

def test_imports():
    """测试核心模块导入"""
    try:
        import torch
        print(f"PyTorch: {torch.__version__}")
        
        import whisperx
        print("WhisperX: OK")
        
        import transformers
        print(f"Transformers: {transformers.__version__}")
        
        return True
    except ImportError as e:
        print(f"导入错误: {e}")
        return False

def test_audio_processing():
    """测试音频处理功能"""
    try:
        import librosa
        import soundfile as sf
        print("音频处理库: OK")
        return True
    except ImportError as e:
        print(f"音频处理库错误: {e}")
        return False

if __name__ == "__main__":
    print("Voice-came 系统测试")
    print("-" * 30)
    
    if test_imports() and test_audio_processing():
        print("✅ 所有测试通过！")
    else:
        print("❌ 某些测试失败，请检查安装")
'''
    
    with open('test_installation.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("📄 已创建测试脚本：test_installation.py")

def main():
    """主函数"""
    print_banner()
    
    # 系统检查
    check_python_version()
    system = check_system()
    has_cuda = check_cuda()
    has_conda = check_conda()
    
    print()
    
    # 选择安装方式
    if has_conda:
        choice = input("💡 选择安装方式 (1: Conda, 2: pip): ").strip()
        use_conda = choice == '1'
    else:
        use_conda = False
    
    print()
    
    # 执行安装
    if use_conda:
        success = install_with_conda(has_cuda)
    else:
        success = install_with_pip(has_cuda)
    
    if not success:
        print("❌ 安装失败，请检查错误信息")
        sys.exit(1)
    
    print()
    
    # 验证安装
    if verify_installation():
        print("🎉 安装成功！")
        create_test_script()
        
        print("\n📋 后续步骤：")
        if use_conda:
            print("1. 激活环境：conda activate voice-came")
        print("2. 运行测试：python test_installation.py")
        print("3. 开始使用Voice-came系统")
    else:
        print("⚠️  安装可能不完整，请手动检查")

if __name__ == "__main__":
    main() 