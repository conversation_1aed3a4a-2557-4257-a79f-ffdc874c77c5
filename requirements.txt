# Voice-came 语音翻译系统依赖包
# 基于WhisperX和Gemma3-12B-Q4的研究结果

# Python版本要求：3.8-3.11

# 核心依赖 - WhisperX语音识别
whisperx>=3.1.1

# PyTorch框架 - 支持CUDA或CPU
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
# 注意：如需GPU支持，使用以下命令：
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 音频处理
librosa>=0.10.0
soundfile>=0.12.1
ffmpeg-normalize>=1.28.2

# AI模型和推理
transformers>=4.30.0
accelerate>=0.20.0
datasets>=2.12.0

# 本地LLM支持（Gemma3-12B-Q4）
llama-cpp-python>=0.2.56

# 音频相关
pydub>=0.25.1
webrtcvad>=2.0.10

# 数据处理
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# 进度条和UI
tqdm>=4.65.0
rich>=13.4.0

# 配置管理
pyyaml>=6.0
python-dotenv>=1.0.0
hydra-core>=1.3.0

# 文件处理
pathlib
os-sys
typing-extensions>=4.5.0

# 网络和API
requests>=2.31.0
httpx>=0.24.0

# 语音合成（可选）
pyttsx3>=2.90

# 多语言支持
polyglot>=16.7.4
langdetect>=1.0.9

# 日志记录
loguru>=0.7.0

# TDD开发工具 (必需)
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-asyncio>=0.21.1
pytest-xdist>=3.3.1
black>=23.0.0
flake8>=6.0.0
mypy>=1.4.0
isort>=5.12.0
pre-commit>=3.3.0

# 开发工具
coverage>=7.2.0
bandit>=1.7.5
safety>=2.3.4

# 性能监控
psutil>=5.9.0
memory-profiler>=0.61.0 