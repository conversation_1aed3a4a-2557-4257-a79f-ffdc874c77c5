#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASMR算法验证器 - Voice-came技术负责人专业模块

功能特性:
1. ASMR音频特征检测与验证
2. 耳语语音识别精度测试
3. 低音量音频处理性能评估
4. 批量ASMR样本验证
5. 算法性能基准测试

技术架构:
- 基于现有Whisper引擎扩展
- 支持多引擎对比验证(whisper.cpp, faster-whisper, whisperx)
- 集成音频预处理和后处理管道
- 提供详细的验证报告和性能指标
"""

import os
import sys
import json
import time
import numpy as np
import librosa
import subprocess
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class ASMRValidationResult:
    """ASMR验证结果数据结构"""
    file_path: str
    engine_type: str
    model_name: str
    
    # 音频特征指标
    duration: float
    sample_rate: int
    rms_energy: float
    spectral_centroid: float
    zero_crossing_rate: float
    
    # ASMR特征检测
    is_asmr_detected: bool
    whisper_confidence: float
    low_volume_ratio: float
    silence_ratio: float
    
    # 性能指标
    processing_time: float
    transcription_accuracy: float
    word_error_rate: float
    
    # 识别结果
    transcription: str
    word_timestamps: List[Dict]
    
    # 验证状态
    validation_status: str
    error_message: Optional[str] = None

class ASMRValidator:
    """ASMR算法验证器核心类"""
    
    def __init__(self, config_path: str = "asmr_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        self.results: List[ASMRValidationResult] = []
        
        # 验证环境检查
        self._validate_environment()
        
    def _load_config(self) -> Dict:
        """加载ASMR验证配置"""
        default_config = {
            "engines": {
                "whisper_cpp": {
                    "enabled": True,
                    "models": ["ggml-small.bin", "ggml-medium.bin", "ggml-large-v2.bin"],
                    "param_file": "whisper/param.txt"
                },
                "faster_whisper": {
                    "enabled": True,
                    "models": ["faster-whisper-small", "faster-whisper-medium", "faster-whisper-large-v3"],
                    "param_file": "whisper-faster/param.txt"
                },
                "whisperx": {
                    "enabled": False,  # 待集成
                    "models": ["small", "medium", "large-v2"],
                    "param_file": "whisperx/param.txt"
                }
            },
            "asmr_detection": {
                "min_duration": 10.0,  # 最小音频时长(秒)
                "max_rms_threshold": 0.05,  # RMS能量阈值
                "min_silence_ratio": 0.1,  # 最小静音比例
                "whisper_confidence_threshold": 0.7,  # Whisper置信度阈值
                "spectral_centroid_threshold": 2000,  # 频谱重心阈值(Hz)
            },
            "validation_criteria": {
                "max_processing_time": 300,  # 最大处理时间(秒)
                "min_transcription_accuracy": 0.8,  # 最小转录精度
                "max_word_error_rate": 0.2,  # 最大词错误率
            },
            "output": {
                "report_dir": "asmr_validation_reports",
                "detailed_logs": True,
                "export_formats": ["json", "csv", "html"]
            }
        }
        
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                # 合并用户配置
                default_config.update(user_config)
        else:
            # 创建默认配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
                
        return default_config
    
    def _validate_environment(self):
        """验证运行环境"""
        print("[ASMR验证器] 检查运行环境...")
        
        # 检查必要的目录
        required_dirs = ['whisper', 'whisper-faster', 'project']
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                print(f"[WARNING] 目录不存在: {dir_name}")
        
        # 检查模型文件
        self._check_available_models()
        
        # 检查依赖库
        try:
            import librosa
            import numpy as np
            print("[INFO] 音频处理依赖库检查通过")
        except ImportError as e:
            print(f"[ERROR] 缺少必要依赖: {e}")
            
    def _check_available_models(self):
        """检查可用的模型文件"""
        print("[INFO] 检查可用模型:")
        
        # Whisper.cpp模型
        if os.path.exists('whisper'):
            whisper_models = [f for f in os.listdir('whisper') if f.startswith('ggml') and f.endswith('.bin')]
            print(f"  Whisper.cpp模型: {whisper_models}")
            
        # Faster-Whisper模型
        if os.path.exists('whisper-faster'):
            faster_models = [f for f in os.listdir('whisper-faster') if f.startswith('faster-whisper')]
            print(f"  Faster-Whisper模型: {faster_models}")
    
    def extract_audio_features(self, audio_path: str) -> Dict:
        """提取音频特征用于ASMR检测"""
        try:
            # 加载音频文件
            y, sr = librosa.load(audio_path, sr=None)
            
            # 基础特征
            duration = librosa.get_duration(y=y, sr=sr)
            rms_energy = np.mean(librosa.feature.rms(y=y)[0])
            spectral_centroid = np.mean(librosa.feature.spectral_centroid(y=y, sr=sr)[0])
            zero_crossing_rate = np.mean(librosa.feature.zero_crossing_rate(y)[0])
            
            # ASMR特征检测
            # 计算静音比例
            silence_threshold = 0.01
            silence_frames = np.sum(np.abs(y) < silence_threshold)
            silence_ratio = silence_frames / len(y)
            
            # 低音量比例
            low_volume_threshold = 0.05
            low_volume_frames = np.sum(np.abs(y) < low_volume_threshold)
            low_volume_ratio = low_volume_frames / len(y)
            
            return {
                'duration': duration,
                'sample_rate': sr,
                'rms_energy': float(rms_energy),
                'spectral_centroid': float(spectral_centroid),
                'zero_crossing_rate': float(zero_crossing_rate),
                'silence_ratio': float(silence_ratio),
                'low_volume_ratio': float(low_volume_ratio)
            }
            
        except Exception as e:
            print(f"[ERROR] 音频特征提取失败: {e}")
            return {}
    
    def detect_asmr_characteristics(self, features: Dict) -> bool:
        """基于音频特征检测ASMR特性"""
        criteria = self.config['asmr_detection']
        
        # ASMR检测逻辑
        is_asmr = (
            features.get('duration', 0) >= criteria['min_duration'] and
            features.get('rms_energy', 1) <= criteria['max_rms_threshold'] and
            features.get('silence_ratio', 0) >= criteria['min_silence_ratio'] and
            features.get('spectral_centroid', 5000) <= criteria['spectral_centroid_threshold']
        )
        
        return is_asmr
    
    def run_whisper_transcription(self, audio_path: str, engine: str, model: str) -> Dict:
        """运行Whisper转录并返回结果"""
        start_time = time.time()
        
        try:
            # 准备音频文件
            wav_path = audio_path + '.wav'
            if not os.path.exists(wav_path):
                # 转换为WAV格式
                subprocess.run([
                    'ffmpeg', '-y', '-i', audio_path, 
                    '-acodec', 'pcm_s16le', '-ac', '1', '-ar', '16000', 
                    wav_path
                ], check=True, capture_output=True)
            
            # 根据引擎类型执行转录
            if engine == 'whisper_cpp':
                result = self._run_whisper_cpp(wav_path, model)
            elif engine == 'faster_whisper':
                result = self._run_faster_whisper(wav_path, model)
            elif engine == 'whisperx':
                result = self._run_whisperx(wav_path, model)
            else:
                raise ValueError(f"不支持的引擎类型: {engine}")
            
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            
            return result
            
        except Exception as e:
            return {
                'error': str(e),
                'processing_time': time.time() - start_time,
                'transcription': '',
                'confidence': 0.0
            }
    
    def _run_whisper_cpp(self, wav_path: str, model: str) -> Dict:
        """运行Whisper.cpp转录"""
        # 读取参数配置
        with open('whisper/param.txt', 'r', encoding='utf-8') as f:
            param_template = f.read().strip()
        
        # 替换参数
        input_file = wav_path.replace('.wav', '')
        cmd_params = param_template.replace('$whisper_file', model)\
                                 .replace('$input_file', input_file)\
                                 .replace('$language', 'auto')
        
        # 执行命令
        result = subprocess.run(cmd_params.split(), 
                              capture_output=True, text=True, 
                              creationflags=0x08000000)
        
        # 解析结果
        srt_file = input_file + '.srt'
        transcription = ""
        if os.path.exists(srt_file):
            with open(srt_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 简单提取文本内容
                lines = content.split('\n')
                transcription = ' '.join([line for line in lines if not line.isdigit() and '-->' not in line and line.strip()])
        
        return {
            'transcription': transcription,
            'confidence': 0.8,  # Whisper.cpp不直接提供置信度
            'word_timestamps': [],
            'engine_output': result.stdout
        }
    
    def _run_faster_whisper(self, wav_path: str, model: str) -> Dict:
        """运行Faster-Whisper转录"""
        # 读取参数配置
        with open('whisper-faster/param.txt', 'r', encoding='utf-8') as f:
            param_template = f.read().strip()
        
        # 替换参数
        input_file = wav_path.replace('.wav', '')
        output_dir = os.path.dirname(input_file)
        cmd_params = param_template.replace('$whisper_file', model[15:])\
                                 .replace('$input_file', input_file)\
                                 .replace('$language', 'auto')\
                                 .replace('$output_dir', output_dir)
        
        # 执行命令
        result = subprocess.run(cmd_params.split(), 
                              capture_output=True, text=True, 
                              creationflags=0x08000000)
        
        # 解析结果(类似whisper.cpp)
        srt_file = input_file + '.srt'
        transcription = ""
        if os.path.exists(srt_file):
            with open(srt_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                transcription = ' '.join([line for line in lines if not line.isdigit() and '-->' not in line and line.strip()])
        
        return {
            'transcription': transcription,
            'confidence': 0.85,  # Faster-Whisper通常有更高置信度
            'word_timestamps': [],
            'engine_output': result.stdout
        }
    
    def _run_whisperx(self, wav_path: str, model: str) -> Dict:
        """运行WhisperX转录
        
        使用WhisperX引擎进行音频转录，支持多说话人识别、VAD预处理和词级别对齐。
        特别针对ASMR内容进行了优化。
        
        Args:
            wav_path: WAV音频文件路径
            model: 模型名称
            
        Returns:
            转录结果字典，包含转录文本、置信度和词级时间戳
        """
        try:
            # 导入必要的模块
            import sys
            import os
            import torch
            import logging
            
            # 配置日志记录器
            logger = logging.getLogger("asmr_validator.whisperx")
            
            # 添加src目录到Python路径
            src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
            if src_dir not in sys.path:
                sys.path.append(src_dir)
            
            # 导入WhisperX引擎
            try:
                from whisperx.whisperx_engine import WhisperXEngine, WhisperXConfig
                logger.info("成功导入WhisperX引擎")
            except ImportError as e:
                logger.error(f"导入WhisperX引擎失败: {str(e)}")
                raise ImportError(f"无法导入WhisperX引擎，请确保已安装相关依赖: {str(e)}")
            
            # 检测CUDA可用性
            cuda_available = torch.cuda.is_available()
            device = "cuda" if cuda_available else "cpu"
            compute_type = "float16" if cuda_available else "float32"
            
            logger.info(f"使用设备: {device}, 计算精度: {compute_type}")
            
            # 配置WhisperX，针对ASMR内容优化参数
            config = WhisperXConfig(
                model_name=model,
                device=device,
                compute_type=compute_type,
                language=None,  # 自动检测语言
                word_timestamps=True,  # 启用词级时间戳
                vad_threshold=0.4,  # 降低VAD阈值以捕获低音量ASMR内容
                vad_min_speech_duration_ms=200,  # 降低最小语音持续时间以捕获短促ASMR声音
                vad_max_silence_duration_ms=1000,  # 增加最大静音持续时间以适应ASMR内容中的停顿
                asmr_mode=True,  # 启用ASMR模式优化
                speaker_diarization=True  # 启用说话人分离
            )
            
            # 初始化引擎
            logger.info(f"初始化WhisperX引擎，模型: {model}")
            engine = WhisperXEngine(config)
            
            # 执行转录，显示进度回调
            def progress_callback(progress: float):
                if int(progress * 100) % 10 == 0:  # 每10%记录一次
                    logger.info(f"WhisperX转录进度: {progress:.1%}")
            
            logger.info(f"开始转录音频: {wav_path}")
            start_time = time.time()
            result = engine.transcribe(wav_path, progress_callback=progress_callback)
            processing_time = time.time() - start_time
            logger.info(f"转录完成，耗时: {processing_time:.2f}秒")
            
            # 提取词级时间戳
            word_timestamps = []
            if "segments" in result and result["segments"]:
                for segment in result["segments"]:
                    if "words" in segment:
                        for word in segment["words"]:
                            word_timestamps.append({
                                "word": word["word"],
                                "start": word["start"],
                                "end": word["end"],
                                "confidence": word.get("confidence", 0.0),
                                "speaker": segment.get("speaker", "SPEAKER_00")
                            })
            
            # 计算平均置信度
            confidences = [word.get("confidence", 0.0) for segment in result.get("segments", []) 
                          for word in segment.get("words", []) if "confidence" in word]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
            
            # 提取转录文本
            transcription = " ".join([segment.get("text", "") for segment in result.get("segments", [])])
            
            # 卸载模型释放内存
            logger.info("卸载模型并释放GPU内存")
            engine.unload_model()
            
            # 如果使用CUDA，清理缓存
            if cuda_available:
                torch.cuda.empty_cache()
                logger.info("已清理CUDA缓存")
            
            return {
                'transcription': transcription,
                'confidence': avg_confidence,
                'word_timestamps': word_timestamps,
                'engine_output': result,
                'processing_time': processing_time
            }
            
        except Exception as e:
            import traceback
            error_msg = f"WhisperX转录失败: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            return {
                'transcription': '',
                'confidence': 0.0,
                'word_timestamps': [],
                'engine_output': error_msg
            }
    
    def validate_single_file(self, audio_path: str, reference_text: str = None) -> ASMRValidationResult:
        """验证单个音频文件"""
        print(f"[INFO] 开始验证: {audio_path}")
        
        # 提取音频特征
        features = self.extract_audio_features(audio_path)
        if not features:
            return ASMRValidationResult(
                file_path=audio_path,
                engine_type="unknown",
                model_name="unknown",
                duration=0, sample_rate=0, rms_energy=0,
                spectral_centroid=0, zero_crossing_rate=0,
                is_asmr_detected=False, whisper_confidence=0,
                low_volume_ratio=0, silence_ratio=0,
                processing_time=0, transcription_accuracy=0,
                word_error_rate=1.0, transcription="",
                word_timestamps=[], validation_status="FAILED",
                error_message="音频特征提取失败"
            )
        
        # ASMR特征检测
        is_asmr = self.detect_asmr_characteristics(features)
        
        # 选择最佳引擎和模型进行验证
        best_result = None
        best_score = 0
        
        for engine_name, engine_config in self.config['engines'].items():
            if not engine_config['enabled']:
                continue
                
            for model in engine_config['models']:
                # 检查模型是否存在
                if not self._check_model_exists(engine_name, model):
                    continue
                
                print(f"[INFO] 测试引擎: {engine_name}, 模型: {model}")
                
                # 运行转录
                transcription_result = self.run_whisper_transcription(audio_path, engine_name, model)
                
                # 计算性能指标
                accuracy = self._calculate_accuracy(transcription_result['transcription'], reference_text)
                wer = self._calculate_wer(transcription_result['transcription'], reference_text)
                
                # 综合评分
                score = self._calculate_validation_score(transcription_result, accuracy, wer, is_asmr)
                
                if score > best_score:
                    best_score = score
                    best_result = ASMRValidationResult(
                        file_path=audio_path,
                        engine_type=engine_name,
                        model_name=model,
                        duration=features['duration'],
                        sample_rate=features['sample_rate'],
                        rms_energy=features['rms_energy'],
                        spectral_centroid=features['spectral_centroid'],
                        zero_crossing_rate=features['zero_crossing_rate'],
                        is_asmr_detected=is_asmr,
                        whisper_confidence=transcription_result['confidence'],
                        low_volume_ratio=features['low_volume_ratio'],
                        silence_ratio=features['silence_ratio'],
                        processing_time=transcription_result['processing_time'],
                        transcription_accuracy=accuracy,
                        word_error_rate=wer,
                        transcription=transcription_result['transcription'],
                        word_timestamps=transcription_result['word_timestamps'],
                        validation_status="PASSED" if score > 0.7 else "FAILED"
                    )
        
        return best_result or ASMRValidationResult(
            file_path=audio_path, engine_type="none", model_name="none",
            duration=0, sample_rate=0, rms_energy=0, spectral_centroid=0,
            zero_crossing_rate=0, is_asmr_detected=False, whisper_confidence=0,
            low_volume_ratio=0, silence_ratio=0, processing_time=0,
            transcription_accuracy=0, word_error_rate=1.0, transcription="",
            word_timestamps=[], validation_status="NO_ENGINE",
            error_message="没有可用的引擎或模型"
        )
    
    def _check_model_exists(self, engine: str, model: str) -> bool:
        """检查模型文件是否存在"""
        if engine == 'whisper_cpp':
            return os.path.exists(f'whisper/{model}')
        elif engine == 'faster_whisper':
            return os.path.exists(f'whisper-faster/{model}')
        elif engine == 'whisperx':
            return True  # WhisperX模型自动下载
        return False
    
    def _calculate_accuracy(self, transcription: str, reference: str) -> float:
        """计算转录精度"""
        if not reference:
            return 0.8  # 无参考文本时给予默认分数
        
        # 简单的字符级别相似度计算
        from difflib import SequenceMatcher
        return SequenceMatcher(None, transcription.lower(), reference.lower()).ratio()
    
    def _calculate_wer(self, transcription: str, reference: str) -> float:
        """计算词错误率"""
        if not reference:
            return 0.2  # 无参考文本时给予默认WER
        
        # 简化的WER计算
        trans_words = transcription.split()
        ref_words = reference.split()
        
        if not ref_words:
            return 1.0
        
        # 使用编辑距离计算WER
        from difflib import SequenceMatcher
        similarity = SequenceMatcher(None, trans_words, ref_words).ratio()
        return 1.0 - similarity
    
    def _calculate_validation_score(self, transcription_result: Dict, accuracy: float, wer: float, is_asmr: bool) -> float:
        """计算综合验证分数"""
        # 权重配置
        weights = {
            'accuracy': 0.4,
            'wer': 0.3,
            'confidence': 0.2,
            'asmr_bonus': 0.1
        }
        
        score = (
            accuracy * weights['accuracy'] +
            (1.0 - wer) * weights['wer'] +
            transcription_result['confidence'] * weights['confidence'] +
            (0.1 if is_asmr else 0) * weights['asmr_bonus']
        )
        
        return min(1.0, max(0.0, score))
    
    def batch_validate(self, audio_files: List[str], reference_texts: List[str] = None) -> List[ASMRValidationResult]:
        """批量验证音频文件"""
        print(f"[INFO] 开始批量验证 {len(audio_files)} 个文件")
        
        results = []
        for i, audio_file in enumerate(audio_files):
            reference = reference_texts[i] if reference_texts and i < len(reference_texts) else None
            result = self.validate_single_file(audio_file, reference)
            results.append(result)
            self.results.append(result)
            
            print(f"[INFO] 完成 {i+1}/{len(audio_files)}: {result.validation_status}")
        
        return results
    
    def generate_report(self, output_path: str = None) -> str:
        """生成验证报告"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"asmr_validation_report_{timestamp}.json"
        
        # 统计信息
        total_files = len(self.results)
        passed_files = len([r for r in self.results if r.validation_status == "PASSED"])
        asmr_detected = len([r for r in self.results if r.is_asmr_detected])
        
        avg_accuracy = np.mean([r.transcription_accuracy for r in self.results]) if self.results else 0
        avg_wer = np.mean([r.word_error_rate for r in self.results]) if self.results else 0
        avg_processing_time = np.mean([r.processing_time for r in self.results]) if self.results else 0
        
        report = {
            "validation_summary": {
                "total_files": total_files,
                "passed_files": passed_files,
                "pass_rate": passed_files / total_files if total_files > 0 else 0,
                "asmr_detected_count": asmr_detected,
                "asmr_detection_rate": asmr_detected / total_files if total_files > 0 else 0
            },
            "performance_metrics": {
                "average_accuracy": avg_accuracy,
                "average_wer": avg_wer,
                "average_processing_time": avg_processing_time
            },
            "engine_performance": self._analyze_engine_performance(),
            "detailed_results": [{
                "file_path": r.file_path,
                "engine_type": r.engine_type,
                "model_name": r.model_name,
                "validation_status": r.validation_status,
                "is_asmr_detected": r.is_asmr_detected,
                "transcription_accuracy": r.transcription_accuracy,
                "word_error_rate": r.word_error_rate,
                "processing_time": r.processing_time,
                "transcription": r.transcription[:200] + "..." if len(r.transcription) > 200 else r.transcription
            } for r in self.results],
            "config_used": self.config,
            "timestamp": datetime.now().isoformat()
        }
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"[INFO] 验证报告已保存: {output_path}")
        return output_path
    
    def _analyze_engine_performance(self) -> Dict:
        """分析各引擎性能"""
        engine_stats = {}
        
        for result in self.results:
            engine = result.engine_type
            if engine not in engine_stats:
                engine_stats[engine] = {
                    'count': 0,
                    'passed': 0,
                    'total_accuracy': 0,
                    'total_wer': 0,
                    'total_time': 0
                }
            
            stats = engine_stats[engine]
            stats['count'] += 1
            if result.validation_status == 'PASSED':
                stats['passed'] += 1
            stats['total_accuracy'] += result.transcription_accuracy
            stats['total_wer'] += result.word_error_rate
            stats['total_time'] += result.processing_time
        
        # 计算平均值
        for engine, stats in engine_stats.items():
            if stats['count'] > 0:
                stats['avg_accuracy'] = stats['total_accuracy'] / stats['count']
                stats['avg_wer'] = stats['total_wer'] / stats['count']
                stats['avg_processing_time'] = stats['total_time'] / stats['count']
                stats['pass_rate'] = stats['passed'] / stats['count']
        
        return engine_stats

def main():
    """ASMR算法验证器主程序"""
    print("=" * 60)
    print("🎧 Voice-came ASMR算法验证器 v1.0")
    print("=" * 60)
    
    # 初始化验证器
    validator = ASMRValidator()
    
    # 示例用法
    print("\n[示例] 单文件验证:")
    print("validator.validate_single_file('path/to/asmr_audio.wav')")
    
    print("\n[示例] 批量验证:")
    print("audio_files = ['audio1.wav', 'audio2.wav']")
    print("results = validator.batch_validate(audio_files)")
    print("validator.generate_report()")
    
    print("\n[配置] 当前验证配置:")
    print(f"- 支持引擎: {list(validator.config['engines'].keys())}")
    print(f"- ASMR检测阈值: RMS≤{validator.config['asmr_detection']['max_rms_threshold']}")
    print(f"- 最小静音比例: {validator.config['asmr_detection']['min_silence_ratio']}")
    
    print("\n✅ ASMR算法验证器初始化完成！")
    print("📋 请调用相应方法开始验证任务。")

if __name__ == "__main__":
    main()