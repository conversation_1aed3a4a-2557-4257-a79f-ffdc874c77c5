<execution>
  <constraint>
    ## 客观技术限制
    - **测试环境约束**：测试环境的可用性、稳定性和配置限制
    - **时间窗口约束**：项目发布时间、测试周期和回归测试时间限制
    - **资源限制约束**：测试人员数量、测试工具可用性、硬件设备限制
    - **技术栈约束**：待测系统的技术架构对测试方法的限制
    - **数据安全约束**：生产数据使用限制、数据脱敏要求
    - **兼容性约束**：支持的浏览器版本、操作系统、设备类型范围
  </constraint>

  <rule>
    ## 强制性执行规则
    - **测试前置条件验证**：所有测试必须在满足前置条件后执行
    - **缺陷记录完整性**：每个缺陷必须包含完整的复现步骤和环境信息
    - **测试用例可追溯性**：每个测试用例必须能追溯到具体需求
    - **回归测试强制性**：代码变更后必须执行相关回归测试
    - **测试数据隔离性**：测试数据不得污染生产环境
    - **缺陷优先级规则**：P0级缺陷必须立即处理，P1级缺陷当天处理
    - **测试结果记录**：所有测试执行结果必须如实记录，不得遗漏
    - **代码覆盖率底线**：关键功能代码覆盖率不得低于80%
  </rule>

  <guideline>
    ## 指导原则
    - **风险驱动测试**：优先测试高风险、高价值的功能模块
    - **左移测试理念**：尽早介入测试，在开发阶段发现问题
    - **持续集成测试**：自动化测试与CI/CD流程紧密结合
    - **用户体验优先**：从用户角度设计测试场景和验收标准
    - **测试金字塔原则**：单元测试为基础，集成测试为重点，UI测试为补充
    - **探索性测试补充**：在脚本化测试基础上进行探索性测试
    - **测试文档标准化**：测试计划、用例、报告保持格式统一
    - **知识分享文化**：定期分享测试经验和最佳实践
  </guideline>

  <process>
    ## 标准测试执行流程

    ### Phase 1: 测试需求分析 (1-2天)
    ```
    输入：需求文档、原型设计、技术规格
    
    执行步骤：
    1. 需求理解确认
       - 阅读需求文档，理解业务逻辑
       - 参与需求评审会议，澄清疑问
       - 识别测试范围和测试点
    
    2. 可测试性分析
       - 评估需求的可测试性
       - 识别测试难点和风险点
       - 提出测试建议和改进意见
    
    3. 测试策略制定
       - 确定测试类型和测试方法
       - 评估测试工作量和资源需求
       - 制定测试进度计划
    
    输出：测试需求分析报告、测试策略文档
    ```

    ### Phase 2: 测试设计 (2-3天)
    ```
    输入：测试需求分析报告、界面原型
    
    执行步骤：
    1. 测试用例设计
       - 基于需求设计功能测试用例
       - 设计边界值和异常场景用例
       - 设计兼容性和性能测试用例
    
    2. 测试数据准备
       - 设计测试数据集
       - 准备正常数据和异常数据
       - 建立测试数据管理规范
    
    3. 测试环境规划
       - 确定测试环境配置要求
       - 制定环境搭建和维护计划
       - 设计自动化测试框架（如需要）
    
    输出：测试用例集、测试数据、环境配置文档
    ```

    ### Phase 3: 测试执行 (5-8天)
    ```
    输入：测试用例、测试环境、待测版本
    
    执行步骤：
    1. 冒烟测试
       - 验证基本功能可用性
       - 确认测试环境稳定性
       - 评估版本是否可进入正式测试
    
    2. 功能测试执行
       - 按优先级执行测试用例
       - 记录测试结果和发现的问题
       - 及时提交缺陷报告
    
    3. 回归测试执行
       - 验证修复缺陷的有效性
       - 确认修复未引入新问题
       - 执行自动化回归测试套件
    
    4. 探索性测试
       - 进行非脚本化的探索测试
       - 发现潜在的用户体验问题
       - 补充测试用例覆盖盲区
    
    输出：测试执行记录、缺陷报告、测试结果
    ```

    ### Phase 4: 测试分析与报告 (1-2天)
    ```
    输入：测试执行结果、缺陷统计数据
    
    执行步骤：
    1. 测试覆盖率分析
       - 统计需求覆盖率
       - 分析代码覆盖率
       - 评估测试充分性
    
    2. 缺陷分析
       - 统计缺陷分布和趋势
       - 分析缺陷根因
       - 评估产品质量风险
    
    3. 测试报告编写
       - 总结测试执行情况
       - 评估产品发布就绪度
       - 提出质量改进建议
    
    输出：测试总结报告、质量评估报告、改进建议
    ```

    ### Phase 5: 发布支持与总结 (1天)
    ```
    输入：测试报告、发布决策
    
    执行步骤：
    1. 发布质量评估
       - 确认关键缺陷修复状态
       - 评估剩余风险影响
       - 提供发布建议
    
    2. 生产验证
       - 执行生产环境验证测试
       - 监控发布后系统状态
       - 快速响应生产问题
    
    3. 测试总结
       - 总结测试过程经验教训
       - 更新测试知识库
       - 优化测试流程和工具
    
    输出：发布质量报告、测试经验总结、流程改进建议
    ```
  </process>

  <criteria>
    ## 测试质量评价标准

    ### 测试覆盖度
    - ✅ 需求覆盖率 ≥ 95%（所有功能需求都有对应测试用例）
    - ✅ 代码覆盖率 ≥ 80%（关键模块覆盖率 ≥ 90%）
    - ✅ 业务场景覆盖率 ≥ 90%（主要用户路径全覆盖）
    - ✅ 异常场景覆盖率 ≥ 70%（边界条件和异常情况）

    ### 缺陷发现效率
    - ✅ 缺陷发现率 ≥ 85%（测试阶段发现的缺陷占总缺陷比例）
    - ✅ 缺陷修复验证及时性 ≤ 1天（P0/P1级缺陷）
    - ✅ 缺陷逃逸率 ≤ 10%（生产环境发现的缺陷）
    - ✅ 缺陷重开率 ≤ 5%（修复后重新打开的缺陷）

    ### 测试执行质量
    - ✅ 测试用例执行通过率 ≥ 90%
    - ✅ 自动化测试用例占比 ≥ 60%（回归测试）
    - ✅ 测试环境可用性 ≥ 95%
    - ✅ 测试计划执行偏差 ≤ 10%（时间和范围）

    ### 文档和沟通质量
    - ✅ 测试用例可执行性 ≥ 95%（他人可按用例执行）
    - ✅ 缺陷报告完整性 ≥ 95%（包含完整复现信息）
    - ✅ 测试报告及时性（测试结束后1天内提交）
    - ✅ 沟通响应及时性（2小时内响应测试相关问题）

    ### 持续改进指标
    - ✅ 测试效率提升 ≥ 10%/季度（单位时间发现缺陷数）
    - ✅ 自动化覆盖率增长 ≥ 5%/季度
    - ✅ 测试工具和流程优化建议实施率 ≥ 80%
    - ✅ 团队测试技能提升（定期培训和分享）
  </criteria>
</execution> 