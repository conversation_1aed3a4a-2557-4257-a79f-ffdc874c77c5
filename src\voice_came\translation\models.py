"""
翻译模块数据模型

定义Voice-came翻译功能中使用的核心数据类型，
遵循接口契约文档的规范。
"""

from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum


class JobStatus(Enum):
    """翻译任务状态"""
    PENDING = "pending"
    RUNNING = "running"  
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class HealthStatus(Enum):
    """服务健康状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class ServiceStatus(Enum):
    """服务运行状态"""
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class AudioSegment:
    """音频片段"""
    start_time: float
    end_time: float
    text: str
    confidence: float


@dataclass
class AudioData:
    """音频数据"""
    file_path: str
    segments: List[AudioSegment]
    metadata: Dict[str, Any]


@dataclass  
class TranslationConfig:
    """翻译配置"""
    source_language: str
    target_language: str
    terminology_enabled: bool = True
    quality_optimization: bool = True
    model_config: Optional[Dict[str, Any]] = None


@dataclass
class TranslationJob:
    """翻译任务"""
    job_id: str
    status: JobStatus
    created_at: datetime
    config: TranslationConfig
    progress: float = 0.0
    error_message: Optional[str] = None


@dataclass
class TranslationResult:
    """翻译结果"""
    job_id: str
    original_text: str
    translated_text: str
    quality_score: float
    terminology_applied: List[str]
    processing_time: float
    metadata: Dict[str, Any]


@dataclass
class TerminologyRule:
    """术语规则"""
    source_term: str
    target_term: str
    context: Optional[str] = None
    priority: int = 0


@dataclass
class TerminologyRules:
    """术语规则集合"""
    rules: List[TerminologyRule]
    language_pair: str


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    issues: List[str]
    suggestions: List[str]
    confidence: float


@dataclass
class QualityScore:
    """质量评分"""
    overall_score: float
    fluency: float
    accuracy: float
    terminology_consistency: float
    details: Dict[str, Any]


@dataclass
class BatchConfig:
    """批量配置"""
    batch_size: int = 5
    max_concurrent_jobs: int = 3
    retry_count: int = 3
    timeout_seconds: int = 300


@dataclass
class BatchJob:
    """批量任务"""
    batch_id: str
    job_ids: List[str]
    status: JobStatus
    created_at: datetime
    config: BatchConfig
    total_files: int
    completed_files: int = 0
    failed_files: int = 0


@dataclass
class BatchProgress:
    """批量进度"""
    batch_id: str
    total_jobs: int
    completed_jobs: int
    failed_jobs: int
    current_job: Optional[str]
    estimated_completion: Optional[datetime]


@dataclass
class ErrorReport:
    """错误报告"""
    error_code: str
    error_message: str
    timestamp: datetime
    context: Dict[str, Any]
    suggested_actions: List[str]


# VoiceTransl相关数据类型
@dataclass
class WhisperXOutput:
    """WhisperX输出格式"""
    segments: List[AudioSegment]
    language: str
    metadata: Dict[str, Any]


@dataclass
class VoiceTranslInput:
    """VoiceTransl输入格式"""
    text: str
    source_language: str
    target_language: str
    context: Optional[str] = None


@dataclass
class VoiceTranslOutput:
    """VoiceTransl输出格式"""
    translated_text: str
    confidence: float
    model_used: str
    processing_time: float


# 改进建议数据类型
@dataclass
class Improvement:
    """改进建议"""
    type: str
    description: str
    severity: str
    suggested_fix: str


@dataclass
class AudioFile:
    """音频文件信息"""
    file_path: str
    filename: str
    size: int
    duration: Optional[float] = None