{"meta": {"format": 3, "version": "7.8.1", "timestamp": "2025-06-16T20:26:37.632393", "branch_coverage": true, "show_contexts": false}, "files": {"src\\voice_came\\__init__.py": {"executed_lines": [1, 14, 15, 16, 17, 19, 20], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 14, 15, 16, 17, 19, 20], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 14, 15, 16, 17, 19, 20], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\core\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 10, 13, 14, 15, 17], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 10, 13, 14, 15, 17], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 10, 13, 14, 15, 17], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\core\\batch_queue.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 265, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 265, "excluded_lines": 0, "num_branches": 52, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 52}, "missing_lines": [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 24, 27, 29, 30, 31, 32, 33, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 62, 68, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 89, 90, 91, 92, 93, 95, 107, 108, 110, 111, 114, 115, 117, 119, 131, 132, 134, 137, 138, 139, 142, 144, 147, 148, 150, 152, 161, 163, 165, 167, 169, 171, 173, 175, 177, 179, 181, 183, 185, 187, 189, 191, 193, 195, 197, 199, 200, 202, 204, 205, 207, 209, 211, 213, 214, 215, 217, 218, 221, 222, 224, 226, 228, 229, 231, 233, 234, 236, 238, 239, 242, 243, 245, 247, 249, 251, 253, 255, 257, 263, 265, 266, 267, 268, 269, 271, 273, 275, 276, 277, 279, 280, 282, 284, 293, 295, 296, 303, 304, 305, 307, 308, 310, 312, 313, 314, 316, 318, 319, 320, 323, 324, 326, 328, 330, 331, 334, 336, 338, 339, 340, 342, 344, 345, 346, 347, 348, 349, 351, 353, 355, 356, 357, 358, 359, 361, 363, 365, 368, 369, 371, 373, 376, 378, 380, 382, 383, 386, 388, 389, 391, 392, 394, 396, 397, 398, 399, 400, 403, 406, 408, 409, 410, 413, 415, 416, 417, 419, 421, 422, 423, 424, 427, 428, 429, 431, 434, 435, 437, 438, 439, 440, 442, 444, 446, 449, 450, 451, 453, 455, 457, 459, 461, 465, 468, 469, 471, 473, 474, 476, 478, 479, 482, 483, 484, 486, 488, 489, 490, 491, 492], "excluded_lines": [], "executed_branches": [], "missing_branches": [[54, 55], [54, 56], [56, 57], [56, 58], [58, -53], [58, 59], [89, -68], [89, 90], [107, 108], [107, 110], [114, 115], [114, 117], [131, 132], [131, 134], [137, 138], [137, 142], [147, 148], [147, 150], [213, 214], [213, 217], [242, 243], [242, 245], [265, 266], [265, 271], [266, 267], [266, 269], [279, 280], [279, 282], [303, 304], [303, 307], [324, 326], [324, 334], [355, 356], [355, 361], [368, 369], [368, 371], [391, -378], [391, 392], [396, -394], [396, 397], [398, 399], [398, 403], [408, 409], [408, 413], [422, 423], [422, 427], [468, -442], [468, 469], [476, 478], [476, 482], [488, -486], [488, 489]], "functions": {"ProcessingJob.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [54, 55, 56, 57, 58, 59], "excluded_lines": [], "executed_branches": [], "missing_branches": [[54, 55], [54, 56], [56, 57], [56, 58], [58, -53], [58, 59]]}, "BatchQueue.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 89, 90, 91, 92, 93], "excluded_lines": [], "executed_branches": [], "missing_branches": [[89, -68], [89, 90]]}, "BatchQueue.add_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [107, 108, 110, 111, 114, 115, 117], "excluded_lines": [], "executed_branches": [], "missing_branches": [[107, 108], [107, 110], [114, 115], [114, 117]]}, "BatchQueue.remove_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [131, 132, 134, 137, 138, 139, 142, 144, 147, 148, 150], "excluded_lines": [], "executed_branches": [], "missing_branches": [[131, 132], [131, 134], [137, 138], [137, 142], [147, 148], [147, 150]]}, "BatchQueue.get_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [161], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.get_all_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [165], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.get_scheduled_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [169, 171], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.get_queue_size": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [175], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.get_pending_jobs_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [179], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.get_running_jobs_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [183], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.get_completed_jobs_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [187], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.get_failed_jobs_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [191], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.get_max_concurrent_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [195], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.set_max_concurrent_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [199, 200], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.set_processor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [204, 205], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.set_progress_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [209], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.start_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [213, 214, 215, 217, 218, 221, 222, 224], "excluded_lines": [], "executed_branches": [], "missing_branches": [[213, 214], [213, 217]]}, "BatchQueue.pause_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [228, 229], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.resume_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [233, 234], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.stop_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [238, 239, 242, 243, 245, 247], "excluded_lines": [], "executed_branches": [], "missing_branches": [[242, 243], [242, 245]]}, "BatchQueue.is_running": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [251], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.is_paused": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [255], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.wait_for_completion": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [263, 265, 266, 267, 268, 269, 271], "excluded_lines": [], "executed_branches": [], "missing_branches": [[265, 266], [265, 271], [266, 267], [266, 269]]}, "BatchQueue.get_overall_progress": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [275, 276, 277, 279, 280, 282, 284], "excluded_lines": [], "executed_branches": [], "missing_branches": [[279, 280], [279, 282]]}, "BatchQueue.save_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [295, 296, 303, 304, 305, 307, 308, 310, 312, 313, 314], "excluded_lines": [], "executed_branches": [], "missing_branches": [[303, 304], [303, 307]]}, "BatchQueue.load_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [318, 319, 320, 323, 324, 326, 328, 330, 331, 334, 336, 338, 339, 340], "excluded_lines": [], "executed_branches": [], "missing_branches": [[324, 326], [324, 334]]}, "BatchQueue.load_state_from_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [344, 345, 346, 347, 348, 349], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue.shutdown": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [353, 355, 356, 357, 358, 359, 361], "excluded_lines": [], "executed_branches": [], "missing_branches": [[355, 356], [355, 361]]}, "BatchQueue.cleanup_completed_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [365, 368, 369, 371], "excluded_lines": [], "executed_branches": [], "missing_branches": [[368, 369], [368, 371]]}, "BatchQueue.get_memory_usage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [376], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchQueue._run_processing_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [380, 382, 383, 386, 388, 389, 391, 392], "excluded_lines": [], "executed_branches": [], "missing_branches": [[391, -378], [391, 392]]}, "BatchQueue._process_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [396, 397, 398, 399, 400, 403, 406, 408, 409, 410, 413, 415, 416, 417], "excluded_lines": [], "executed_branches": [], "missing_branches": [[396, -394], [396, 397], [398, 399], [398, 403], [408, 409], [408, 413]]}, "BatchQueue._start_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [421, 422, 423, 424, 427, 428, 429, 431, 434, 435, 437, 438, 439, 440], "excluded_lines": [], "executed_branches": [], "missing_branches": [[422, 423], [422, 427]]}, "BatchQueue._process_single_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [444, 446, 449, 450, 451, 453, 455, 457, 459, 461, 465, 468, 469], "excluded_lines": [], "executed_branches": [], "missing_branches": [[468, -442], [468, 469]]}, "BatchQueue._handle_job_failure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [473, 474, 476, 478, 479, 482, 483, 484], "excluded_lines": [], "executed_branches": [], "missing_branches": [[476, 478], [476, 482]]}, "BatchQueue._save_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [488, 489, 490, 491, 492], "excluded_lines": [], "executed_branches": [], "missing_branches": [[488, -486], [488, 489]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 71, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 71, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 24, 27, 29, 30, 31, 32, 33, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 62, 68, 95, 119, 152, 163, 167, 173, 177, 181, 185, 189, 193, 197, 202, 207, 211, 226, 231, 236, 249, 253, 257, 273, 293, 316, 342, 351, 363, 373, 378, 394, 419, 442, 471, 486], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"JobStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProcessingJob": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [54, 55, 56, 57, 58, 59], "excluded_lines": [], "executed_branches": [], "missing_branches": [[54, 55], [54, 56], [56, 57], [56, 58], [58, -53], [58, 59]]}, "BatchQueue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 188, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 188, "excluded_lines": 0, "num_branches": 46, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 46}, "missing_lines": [76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 89, 90, 91, 92, 93, 107, 108, 110, 111, 114, 115, 117, 131, 132, 134, 137, 138, 139, 142, 144, 147, 148, 150, 161, 165, 169, 171, 175, 179, 183, 187, 191, 195, 199, 200, 204, 205, 209, 213, 214, 215, 217, 218, 221, 222, 224, 228, 229, 233, 234, 238, 239, 242, 243, 245, 247, 251, 255, 263, 265, 266, 267, 268, 269, 271, 275, 276, 277, 279, 280, 282, 284, 295, 296, 303, 304, 305, 307, 308, 310, 312, 313, 314, 318, 319, 320, 323, 324, 326, 328, 330, 331, 334, 336, 338, 339, 340, 344, 345, 346, 347, 348, 349, 353, 355, 356, 357, 358, 359, 361, 365, 368, 369, 371, 376, 380, 382, 383, 386, 388, 389, 391, 392, 396, 397, 398, 399, 400, 403, 406, 408, 409, 410, 413, 415, 416, 417, 421, 422, 423, 424, 427, 428, 429, 431, 434, 435, 437, 438, 439, 440, 444, 446, 449, 450, 451, 453, 455, 457, 459, 461, 465, 468, 469, 473, 474, 476, 478, 479, 482, 483, 484, 488, 489, 490, 491, 492], "excluded_lines": [], "executed_branches": [], "missing_branches": [[89, -68], [89, 90], [107, 108], [107, 110], [114, 115], [114, 117], [131, 132], [131, 134], [137, 138], [137, 142], [147, 148], [147, 150], [213, 214], [213, 217], [242, 243], [242, 245], [265, 266], [265, 271], [266, 267], [266, 269], [279, 280], [279, 282], [303, 304], [303, 307], [324, 326], [324, 334], [355, 356], [355, 361], [368, 369], [368, 371], [391, -378], [391, 392], [396, -394], [396, 397], [398, 399], [398, 403], [408, 409], [408, 413], [422, 423], [422, 427], [468, -442], [468, 469], [476, 478], [476, 482], [488, -486], [488, 489]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 71, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 71, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 24, 27, 29, 30, 31, 32, 33, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 62, 68, 95, 119, 152, 163, 167, 173, 177, 181, 185, 189, 193, 197, 202, 207, 211, 226, 231, 236, 249, 253, 257, 273, 293, 316, 342, 351, 363, 373, 378, 394, 419, 442, 471, 486], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\core\\enhanced_batch_queue.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 509, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 509, "excluded_lines": 0, "num_branches": 146, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 146}, "missing_lines": [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 38, 41, 43, 44, 45, 46, 47, 48, 51, 53, 54, 55, 56, 59, 60, 63, 74, 75, 76, 77, 78, 79, 80, 81, 82, 85, 86, 87, 88, 91, 92, 93, 96, 97, 98, 100, 102, 103, 104, 106, 108, 110, 112, 113, 114, 115, 116, 118, 120, 121, 123, 124, 125, 126, 128, 131, 132, 134, 135, 136, 137, 138, 139, 140, 143, 144, 145, 146, 147, 150, 151, 152, 154, 156, 157, 158, 159, 160, 161, 162, 165, 166, 167, 170, 171, 172, 173, 174, 177, 178, 179, 180, 182, 185, 188, 189, 190, 191, 192, 194, 196, 204, 206, 208, 209, 210, 211, 215, 216, 218, 220, 221, 223, 225, 226, 228, 233, 234, 241, 242, 247, 248, 249, 251, 254, 257, 258, 259, 267, 273, 274, 276, 279, 282, 284, 286, 289, 290, 292, 295, 296, 298, 301, 302, 303, 305, 308, 309, 310, 312, 314, 316, 318, 319, 320, 322, 329, 332, 343, 344, 345, 346, 349, 350, 351, 352, 355, 356, 357, 358, 361, 362, 363, 364, 367, 368, 371, 372, 375, 376, 378, 387, 388, 389, 391, 392, 395, 397, 399, 401, 413, 414, 415, 417, 420, 421, 422, 424, 427, 429, 431, 433, 435, 436, 438, 440, 441, 443, 445, 447, 449, 451, 452, 453, 455, 457, 458, 460, 461, 466, 468, 470, 471, 472, 473, 475, 476, 479, 485, 486, 489, 492, 493, 495, 497, 499, 500, 501, 502, 504, 505, 507, 509, 510, 511, 512, 514, 515, 517, 523, 524, 525, 526, 528, 529, 531, 533, 535, 536, 537, 540, 541, 542, 545, 547, 549, 551, 553, 554, 555, 556, 557, 560, 561, 565, 566, 569, 570, 571, 572, 573, 574, 576, 577, 578, 580, 584, 585, 587, 588, 590, 592, 593, 594, 596, 598, 600, 601, 602, 603, 606, 607, 610, 612, 614, 615, 618, 619, 620, 621, 622, 625, 628, 629, 632, 633, 634, 635, 636, 639, 640, 643, 644, 645, 646, 647, 650, 651, 653, 656, 657, 659, 660, 663, 665, 666, 668, 669, 670, 671, 674, 675, 676, 679, 681, 682, 685, 686, 690, 691, 693, 699, 700, 702, 704, 705, 707, 709, 711, 713, 725, 726, 727, 744, 745, 746, 749, 751, 753, 754, 756, 758, 759, 761, 762, 763, 766, 767, 770, 771, 772, 780, 781, 782, 783, 784, 787, 788, 789, 790, 791, 793, 796, 797, 798, 800, 801, 804, 805, 806, 807, 808, 810, 812, 813, 815, 816, 818, 820, 821, 822, 823, 825, 827, 828, 830, 832, 833, 834, 835, 836, 838, 841, 842, 843, 845, 846, 849, 851, 852, 856, 857, 859, 861, 862, 863, 864, 865, 866, 867, 868, 869, 871, 872, 874, 876, 877, 878, 880, 881, 882, 884, 886, 887, 888, 889, 890, 892, 894, 895, 896, 897, 900, 903, 904, 905, 907, 909, 910, 911, 913, 914, 916, 917, 919, 921, 923, 925, 926, 927, 928, 929, 931, 933, 942, 943, 944, 945, 946, 947, 948, 950], "excluded_lines": [], "executed_branches": [], "missing_branches": [[96, -63], [96, 97], [97, 96], [97, 98], [102, 103], [102, 104], [120, 121], [120, 123], [124, 125], [124, 128], [166, 167], [166, 170], [171, 172], [171, 177], [173, 174], [173, 177], [177, 178], [177, 182], [179, 180], [179, 182], [220, 221], [220, 223], [233, 234], [233, 241], [241, 242], [241, 247], [247, 248], [247, 251], [319, 320], [319, 322], [371, 372], [371, 375], [375, -332], [375, 376], [388, 389], [388, 391], [414, 415], [414, 417], [420, 421], [420, 424], [457, 458], [457, 460], [471, 472], [471, 475], [485, 486], [485, 489], [492, 493], [492, 495], [500, 501], [500, 504], [510, 511], [510, 514], [524, 525], [524, 528], [533, 535], [533, 540], [535, 536], [535, 537], [540, 541], [540, 545], [553, 554], [553, 596], [555, 556], [555, 560], [561, 565], [561, 590], [569, 570], [569, 584], [571, 572], [571, 574], [574, 569], [574, 576], [576, 577], [576, 580], [584, 585], [584, 587], [587, 588], [587, 590], [606, -598], [606, 607], [619, 620], [619, 643], [620, 621], [620, 625], [632, 633], [632, 639], [639, 619], [639, 640], [656, 657], [656, 690], [666, 668], [666, 674], [681, 682], [681, 685], [685, 686], [685, 690], [704, 705], [704, 707], [725, 726], [725, 744], [726, 725], [726, 727], [758, 759], [758, 761], [771, 772], [771, 804], [787, 788], [787, 793], [796, 797], [796, 798], [798, 771], [798, 800], [805, 806], [805, 807], [807, 808], [807, 810], [820, -818], [820, 821], [833, 834], [833, 841], [834, 833], [834, 835], [836, 833], [836, 838], [841, 842], [841, 845], [845, 846], [845, 849], [856, -825], [856, 857], [861, -859], [861, 862], [863, -862], [863, 864], [871, -859], [871, 872], [876, 877], [876, 880], [880, -874], [880, 881], [910, 911], [910, 913], [926, -923], [926, 927], [942, 943], [942, 950]], "functions": {"QueueJob.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [74, 75, 76, 77, 78, 79, 80, 81, 82, 85, 86, 87, 88, 91, 92, 93, 96, 97, 98], "excluded_lines": [], "executed_branches": [], "missing_branches": [[96, -63], [96, 97], [97, 96], [97, 98]]}, "QueueJob.__lt__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [102, 103, 104], "excluded_lines": [], "executed_branches": [], "missing_branches": [[102, 103], [102, 104]]}, "QueueJob.can_retry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [108], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QueueJob.reset_for_retry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [112, 113, 114, 115, 116], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QueueJob.get_estimated_remaining_time": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [120, 121, 123, 124, 125, 126, 128], "excluded_lines": [], "executed_branches": [], "missing_branches": [[120, 121], [120, 123], [124, 125], [124, 128]]}, "QueueMetrics.update_from_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [156, 157, 158, 159, 160, 161, 162, 165, 166, 167, 170, 171, 172, 173, 174, 177, 178, 179, 180, 182], "excluded_lines": [], "executed_branches": [], "missing_branches": [[166, 167], [166, 170], [171, 172], [171, 177], [173, 174], [173, 177], [177, 178], [177, 182], [179, 180], [179, 182]]}, "PerformanceMonitor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [189, 190, 191, 192], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PerformanceMonitor.record_job_completion": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [196, 204], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PerformanceMonitor.record_memory_usage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [208, 209, 210, 211, 215, 216], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PerformanceMonitor.get_performance_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [220, 221, 223, 225, 226, 228, 233, 234, 241, 242, 247, 248, 249, 251], "excluded_lines": [], "executed_branches": [], "missing_branches": [[220, 221], [220, 223], [233, 234], [233, 241], [241, 242], [241, 247], [247, 248], [247, 251]]}, "ErrorRecoveryManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [258, 259], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorRecoveryManager.handle_job_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [273, 274, 276, 279, 282, 284], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorRecoveryManager._handle_file_not_found": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [289, 290], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorRecoveryManager._handle_permission_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [295, 296], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorRecoveryManager._handle_memory_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [301, 302, 303], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorRecoveryManager._handle_timeout_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [308, 309, 310], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorRecoveryManager._handle_default_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [314], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorRecoveryManager.get_error_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [318, 319, 320, 322], "excluded_lines": [], "executed_branches": [], "missing_branches": [[319, 320], [319, 322]]}, "EnhancedBatchQueue.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [343, 344, 345, 346, 349, 350, 351, 352, 355, 356, 357, 358, 361, 362, 363, 364, 367, 368, 371, 372, 375, 376], "excluded_lines": [], "executed_branches": [], "missing_branches": [[371, 372], [371, 375], [375, -332], [375, 376]]}, "EnhancedBatchQueue.add_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [387, 388, 389, 391, 392, 395, 397, 399], "excluded_lines": [], "executed_branches": [], "missing_branches": [[388, 389], [388, 391]]}, "EnhancedBatchQueue.remove_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [413, 414, 415, 417, 420, 421, 422, 424, 427, 429, 431], "excluded_lines": [], "executed_branches": [], "missing_branches": [[414, 415], [414, 417], [420, 421], [420, 424]]}, "EnhancedBatchQueue.size": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [435, 436], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "EnhancedBatchQueue.get_running_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [440, 441], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "EnhancedBatchQueue.get_pending_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [445, 447], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "EnhancedBatchQueue.get_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [451, 452, 453], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "EnhancedBatchQueue.get_performance_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [457, 458, 460, 461, 466], "excluded_lines": [], "executed_branches": [], "missing_branches": [[457, 458], [457, 460]]}, "EnhancedBatchQueue.start": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [470, 471, 472, 473, 475, 476, 479, 485, 486, 489, 492, 493, 495], "excluded_lines": [], "executed_branches": [], "missing_branches": [[471, 472], [471, 475], [485, 486], [485, 489], [492, 493], [492, 495]]}, "EnhancedBatchQueue.pause": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [499, 500, 501, 502, 504, 505], "excluded_lines": [], "executed_branches": [], "missing_branches": [[500, 501], [500, 504]]}, "EnhancedBatchQueue.resume": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [509, 510, 511, 512, 514, 515], "excluded_lines": [], "executed_branches": [], "missing_branches": [[510, 511], [510, 514]]}, "EnhancedBatchQueue.stop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [523, 524, 525, 526, 528, 529, 531, 533, 535, 536, 537, 540, 541, 542, 545, 547], "excluded_lines": [], "executed_branches": [], "missing_branches": [[524, 525], [524, 528], [533, 535], [533, 540], [535, 536], [535, 537], [540, 541], [540, 545]]}, "EnhancedBatchQueue._process_jobs_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 29, "excluded_lines": 0, "num_branches": 18, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 18}, "missing_lines": [551, 553, 554, 555, 556, 557, 560, 561, 565, 566, 569, 570, 571, 572, 573, 574, 576, 577, 578, 580, 584, 585, 587, 588, 590, 592, 593, 594, 596], "excluded_lines": [], "executed_branches": [], "missing_branches": [[553, 554], [553, 596], [555, 556], [555, 560], [561, 565], [561, 590], [569, 570], [569, 584], [571, 572], [571, 574], [574, 569], [574, 576], [576, 577], [576, 580], [584, 585], [584, 587], [587, 588], [587, 590]]}, "EnhancedBatchQueue._start_job_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [600, 601, 602, 603, 606, 607], "excluded_lines": [], "executed_branches": [], "missing_branches": [[606, -598], [606, 607]]}, "EnhancedBatchQueue._process_single_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 47, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 47, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [612, 614, 615, 618, 619, 620, 621, 622, 625, 628, 629, 632, 633, 634, 635, 636, 639, 640, 643, 644, 645, 646, 647, 650, 651, 653, 656, 657, 659, 660, 663, 665, 666, 668, 669, 670, 671, 674, 675, 676, 679, 681, 682, 685, 686, 690, 691], "excluded_lines": [], "executed_branches": [], "missing_branches": [[619, 620], [619, 643], [620, 621], [620, 625], [632, 633], [632, 639], [639, 619], [639, 640], [656, 657], [656, 690], [666, 668], [666, 674], [681, 682], [681, 685], [685, 686], [685, 690]]}, "EnhancedBatchQueue.set_progress_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [699, 700], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "EnhancedBatchQueue.save_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [704, 705, 707, 709, 711, 713, 725, 726, 727, 744, 745, 746, 749, 751, 753, 754], "excluded_lines": [], "executed_branches": [], "missing_branches": [[704, 705], [704, 707], [725, 726], [725, 744], [726, 725], [726, 727]]}, "EnhancedBatchQueue._load_state_from_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 36, "excluded_lines": 0, "num_branches": 14, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 14}, "missing_lines": [758, 759, 761, 762, 763, 766, 767, 770, 771, 772, 780, 781, 782, 783, 784, 787, 788, 789, 790, 791, 793, 796, 797, 798, 800, 801, 804, 805, 806, 807, 808, 810, 812, 813, 815, 816], "excluded_lines": [], "executed_branches": [], "missing_branches": [[758, 759], [758, 761], [771, 772], [771, 804], [787, 788], [787, 793], [796, 797], [796, 798], [798, 771], [798, 800], [805, 806], [805, 807], [807, 808], [807, 810]]}, "EnhancedBatchQueue._schedule_cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [820, 821, 822, 823], "excluded_lines": [], "executed_branches": [], "missing_branches": [[820, -818], [820, 821]]}, "EnhancedBatchQueue._cleanup_completed_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0, "num_branches": 12, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 12}, "missing_lines": [827, 828, 830, 832, 833, 834, 835, 836, 838, 841, 842, 843, 845, 846, 849, 851, 852, 856, 857], "excluded_lines": [], "executed_branches": [], "missing_branches": [[833, 834], [833, 841], [834, 833], [834, 835], [836, 833], [836, 838], [841, 842], [841, 845], [845, 846], [845, 849], [856, -825], [856, 857]]}, "EnhancedBatchQueue._start_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [861, 862, 871, 872], "excluded_lines": [], "executed_branches": [], "missing_branches": [[861, -859], [861, 862], [871, -859], [871, 872]]}, "EnhancedBatchQueue._start_monitoring.monitor_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [863, 864, 865, 866, 867, 868, 869], "excluded_lines": [], "executed_branches": [], "missing_branches": [[863, -862], [863, 864]]}, "EnhancedBatchQueue._stop_timers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [876, 877, 878, 880, 881, 882], "excluded_lines": [], "executed_branches": [], "missing_branches": [[876, 877], [876, 880], [880, -874], [880, 881]]}, "EnhancedBatchQueue._get_current_memory_usage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [886, 887, 888, 889, 890], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "EnhancedBatchQueue.__del__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [894, 895, 896, 897], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QueueManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [904, 905], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QueueManager.create_queue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [909, 910, 911, 913, 914, 916, 917], "excluded_lines": [], "executed_branches": [], "missing_branches": [[910, 911], [910, 913]]}, "QueueManager.get_queue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [921], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QueueManager.remove_queue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [925, 926, 927, 928, 929], "excluded_lines": [], "executed_branches": [], "missing_branches": [[926, -923], [926, 927]]}, "QueueManager.get_global_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [933, 942, 943, 944, 945, 946, 947, 948, 950], "excluded_lines": [], "executed_branches": [], "missing_branches": [[942, 943], [942, 950]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 100, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 100, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 38, 41, 43, 44, 45, 46, 47, 48, 51, 53, 54, 55, 56, 59, 60, 63, 100, 106, 110, 118, 131, 132, 134, 135, 136, 137, 138, 139, 140, 143, 144, 145, 146, 147, 150, 151, 152, 154, 185, 188, 194, 206, 218, 254, 257, 267, 286, 292, 298, 305, 312, 316, 329, 332, 378, 401, 433, 438, 443, 449, 455, 468, 497, 507, 517, 549, 598, 610, 693, 702, 756, 818, 825, 859, 874, 884, 892, 900, 903, 907, 919, 923, 931], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"JobState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "JobPriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QueueJob": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 35, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [74, 75, 76, 77, 78, 79, 80, 81, 82, 85, 86, 87, 88, 91, 92, 93, 96, 97, 98, 102, 103, 104, 108, 112, 113, 114, 115, 116, 120, 121, 123, 124, 125, 126, 128], "excluded_lines": [], "executed_branches": [], "missing_branches": [[96, -63], [96, 97], [97, 96], [97, 98], [102, 103], [102, 104], [120, 121], [120, 123], [124, 125], [124, 128]]}, "QueueMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [156, 157, 158, 159, 160, 161, 162, 165, 166, 167, 170, 171, 172, 173, 174, 177, 178, 179, 180, 182], "excluded_lines": [], "executed_branches": [], "missing_branches": [[166, 167], [166, 170], [171, 172], [171, 177], [173, 174], [173, 177], [177, 178], [177, 182], [179, 180], [179, 182]]}, "PerformanceMonitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 26, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [189, 190, 191, 192, 196, 204, 208, 209, 210, 211, 215, 216, 220, 221, 223, 225, 226, 228, 233, 234, 241, 242, 247, 248, 249, 251], "excluded_lines": [], "executed_branches": [], "missing_branches": [[220, 221], [220, 223], [233, 234], [233, 241], [241, 242], [241, 247], [247, 248], [247, 251]]}, "ErrorRecoveryManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 23, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [258, 259, 273, 274, 276, 279, 282, 284, 289, 290, 295, 296, 301, 302, 303, 308, 309, 310, 314, 318, 319, 320, 322], "excluded_lines": [], "executed_branches": [], "missing_branches": [[319, 320], [319, 322]]}, "EnhancedBatchQueue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 281, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 281, "excluded_lines": 0, "num_branches": 110, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 110}, "missing_lines": [343, 344, 345, 346, 349, 350, 351, 352, 355, 356, 357, 358, 361, 362, 363, 364, 367, 368, 371, 372, 375, 376, 387, 388, 389, 391, 392, 395, 397, 399, 413, 414, 415, 417, 420, 421, 422, 424, 427, 429, 431, 435, 436, 440, 441, 445, 447, 451, 452, 453, 457, 458, 460, 461, 466, 470, 471, 472, 473, 475, 476, 479, 485, 486, 489, 492, 493, 495, 499, 500, 501, 502, 504, 505, 509, 510, 511, 512, 514, 515, 523, 524, 525, 526, 528, 529, 531, 533, 535, 536, 537, 540, 541, 542, 545, 547, 551, 553, 554, 555, 556, 557, 560, 561, 565, 566, 569, 570, 571, 572, 573, 574, 576, 577, 578, 580, 584, 585, 587, 588, 590, 592, 593, 594, 596, 600, 601, 602, 603, 606, 607, 612, 614, 615, 618, 619, 620, 621, 622, 625, 628, 629, 632, 633, 634, 635, 636, 639, 640, 643, 644, 645, 646, 647, 650, 651, 653, 656, 657, 659, 660, 663, 665, 666, 668, 669, 670, 671, 674, 675, 676, 679, 681, 682, 685, 686, 690, 691, 699, 700, 704, 705, 707, 709, 711, 713, 725, 726, 727, 744, 745, 746, 749, 751, 753, 754, 758, 759, 761, 762, 763, 766, 767, 770, 771, 772, 780, 781, 782, 783, 784, 787, 788, 789, 790, 791, 793, 796, 797, 798, 800, 801, 804, 805, 806, 807, 808, 810, 812, 813, 815, 816, 820, 821, 822, 823, 827, 828, 830, 832, 833, 834, 835, 836, 838, 841, 842, 843, 845, 846, 849, 851, 852, 856, 857, 861, 862, 863, 864, 865, 866, 867, 868, 869, 871, 872, 876, 877, 878, 880, 881, 882, 886, 887, 888, 889, 890, 894, 895, 896, 897], "excluded_lines": [], "executed_branches": [], "missing_branches": [[371, 372], [371, 375], [375, -332], [375, 376], [388, 389], [388, 391], [414, 415], [414, 417], [420, 421], [420, 424], [457, 458], [457, 460], [471, 472], [471, 475], [485, 486], [485, 489], [492, 493], [492, 495], [500, 501], [500, 504], [510, 511], [510, 514], [524, 525], [524, 528], [533, 535], [533, 540], [535, 536], [535, 537], [540, 541], [540, 545], [553, 554], [553, 596], [555, 556], [555, 560], [561, 565], [561, 590], [569, 570], [569, 584], [571, 572], [571, 574], [574, 569], [574, 576], [576, 577], [576, 580], [584, 585], [584, 587], [587, 588], [587, 590], [606, -598], [606, 607], [619, 620], [619, 643], [620, 621], [620, 625], [632, 633], [632, 639], [639, 619], [639, 640], [656, 657], [656, 690], [666, 668], [666, 674], [681, 682], [681, 685], [685, 686], [685, 690], [704, 705], [704, 707], [725, 726], [725, 744], [726, 725], [726, 727], [758, 759], [758, 761], [771, 772], [771, 804], [787, 788], [787, 793], [796, 797], [796, 798], [798, 771], [798, 800], [805, 806], [805, 807], [807, 808], [807, 810], [820, -818], [820, 821], [833, 834], [833, 841], [834, 833], [834, 835], [836, 833], [836, 838], [841, 842], [841, 845], [845, 846], [845, 849], [856, -825], [856, 857], [861, -859], [861, 862], [863, -862], [863, 864], [871, -859], [871, 872], [876, 877], [876, 880], [880, -874], [880, 881]]}, "QueueManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [904, 905, 909, 910, 911, 913, 914, 916, 917, 921, 925, 926, 927, 928, 929, 933, 942, 943, 944, 945, 946, 947, 948, 950], "excluded_lines": [], "executed_branches": [], "missing_branches": [[910, 911], [910, 913], [926, -923], [926, 927], [942, 943], [942, 950]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 100, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 100, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 38, 41, 43, 44, 45, 46, 47, 48, 51, 53, 54, 55, 56, 59, 60, 63, 100, 106, 110, 118, 131, 132, 134, 135, 136, 137, 138, 139, 140, 143, 144, 145, 146, 147, 150, 151, 152, 154, 185, 188, 194, 206, 218, 254, 257, 267, 286, 292, 298, 305, 312, 316, 329, 332, 378, 401, 433, 438, 443, 449, 455, 468, 497, 507, 517, 549, 598, 610, 693, 702, 756, 818, 825, 859, 874, 884, 892, 900, 903, 907, 919, 923, 931], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\core\\file_processor.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 32, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 32, "excluded_lines": 21, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [10, 11, 12, 13, 14, 16, 17, 19, 22, 50, 56, 58, 67, 68, 70, 71, 74, 77, 84, 85, 87, 96, 97, 100, 101, 102, 104, 105, 107, 108, 111, 114], "excluded_lines": [28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48], "executed_branches": [], "missing_branches": [[67, 68], [67, 70], [96, 97], [96, 100], [100, 101], [100, 111]], "functions": {"FileProcessor.process_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 14, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48], "executed_branches": [], "missing_branches": []}, "FileProcessor.get_supported_formats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [56], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileProcessor.validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [67, 68, 70, 71], "excluded_lines": [], "executed_branches": [], "missing_branches": [[67, 68], [67, 70]]}, "MockFileProcessor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [84, 85], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MockFileProcessor.process_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [96, 97, 100, 101, 102, 104, 105, 107, 108, 111, 114], "excluded_lines": [], "executed_branches": [], "missing_branches": [[96, 97], [96, 100], [100, 101], [100, 111]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 7, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 11, 12, 13, 14, 16, 17, 19, 22, 50, 58, 74, 77, 87], "excluded_lines": [28, 29, 30, 31, 32, 33, 34], "executed_branches": [], "missing_branches": []}}, "classes": {"FileProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 14, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [56, 67, 68, 70, 71], "excluded_lines": [35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48], "executed_branches": [], "missing_branches": [[67, 68], [67, 70]]}, "MockFileProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [84, 85, 96, 97, 100, 101, 102, 104, 105, 107, 108, 111, 114], "excluded_lines": [], "executed_branches": [], "missing_branches": [[96, 97], [96, 100], [100, 101], [100, 111]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 7, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 11, 12, 13, 14, 16, 17, 19, 22, 50, 58, 74, 77, 87], "excluded_lines": [28, 29, 30, 31, 32, 33, 34], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\core\\file_validator.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 129, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 129, "excluded_lines": 0, "num_branches": 42, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 42}, "missing_lines": [10, 11, 12, 13, 14, 16, 17, 19, 22, 23, 25, 26, 27, 28, 29, 31, 32, 33, 36, 43, 50, 51, 55, 58, 59, 60, 61, 64, 72, 81, 82, 84, 86, 87, 88, 89, 92, 93, 94, 95, 98, 99, 100, 101, 103, 104, 107, 108, 109, 110, 113, 114, 115, 116, 118, 119, 120, 121, 123, 124, 125, 126, 127, 129, 130, 131, 132, 133, 134, 135, 136, 139, 140, 141, 142, 143, 145, 146, 148, 149, 150, 151, 152, 154, 168, 169, 171, 172, 174, 175, 176, 179, 180, 182, 183, 185, 195, 196, 197, 198, 200, 201, 203, 204, 205, 207, 209, 210, 211, 213, 219, 221, 227, 228, 230, 239, 240, 241, 244, 245, 246, 247, 249, 250, 253, 254, 255, 256, 258], "excluded_lines": [], "executed_branches": [], "missing_branches": [[32, -31], [32, 33], [58, 59], [58, 64], [86, 87], [86, 92], [92, 93], [92, 98], [98, 99], [98, 103], [107, 108], [107, 113], [118, 119], [118, 123], [123, 124], [123, 139], [139, 140], [139, 145], [140, 141], [140, 145], [168, 169], [168, 171], [174, 175], [174, 182], [179, 174], [179, 180], [197, 198], [197, 200], [203, 204], [203, 207], [204, 203], [204, 205], [245, 246], [245, 253], [246, 245], [246, 247], [247, 245], [247, 249], [254, 255], [254, 258], [255, 254], [255, 256]], "functions": {"ValidationResult.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [32, 33], "excluded_lines": [], "executed_branches": [], "missing_branches": [[32, -31], [32, 33]]}, "FileValidator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [50, 51, 55, 58, 59, 60, 61, 64], "excluded_lines": [], "executed_branches": [], "missing_branches": [[58, 59], [58, 64]]}, "FileValidator.validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 54, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 54, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [81, 82, 84, 86, 87, 88, 89, 92, 93, 94, 95, 98, 99, 100, 101, 103, 104, 107, 108, 109, 110, 113, 114, 115, 116, 118, 119, 120, 121, 123, 124, 125, 126, 127, 129, 130, 131, 132, 133, 134, 135, 136, 139, 140, 141, 142, 143, 145, 146, 148, 149, 150, 151, 152], "excluded_lines": [], "executed_branches": [], "missing_branches": [[86, 87], [86, 92], [92, 93], [92, 98], [98, 99], [98, 103], [107, 108], [107, 113], [118, 119], [118, 123], [123, 124], [123, 139], [139, 140], [139, 145], [140, 141], [140, 145]]}, "FileValidator.batch_validate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [168, 169, 171, 172, 174, 175, 176, 179, 180, 182, 183], "excluded_lines": [], "executed_branches": [], "missing_branches": [[168, 169], [168, 171], [174, 175], [174, 182], [179, 174], [179, 180]]}, "FileValidator._check_file_header": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [195, 196, 197, 198, 200, 201, 203, 204, 205, 207, 209, 210, 211], "excluded_lines": [], "executed_branches": [], "missing_branches": [[197, 198], [197, 200], [203, 204], [203, 207], [204, 203], [204, 205]]}, "FileValidator.get_supported_formats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [219], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileValidator.get_max_file_size_str": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [227, 228], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileValidator.get_validation_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [239, 240, 241, 244, 245, 246, 247, 249, 250, 253, 254, 255, 256, 258], "excluded_lines": [], "executed_branches": [], "missing_branches": [[245, 246], [245, 253], [246, 245], [246, 247], [247, 245], [247, 249], [254, 255], [254, 258], [255, 254], [255, 256]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 11, 12, 13, 14, 16, 17, 19, 22, 23, 25, 26, 27, 28, 29, 31, 36, 43, 72, 154, 185, 213, 221, 230], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"ValidationResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [32, 33], "excluded_lines": [], "executed_branches": [], "missing_branches": [[32, -31], [32, 33]]}, "FileValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 103, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 103, "excluded_lines": 0, "num_branches": 40, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 40}, "missing_lines": [50, 51, 55, 58, 59, 60, 61, 64, 81, 82, 84, 86, 87, 88, 89, 92, 93, 94, 95, 98, 99, 100, 101, 103, 104, 107, 108, 109, 110, 113, 114, 115, 116, 118, 119, 120, 121, 123, 124, 125, 126, 127, 129, 130, 131, 132, 133, 134, 135, 136, 139, 140, 141, 142, 143, 145, 146, 148, 149, 150, 151, 152, 168, 169, 171, 172, 174, 175, 176, 179, 180, 182, 183, 195, 196, 197, 198, 200, 201, 203, 204, 205, 207, 209, 210, 211, 219, 227, 228, 239, 240, 241, 244, 245, 246, 247, 249, 250, 253, 254, 255, 256, 258], "excluded_lines": [], "executed_branches": [], "missing_branches": [[58, 59], [58, 64], [86, 87], [86, 92], [92, 93], [92, 98], [98, 99], [98, 103], [107, 108], [107, 113], [118, 119], [118, 123], [123, 124], [123, 139], [139, 140], [139, 145], [140, 141], [140, 145], [168, 169], [168, 171], [174, 175], [174, 182], [179, 174], [179, 180], [197, 198], [197, 200], [203, 204], [203, 207], [204, 203], [204, 205], [245, 246], [245, 253], [246, 245], [246, 247], [247, 245], [247, 249], [254, 255], [254, 258], [255, 254], [255, 256]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 11, 12, 13, 14, 16, 17, 19, 22, 23, 25, 26, 27, 28, 29, 31, 36, 43, 72, 154, 185, 213, 221, 230], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\example.py": {"executed_lines": [1, 8, 13, 18, 25, 26, 28, 31, 45], "summary": {"covered_lines": 7, "num_statements": 23, "percent_covered": 22.580645161290324, "percent_covered_display": "22.58", "missing_lines": 16, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [10, 15, 20, 21, 22, 29, 33, 34, 35, 36, 37, 38, 40, 42, 43, 47], "excluded_lines": [], "executed_branches": [], "missing_branches": [[20, 21], [20, 22], [33, 34], [33, 35], [35, 36], [35, 37], [37, 38], [37, 40]], "functions": {"add": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "multiply": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [15], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "divide": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [20, 21, 22], "excluded_lines": [], "executed_branches": [], "missing_branches": [[20, 21], [20, 22]]}, "Calculator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [29], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Calculator.calculate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [33, 34, 35, 36, 37, 38, 40, 42, 43], "excluded_lines": [], "executed_branches": [], "missing_branches": [[33, 34], [33, 35], [35, 36], [35, 37], [37, 38], [37, 40]]}, "Calculator.get_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [47], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 8, 13, 18, 25, 26, 28, 31, 45], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"Calculator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [29, 33, 34, 35, 36, 37, 38, 40, 42, 43, 47], "excluded_lines": [], "executed_branches": [], "missing_branches": [[33, 34], [33, 35], [35, 36], [35, 37], [37, 38], [37, 40]]}, "": {"executed_lines": [1, 8, 13, 18, 25, 26, 28, 31, 45], "summary": {"covered_lines": 7, "num_statements": 12, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [10, 15, 20, 21, 22], "excluded_lines": [], "executed_branches": [], "missing_branches": [[20, 21], [20, 22]]}}}, "src\\voice_came\\exceptions.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 21, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 13, 14, 15, 16, 19, 21, 24, 26, 29, 31, 34, 36, 39, 41, 44, 46, 49, 51, 54, 56], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"VoiceCameError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [14, 15, 16], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 13, 19, 21, 24, 26, 29, 31, 34, 36, 39, 41, 44, 46, 49, 51, 54, 56], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"VoiceCameError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [14, 15, 16], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileValidationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "WhisperXError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TranslationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BatchProcessingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ConfigurationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AudioProcessingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QueueError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProcessingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 13, 19, 21, 24, 26, 29, 31, 34, 36, 39, 41, 44, 46, 49, 51, 54, 56], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\metrics\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [14, 15, 16, 17, 18, 19, 20, 22, 32], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [14, 15, 16, 17, 18, 19, 20, 22, 32], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [14, 15, 16, 17, 18, 19, 20, 22, 32], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\metrics\\compliance_checker.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 195, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 195, "excluded_lines": 0, "num_branches": 60, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 60}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 16, 19, 20, 21, 23, 24, 25, 26, 27, 30, 37, 39, 41, 42, 43, 44, 47, 54, 61, 62, 64, 65, 66, 68, 70, 71, 73, 75, 76, 78, 79, 81, 82, 84, 85, 86, 88, 91, 92, 94, 96, 97, 98, 100, 102, 104, 105, 106, 109, 110, 112, 113, 114, 115, 116, 119, 120, 121, 122, 124, 126, 127, 128, 130, 132, 133, 135, 136, 137, 139, 140, 142, 143, 145, 146, 147, 149, 150, 152, 153, 154, 156, 158, 159, 160, 162, 163, 166, 169, 172, 175, 176, 177, 180, 181, 183, 184, 186, 189, 190, 192, 194, 195, 196, 198, 200, 202, 206, 208, 209, 211, 212, 213, 215, 218, 219, 221, 223, 224, 225, 227, 229, 230, 240, 242, 243, 244, 246, 248, 249, 251, 252, 253, 255, 256, 258, 259, 260, 266, 272, 280, 281, 282, 284, 287, 293, 295, 296, 297, 299, 300, 302, 309, 310, 311, 313, 316, 322, 324, 326, 328, 329, 330, 338, 339, 340, 347, 348, 350, 352, 354, 356, 357, 359, 360, 363, 364, 365, 367, 368, 369, 371, 372, 373, 375, 376, 377, 379], "excluded_lines": [], "executed_branches": [], "missing_branches": [[70, 71], [70, 73], [78, 79], [78, 81], [84, 85], [84, 88], [85, 84], [85, 86], [91, 92], [91, 94], [105, 106], [105, 109], [112, 113], [112, 119], [113, 114], [113, 115], [115, 112], [115, 116], [119, 120], [119, 121], [121, 122], [121, 124], [132, 133], [132, 135], [139, 140], [139, 142], [145, 146], [145, 149], [146, 145], [146, 147], [162, 163], [162, 166], [175, 176], [175, 180], [183, 184], [183, 186], [189, 190], [189, 192], [208, 209], [208, 211], [218, 219], [218, 221], [248, 249], [248, 251], [258, 259], [258, 272], [259, 260], [259, 266], [329, 330], [329, 338], [339, 340], [339, 350], [359, 360], [359, 363], [364, 365], [364, 367], [368, 369], [368, 371], [372, 373], [372, 379]], "functions": {"ComplianceChecker.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [20, 21, 23, 24, 25, 26, 27, 30], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComplianceChecker.check_tdd_compliance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [39, 41, 42, 43, 44, 47, 54, 61, 62, 64, 65, 66], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComplianceChecker._check_test_first_compliance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [70, 71, 73, 75, 76, 78, 79, 81, 82, 84, 85, 86, 88, 91, 92, 94, 96, 97, 98], "excluded_lines": [], "executed_branches": [], "missing_branches": [[70, 71], [70, 73], [78, 79], [78, 81], [84, 85], [84, 88], [85, 84], [85, 86], [91, 92], [91, 94]]}, "ComplianceChecker._is_test_first_commit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0, "num_branches": 12, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 12}, "missing_lines": [102, 104, 105, 106, 109, 110, 112, 113, 114, 115, 116, 119, 120, 121, 122, 124, 126, 127, 128], "excluded_lines": [], "executed_branches": [], "missing_branches": [[105, 106], [105, 109], [112, 113], [112, 119], [113, 114], [113, 115], [115, 112], [115, 116], [119, 120], [119, 121], [121, 122], [121, 124]]}, "ComplianceChecker._check_commit_patterns": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 17, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [132, 133, 135, 136, 137, 139, 140, 142, 143, 145, 146, 147, 149, 150, 152, 153, 154], "excluded_lines": [], "executed_branches": [], "missing_branches": [[132, 133], [132, 135], [139, 140], [139, 142], [145, 146], [145, 149], [146, 145], [146, 147]]}, "ComplianceChecker._check_file_structure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [158, 159, 160, 162, 163, 166, 169, 172, 175, 176, 177, 180, 181, 183, 184, 186, 189, 190, 192, 194, 195, 196], "excluded_lines": [], "executed_branches": [], "missing_branches": [[162, 163], [162, 166], [175, 176], [175, 180], [183, 184], [183, 186], [189, 190], [189, 192]]}, "ComplianceChecker._check_coverage_compliance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [200, 202, 206, 208, 209, 211, 212, 213, 215, 218, 219, 221, 223, 224, 225], "excluded_lines": [], "executed_branches": [], "missing_branches": [[208, 209], [208, 211], [218, 219], [218, 221]]}, "ComplianceChecker.get_detailed_compliance_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [229, 230, 240, 242, 243, 244], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComplianceChecker._get_test_first_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [248, 249, 251, 252, 253, 255, 256, 258, 259, 260, 266, 272, 280, 281, 282], "excluded_lines": [], "executed_branches": [], "missing_branches": [[248, 249], [248, 251], [258, 259], [258, 272], [259, 260], [259, 266]]}, "ComplianceChecker._get_commit_pattern_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [287], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComplianceChecker._get_file_structure_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [295, 296, 297, 299, 300, 302, 309, 310, 311], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComplianceChecker._get_coverage_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [316], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComplianceChecker._get_compliance_violations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [324, 326, 328, 329, 330, 338, 339, 340, 347, 348, 350], "excluded_lines": [], "executed_branches": [], "missing_branches": [[329, 330], [329, 338], [339, 340], [339, 350]]}, "ComplianceChecker._generate_compliance_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [354, 356, 357, 359, 360, 363, 364, 365, 367, 368, 369, 371, 372, 373, 375, 376, 377, 379], "excluded_lines": [], "executed_branches": [], "missing_branches": [[359, 360], [359, 363], [364, 365], [364, 367], [368, 369], [368, 371], [372, 373], [372, 379]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 16, 19, 37, 68, 100, 130, 156, 198, 227, 246, 284, 293, 313, 322, 352], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"ComplianceChecker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 173, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 173, "excluded_lines": 0, "num_branches": 60, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 60}, "missing_lines": [20, 21, 23, 24, 25, 26, 27, 30, 39, 41, 42, 43, 44, 47, 54, 61, 62, 64, 65, 66, 70, 71, 73, 75, 76, 78, 79, 81, 82, 84, 85, 86, 88, 91, 92, 94, 96, 97, 98, 102, 104, 105, 106, 109, 110, 112, 113, 114, 115, 116, 119, 120, 121, 122, 124, 126, 127, 128, 132, 133, 135, 136, 137, 139, 140, 142, 143, 145, 146, 147, 149, 150, 152, 153, 154, 158, 159, 160, 162, 163, 166, 169, 172, 175, 176, 177, 180, 181, 183, 184, 186, 189, 190, 192, 194, 195, 196, 200, 202, 206, 208, 209, 211, 212, 213, 215, 218, 219, 221, 223, 224, 225, 229, 230, 240, 242, 243, 244, 248, 249, 251, 252, 253, 255, 256, 258, 259, 260, 266, 272, 280, 281, 282, 287, 295, 296, 297, 299, 300, 302, 309, 310, 311, 316, 324, 326, 328, 329, 330, 338, 339, 340, 347, 348, 350, 354, 356, 357, 359, 360, 363, 364, 365, 367, 368, 369, 371, 372, 373, 375, 376, 377, 379], "excluded_lines": [], "executed_branches": [], "missing_branches": [[70, 71], [70, 73], [78, 79], [78, 81], [84, 85], [84, 88], [85, 84], [85, 86], [91, 92], [91, 94], [105, 106], [105, 109], [112, 113], [112, 119], [113, 114], [113, 115], [115, 112], [115, 116], [119, 120], [119, 121], [121, 122], [121, 124], [132, 133], [132, 135], [139, 140], [139, 142], [145, 146], [145, 149], [146, 145], [146, 147], [162, 163], [162, 166], [175, 176], [175, 180], [183, 184], [183, 186], [189, 190], [189, 192], [208, 209], [208, 211], [218, 219], [218, 221], [248, 249], [248, 251], [258, 259], [258, 272], [259, 260], [259, 266], [329, 330], [329, 338], [339, 340], [339, 350], [359, 360], [359, 363], [364, 365], [364, 367], [368, 369], [368, 371], [372, 373], [372, 379]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 16, 19, 37, 68, 100, 130, 156, 198, 227, 246, 284, 293, 313, 322, 352], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\metrics\\coverage_monitor.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 138, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 138, "excluded_lines": 0, "num_branches": 58, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 58}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 19, 20, 21, 22, 25, 35, 37, 39, 48, 49, 52, 54, 55, 56, 58, 60, 61, 64, 66, 68, 69, 70, 72, 73, 76, 79, 80, 82, 83, 84, 85, 86, 89, 91, 104, 106, 107, 108, 110, 112, 114, 118, 120, 121, 122, 123, 124, 125, 126, 129, 134, 135, 137, 139, 140, 143, 144, 145, 146, 147, 148, 149, 150, 151, 153, 155, 161, 162, 164, 171, 173, 179, 180, 181, 183, 184, 187, 189, 190, 191, 192, 194, 195, 196, 197, 203, 204, 206, 207, 208, 210, 211, 216, 217, 219, 221, 223, 240, 242, 243, 244, 245, 247, 248, 251, 252, 253, 258, 260, 261, 263, 265, 267, 269, 270, 272, 273, 274, 277, 278, 279, 280, 281, 284, 285, 287, 288, 289, 290, 292], "excluded_lines": [], "executed_branches": [], "missing_branches": [[48, 49], [48, 52], [60, 61], [60, 64], [69, 70], [69, 72], [82, 83], [82, 89], [84, 82], [84, 85], [118, 120], [118, 164], [121, 122], [121, 164], [122, 121], [122, 123], [125, 121], [125, 126], [137, 139], [137, 143], [144, 145], [144, 153], [145, 144], [145, 146], [148, 149], [148, 150], [150, 144], [150, 151], [180, 181], [180, 183], [189, 190], [189, 219], [191, 189], [191, 192], [194, 195], [194, 196], [196, 197], [196, 203], [206, 207], [206, 210], [207, 206], [207, 208], [210, 189], [210, 211], [244, 245], [244, 247], [251, 252], [251, 263], [272, 273], [272, 277], [278, 279], [278, 284], [280, 278], [280, 281], [287, 288], [287, 292], [289, 290], [289, 292]], "functions": {"CoverageMonitor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [19, 20, 21, 22, 25], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CoverageMonitor.run_coverage_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [37, 39, 48, 49, 52, 54, 55, 56], "excluded_lines": [], "executed_branches": [], "missing_branches": [[48, 49], [48, 52]]}, "CoverageMonitor.get_current_coverage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [60, 61, 64], "excluded_lines": [], "executed_branches": [], "missing_branches": [[60, 61], [60, 64]]}, "CoverageMonitor._parse_coverage_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [68, 69, 70, 72, 73, 76, 79, 80, 82, 83, 84, 85, 86, 89, 91, 104, 106, 107, 108], "excluded_lines": [], "executed_branches": [], "missing_branches": [[69, 70], [69, 72], [82, 83], [82, 89], [84, 82], [84, 85]]}, "CoverageMonitor._get_test_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 30, "excluded_lines": 0, "num_branches": 18, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 18}, "missing_lines": [112, 114, 118, 120, 121, 122, 123, 124, 125, 126, 129, 134, 135, 137, 139, 140, 143, 144, 145, 146, 147, 148, 149, 150, 151, 153, 155, 161, 162, 164], "excluded_lines": [], "executed_branches": [], "missing_branches": [[118, 120], [118, 164], [121, 122], [121, 164], [122, 121], [122, 123], [125, 121], [125, 126], [137, 139], [137, 143], [144, 145], [144, 153], [145, 144], [145, 146], [148, 149], [148, 150], [150, 144], [150, 151]]}, "CoverageMonitor._get_detailed_coverage_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 25, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [173, 179, 180, 181, 183, 184, 187, 189, 190, 191, 192, 194, 195, 196, 197, 203, 204, 206, 207, 208, 210, 211, 216, 217, 219], "excluded_lines": [], "executed_branches": [], "missing_branches": [[180, 181], [180, 183], [189, 190], [189, 219], [191, 189], [191, 192], [194, 195], [194, 196], [196, 197], [196, 203], [206, 207], [206, 210], [207, 206], [207, 208], [210, 189], [210, 211]]}, "CoverageMonitor._get_default_coverage_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [223], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CoverageMonitor.get_coverage_trend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [242, 243, 244, 245, 247, 248, 251, 252, 253, 258, 260, 261, 263], "excluded_lines": [], "executed_branches": [], "missing_branches": [[244, 245], [244, 247], [251, 252], [251, 263]]}, "CoverageMonitor.generate_coverage_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [267, 269, 270, 272, 273, 274, 277, 278, 279, 280, 281, 284, 285, 287, 288, 289, 290, 292], "excluded_lines": [], "executed_branches": [], "missing_branches": [[272, 273], [272, 277], [278, 279], [278, 284], [280, 278], [280, 281], [287, 288], [287, 292], [289, 290], [289, 292]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 35, 58, 66, 110, 171, 221, 240, 265], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"CoverageMonitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 122, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 122, "excluded_lines": 0, "num_branches": 58, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 58}, "missing_lines": [19, 20, 21, 22, 25, 37, 39, 48, 49, 52, 54, 55, 56, 60, 61, 64, 68, 69, 70, 72, 73, 76, 79, 80, 82, 83, 84, 85, 86, 89, 91, 104, 106, 107, 108, 112, 114, 118, 120, 121, 122, 123, 124, 125, 126, 129, 134, 135, 137, 139, 140, 143, 144, 145, 146, 147, 148, 149, 150, 151, 153, 155, 161, 162, 164, 173, 179, 180, 181, 183, 184, 187, 189, 190, 191, 192, 194, 195, 196, 197, 203, 204, 206, 207, 208, 210, 211, 216, 217, 219, 223, 242, 243, 244, 245, 247, 248, 251, 252, 253, 258, 260, 261, 263, 267, 269, 270, 272, 273, 274, 277, 278, 279, 280, 281, 284, 285, 287, 288, 289, 290, 292], "excluded_lines": [], "executed_branches": [], "missing_branches": [[48, 49], [48, 52], [60, 61], [60, 64], [69, 70], [69, 72], [82, 83], [82, 89], [84, 82], [84, 85], [118, 120], [118, 164], [121, 122], [121, 164], [122, 121], [122, 123], [125, 121], [125, 126], [137, 139], [137, 143], [144, 145], [144, 153], [145, 144], [145, 146], [148, 149], [148, 150], [150, 144], [150, 151], [180, 181], [180, 183], [189, 190], [189, 219], [191, 189], [191, 192], [194, 195], [194, 196], [196, 197], [196, 203], [206, 207], [206, 210], [207, 206], [207, 208], [210, 189], [210, 211], [244, 245], [244, 247], [251, 252], [251, 263], [272, 273], [272, 277], [278, 279], [278, 284], [280, 278], [280, 281], [287, 288], [287, 292], [289, 290], [289, 292]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 35, 58, 66, 110, 171, 221, 240, 265], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\metrics\\cycle_time_tracker.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 50, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 50, "excluded_lines": 0, "num_branches": 14, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 14}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 19, 20, 22, 24, 27, 34, 36, 38, 40, 41, 50, 51, 52, 53, 54, 56, 74, 76, 77, 78, 86, 88, 91, 92, 101, 103, 105, 107, 109, 110, 113, 114, 115, 118, 119, 122, 123, 126, 127, 130, 131, 133], "excluded_lines": [], "executed_branches": [], "missing_branches": [[40, 41], [40, 50], [91, 92], [91, 103], [109, 110], [109, 113], [118, 119], [118, 122], [122, 123], [122, 126], [126, 127], [126, 130], [130, 131], [130, 133]], "functions": {"CycleTimeTracker.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [19, 20, 22, 24, 27], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CycleTimeTracker.get_cycle_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [36, 38, 40, 41, 50, 51, 52, 53, 54, 56, 74, 76, 77, 78], "excluded_lines": [], "executed_branches": [], "missing_branches": [[40, 41], [40, 50]]}, "CycleTimeTracker._generate_mock_cycle_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [88, 91, 92, 101, 103], "excluded_lines": [], "executed_branches": [], "missing_branches": [[91, 92], [91, 103]]}, "CycleTimeTracker._generate_time_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [107, 109, 110, 113, 114, 115, 118, 119, 122, 123, 126, 127, 130, 131, 133], "excluded_lines": [], "executed_branches": [], "missing_branches": [[109, 110], [109, 113], [118, 119], [118, 122], [122, 123], [122, 126], [126, 127], [126, 130], [130, 131], [130, 133]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 34, 86, 105], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"CycleTimeTracker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 39, "excluded_lines": 0, "num_branches": 14, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 14}, "missing_lines": [19, 20, 22, 24, 27, 36, 38, 40, 41, 50, 51, 52, 53, 54, 56, 74, 76, 77, 78, 88, 91, 92, 101, 103, 107, 109, 110, 113, 114, 115, 118, 119, 122, 123, 126, 127, 130, 131, 133], "excluded_lines": [], "executed_branches": [], "missing_branches": [[40, 41], [40, 50], [91, 92], [91, 103], [109, 110], [109, 113], [118, 119], [118, 122], [122, 123], [122, 126], [126, 127], [126, 130], [130, 131], [130, 133]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 34, 86, 105], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\metrics\\metrics_reporter.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 193, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 193, "excluded_lines": 0, "num_branches": 42, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 42}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 17, 20, 21, 22, 24, 25, 27, 29, 31, 32, 34, 35, 48, 49, 52, 54, 56, 57, 58, 60, 62, 63, 64, 65, 67, 69, 71, 84, 85, 88, 90, 92, 93, 94, 96, 98, 100, 102, 115, 116, 118, 120, 121, 122, 124, 126, 128, 148, 150, 152, 159, 161, 163, 169, 171, 173, 178, 180, 182, 187, 189, 192, 193, 195, 196, 203, 205, 207, 210, 212, 213, 215, 216, 218, 220, 222, 226, 227, 228, 229, 237, 239, 241, 242, 245, 246, 247, 248, 250, 259, 261, 262, 264, 265, 267, 274, 276, 277, 278, 279, 280, 281, 283, 285, 287, 288, 289, 290, 291, 292, 294, 296, 298, 299, 300, 301, 302, 304, 305, 306, 308, 310, 311, 312, 314, 316, 327, 329, 336, 337, 338, 339, 341, 348, 350, 352, 354, 355, 356, 357, 358, 359, 361, 363, 365, 366, 367, 369, 371, 372, 374, 376, 378, 423, 424, 426, 427, 428, 430, 432, 433, 436, 437, 439, 440, 442, 443, 445, 446, 448, 449, 451, 452, 454, 455, 462, 463, 465, 466, 468, 469, 471, 472, 474, 476], "excluded_lines": [], "executed_branches": [], "missing_branches": [[31, 32], [31, 34], [62, 63], [62, 64], [64, 65], [64, 67], [195, 196], [195, 203], [212, 213], [212, 215], [215, 216], [215, 218], [226, 227], [226, 237], [228, 226], [228, 229], [241, 242], [241, 245], [261, 262], [261, 264], [276, 277], [276, 278], [278, 279], [278, 280], [280, 281], [280, 283], [287, 288], [287, 289], [289, 290], [289, 291], [291, 292], [291, 294], [300, 301], [300, 308], [305, 306], [305, 308], [354, 355], [354, 356], [356, 357], [356, 358], [358, 359], [358, 361]], "functions": {"MetricsReporter.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [21, 22, 24, 25, 27], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter.generate_daily_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [31, 32, 34, 35, 48, 49, 52, 54, 56, 57, 58], "excluded_lines": [], "executed_branches": [], "missing_branches": [[31, 32], [31, 34]]}, "MetricsReporter.generate_weekly_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [62, 63, 64, 65, 67, 69, 71, 84, 85, 88, 90, 92, 93, 94], "excluded_lines": [], "executed_branches": [], "missing_branches": [[62, 63], [62, 64], [64, 65], [64, 67]]}, "MetricsReporter.generate_dashboard_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [98, 100, 102, 115, 116, 118, 120, 121, 122], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [126, 128], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._analyze_coverage_trends": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [150, 152], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._analyze_quality_trends": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [161, 163], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._analyze_performance_trends": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [171, 173], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._analyze_compliance_trends": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [180, 182], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_alerts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [189, 192, 193, 195, 196, 203], "excluded_lines": [], "executed_branches": [], "missing_branches": [[195, 196], [195, 203]]}, "MetricsReporter._generate_daily_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [207, 210, 212, 213, 215, 216, 218], "excluded_lines": [], "executed_branches": [], "missing_branches": [[212, 213], [212, 215], [215, 216], [215, 218]]}, "MetricsReporter._collect_weekly_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [222, 226, 227, 228, 229, 237], "excluded_lines": [], "executed_branches": [], "missing_branches": [[226, 227], [226, 237], [228, 226], [228, 229]]}, "MetricsReporter._generate_weekly_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [241, 242, 245, 246, 247, 248, 250], "excluded_lines": [], "executed_branches": [], "missing_branches": [[241, 242], [241, 245]]}, "MetricsReporter._analyze_weekly_trends": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [261, 262, 264, 265, 267], "excluded_lines": [], "executed_branches": [], "missing_branches": [[261, 262], [261, 264]]}, "MetricsReporter._rate_quality_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [276, 277, 278, 279, 280, 281, 283], "excluded_lines": [], "executed_branches": [], "missing_branches": [[276, 277], [276, 278], [278, 279], [278, 280], [280, 281], [280, 283]]}, "MetricsReporter._rate_performance_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [287, 288, 289, 290, 291, 292, 294], "excluded_lines": [], "executed_branches": [], "missing_branches": [[287, 288], [287, 289], [289, 290], [289, 291], [291, 292], [291, 294]]}, "MetricsReporter._load_latest_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [298, 299, 300, 301, 302, 304, 305, 306, 308, 310, 311, 312], "excluded_lines": [], "executed_branches": [], "missing_branches": [[300, 301], [300, 308], [305, 306], [305, 308]]}, "MetricsReporter._generate_overview": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [316], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._calculate_health_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [329, 336, 337, 338, 339, 341, 348], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._determine_overall_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [352, 354, 355, 356, 357, 358, 359, 361], "excluded_lines": [], "executed_branches": [], "missing_branches": [[354, 355], [354, 356], [356, 357], [356, 358], [358, 359], [358, 361]]}, "MetricsReporter._save_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [365, 366, 367, 369, 371, 372], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_html_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [376, 378, 423, 424, 426, 427, 428, 430, 432, 433], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_coverage_widget": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [437], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_quality_widget": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [440], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_performance_widget": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [443], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_compliance_widget": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [446], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._get_recent_activities": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [449], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._get_active_alerts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [452], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_quick_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [455], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._identify_achievements": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [463], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._identify_issues": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [466], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._set_weekly_goals": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [469], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_weekly_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [472], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricsReporter._generate_weekly_charts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [476], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 43, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 17, 20, 29, 60, 96, 124, 148, 159, 169, 178, 187, 205, 220, 239, 259, 274, 285, 296, 314, 327, 350, 363, 374, 436, 439, 442, 445, 448, 451, 454, 462, 465, 468, 471, 474], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"MetricsReporter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 150, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 150, "excluded_lines": 0, "num_branches": 42, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 42}, "missing_lines": [21, 22, 24, 25, 27, 31, 32, 34, 35, 48, 49, 52, 54, 56, 57, 58, 62, 63, 64, 65, 67, 69, 71, 84, 85, 88, 90, 92, 93, 94, 98, 100, 102, 115, 116, 118, 120, 121, 122, 126, 128, 150, 152, 161, 163, 171, 173, 180, 182, 189, 192, 193, 195, 196, 203, 207, 210, 212, 213, 215, 216, 218, 222, 226, 227, 228, 229, 237, 241, 242, 245, 246, 247, 248, 250, 261, 262, 264, 265, 267, 276, 277, 278, 279, 280, 281, 283, 287, 288, 289, 290, 291, 292, 294, 298, 299, 300, 301, 302, 304, 305, 306, 308, 310, 311, 312, 316, 329, 336, 337, 338, 339, 341, 348, 352, 354, 355, 356, 357, 358, 359, 361, 365, 366, 367, 369, 371, 372, 376, 378, 423, 424, 426, 427, 428, 430, 432, 433, 437, 440, 443, 446, 449, 452, 455, 463, 466, 469, 472, 476], "excluded_lines": [], "executed_branches": [], "missing_branches": [[31, 32], [31, 34], [62, 63], [62, 64], [64, 65], [64, 67], [195, 196], [195, 203], [212, 213], [212, 215], [215, 216], [215, 218], [226, 227], [226, 237], [228, 226], [228, 229], [241, 242], [241, 245], [261, 262], [261, 264], [276, 277], [276, 278], [278, 279], [278, 280], [280, 281], [280, 283], [287, 288], [287, 289], [289, 290], [289, 291], [291, 292], [291, 294], [300, 301], [300, 308], [305, 306], [305, 308], [354, 355], [354, 356], [356, 357], [356, 358], [358, 359], [358, 361]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 43, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 17, 20, 29, 60, 96, 124, 148, 159, 169, 178, 187, 205, 220, 239, 259, 274, 285, 296, 314, 327, 350, 363, 374, 436, 439, 442, 445, 448, 451, 454, 462, 465, 468, 471, 474], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\metrics\\performance_tracker.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 54, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 54, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 19, 20, 22, 25, 32, 34, 36, 37, 40, 42, 43, 45, 46, 47, 49, 51, 52, 55, 59, 62, 64, 65, 68, 69, 71, 72, 73, 74, 75, 76, 78, 80, 81, 84, 88, 91, 93, 94, 97, 98, 100, 101, 102, 103, 104, 105], "excluded_lines": [], "executed_branches": [], "missing_branches": [[62, 64], [62, 68], [91, 93], [91, 97]], "functions": {"PerformanceTracker.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [19, 20, 22, 25], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PerformanceTracker.get_performance_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [34, 36, 37, 40, 42, 43, 45, 46, 47], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PerformanceTracker._measure_test_execution_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [51, 52, 55, 59, 62, 64, 65, 68, 69, 71, 72, 73, 74, 75, 76], "excluded_lines": [], "executed_branches": [], "missing_branches": [[62, 64], [62, 68]]}, "PerformanceTracker._measure_coverage_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [80, 81, 84, 88, 91, 93, 94, 97, 98, 100, 101, 102, 103, 104, 105], "excluded_lines": [], "executed_branches": [], "missing_branches": [[91, 93], [91, 97]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 32, 49, 78], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"PerformanceTracker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 43, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [19, 20, 22, 25, 34, 36, 37, 40, 42, 43, 45, 46, 47, 51, 52, 55, 59, 62, 64, 65, 68, 69, 71, 72, 73, 74, 75, 76, 80, 81, 84, 88, 91, 93, 94, 97, 98, 100, 101, 102, 103, 104, 105], "excluded_lines": [], "executed_branches": [], "missing_branches": [[62, 64], [62, 68], [91, 93], [91, 97]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 15, 18, 32, 49, 78], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\metrics\\quality_analyzer.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 212, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 212, "excluded_lines": 0, "num_branches": 62, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 62}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 17, 20, 21, 22, 25, 33, 35, 37, 38, 39, 40, 41, 44, 52, 60, 61, 63, 64, 65, 67, 69, 70, 71, 73, 74, 76, 77, 78, 80, 81, 82, 85, 87, 88, 90, 92, 93, 95, 96, 98, 101, 102, 105, 106, 108, 109, 110, 112, 114, 115, 116, 118, 119, 121, 122, 123, 125, 126, 127, 130, 131, 132, 134, 135, 137, 138, 140, 143, 144, 147, 149, 150, 151, 153, 155, 157, 158, 160, 161, 162, 164, 165, 166, 169, 174, 175, 177, 178, 180, 181, 184, 185, 186, 189, 190, 192, 193, 195, 196, 197, 199, 201, 203, 207, 208, 211, 212, 214, 215, 216, 219, 221, 222, 225, 228, 229, 231, 232, 234, 235, 236, 238, 240, 242, 246, 247, 249, 250, 253, 254, 255, 258, 260, 262, 264, 266, 267, 268, 270, 272, 274, 275, 276, 278, 279, 280, 283, 288, 290, 291, 293, 295, 297, 298, 308, 310, 311, 312, 314, 316, 317, 319, 320, 321, 323, 324, 325, 327, 329, 330, 331, 338, 339, 341, 346, 347, 348, 350, 353, 358, 361, 366, 369, 374, 377, 382, 384, 386, 387, 389, 390, 393, 394, 395, 397, 398, 399, 401, 402, 403, 405, 406, 407, 409], "excluded_lines": [], "executed_branches": [], "missing_branches": [[70, 71], [70, 73], [76, 77], [76, 95], [77, 78], [77, 80], [87, 88], [87, 90], [95, 96], [95, 98], [101, 102], [101, 105], [115, 116], [115, 118], [121, 122], [121, 137], [122, 123], [122, 125], [137, 138], [137, 140], [143, 144], [143, 147], [160, 161], [160, 180], [161, 162], [161, 164], [180, 181], [180, 184], [189, 190], [189, 192], [207, 208], [207, 211], [214, 215], [214, 219], [215, 214], [215, 216], [221, 222], [221, 225], [228, 229], [228, 231], [246, 247], [246, 249], [274, 275], [274, 293], [275, 276], [275, 278], [319, 320], [319, 341], [320, 321], [320, 323], [329, 319], [329, 330], [330, 329], [330, 331], [389, 390], [389, 393], [394, 395], [394, 397], [398, 399], [398, 401], [402, 403], [402, 409]], "functions": {"QualityAnalyzer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [21, 22, 25], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QualityAnalyzer.analyze_code_quality": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [35, 37, 38, 39, 40, 41, 44, 52, 60, 61, 63, 64, 65], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QualityAnalyzer._analyze_complexity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 27, "excluded_lines": 0, "num_branches": 12, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 12}, "missing_lines": [69, 70, 71, 73, 74, 76, 77, 78, 80, 81, 82, 85, 87, 88, 90, 92, 93, 95, 96, 98, 101, 102, 105, 106, 108, 109, 110], "excluded_lines": [], "executed_branches": [], "missing_branches": [[70, 71], [70, 73], [76, 77], [76, 95], [77, 78], [77, 80], [87, 88], [87, 90], [95, 96], [95, 98], [101, 102], [101, 105]]}, "QualityAnalyzer._analyze_maintainability": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 25, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [114, 115, 116, 118, 119, 121, 122, 123, 125, 126, 127, 130, 131, 132, 134, 135, 137, 138, 140, 143, 144, 147, 149, 150, 151], "excluded_lines": [], "executed_branches": [], "missing_branches": [[115, 116], [115, 118], [121, 122], [121, 137], [122, 123], [122, 125], [137, 138], [137, 140], [143, 144], [143, 147]]}, "QualityAnalyzer._analyze_duplication": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 26, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [155, 157, 158, 160, 161, 162, 164, 165, 166, 169, 174, 175, 177, 178, 180, 181, 184, 185, 186, 189, 190, 192, 193, 195, 196, 197], "excluded_lines": [], "executed_branches": [], "missing_branches": [[160, 161], [160, 180], [161, 162], [161, 164], [180, 181], [180, 184], [189, 190], [189, 192]]}, "QualityAnalyzer._analyze_code_style": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [201, 203, 207, 208, 211, 212, 214, 215, 216, 219, 221, 222, 225, 228, 229, 231, 232, 234, 235, 236], "excluded_lines": [], "executed_branches": [], "missing_branches": [[207, 208], [207, 211], [214, 215], [214, 219], [215, 214], [215, 216], [221, 222], [221, 225], [228, 229], [228, 231]]}, "QualityAnalyzer._analyze_security": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [240, 242, 246, 247, 249, 250, 253, 254, 255, 258, 260, 262, 264, 266, 267, 268], "excluded_lines": [], "executed_branches": [], "missing_branches": [[246, 247], [246, 249]]}, "QualityAnalyzer._count_code_lines": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [272, 274, 275, 276, 278, 279, 280, 283, 288, 290, 291, 293], "excluded_lines": [], "executed_branches": [], "missing_branches": [[274, 275], [274, 293], [275, 276], [275, 278]]}, "QualityAnalyzer.get_detailed_quality_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [297, 298, 308, 310, 311, 312], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QualityAnalyzer._get_complexity_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [316, 317, 319, 320, 321, 323, 324, 325, 327, 329, 330, 331, 338, 339, 341, 346, 347, 348], "excluded_lines": [], "executed_branches": [], "missing_branches": [[319, 320], [319, 341], [320, 321], [320, 323], [329, 319], [329, 330], [330, 329], [330, 331]]}, "QualityAnalyzer._get_maintainability_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [353], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QualityAnalyzer._get_duplication_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [361], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QualityAnalyzer._get_style_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [369], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QualityAnalyzer._get_security_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [377], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "QualityAnalyzer._generate_quality_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [384, 386, 387, 389, 390, 393, 394, 395, 397, 398, 399, 401, 402, 403, 405, 406, 407, 409], "excluded_lines": [], "executed_branches": [], "missing_branches": [[389, 390], [389, 393], [394, 395], [394, 397], [398, 399], [398, 401], [402, 403], [402, 409]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 17, 20, 33, 67, 112, 153, 199, 238, 270, 295, 314, 350, 358, 366, 374, 382], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"QualityAnalyzer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 188, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 188, "excluded_lines": 0, "num_branches": 62, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 62}, "missing_lines": [21, 22, 25, 35, 37, 38, 39, 40, 41, 44, 52, 60, 61, 63, 64, 65, 69, 70, 71, 73, 74, 76, 77, 78, 80, 81, 82, 85, 87, 88, 90, 92, 93, 95, 96, 98, 101, 102, 105, 106, 108, 109, 110, 114, 115, 116, 118, 119, 121, 122, 123, 125, 126, 127, 130, 131, 132, 134, 135, 137, 138, 140, 143, 144, 147, 149, 150, 151, 155, 157, 158, 160, 161, 162, 164, 165, 166, 169, 174, 175, 177, 178, 180, 181, 184, 185, 186, 189, 190, 192, 193, 195, 196, 197, 201, 203, 207, 208, 211, 212, 214, 215, 216, 219, 221, 222, 225, 228, 229, 231, 232, 234, 235, 236, 240, 242, 246, 247, 249, 250, 253, 254, 255, 258, 260, 262, 264, 266, 267, 268, 272, 274, 275, 276, 278, 279, 280, 283, 288, 290, 291, 293, 297, 298, 308, 310, 311, 312, 316, 317, 319, 320, 321, 323, 324, 325, 327, 329, 330, 331, 338, 339, 341, 346, 347, 348, 353, 361, 369, 377, 384, 386, 387, 389, 390, 393, 394, 395, 397, 398, 399, 401, 402, 403, 405, 406, 407, 409], "excluded_lines": [], "executed_branches": [], "missing_branches": [[70, 71], [70, 73], [76, 77], [76, 95], [77, 78], [77, 80], [87, 88], [87, 90], [95, 96], [95, 98], [101, 102], [101, 105], [115, 116], [115, 118], [121, 122], [121, 137], [122, 123], [122, 125], [137, 138], [137, 140], [143, 144], [143, 147], [160, 161], [160, 180], [161, 162], [161, 164], [180, 181], [180, 184], [189, 190], [189, 192], [207, 208], [207, 211], [214, 215], [214, 219], [215, 214], [215, 216], [221, 222], [221, 225], [228, 229], [228, 231], [246, 247], [246, 249], [274, 275], [274, 293], [275, 276], [275, 278], [319, 320], [319, 341], [320, 321], [320, 323], [329, 319], [329, 330], [330, 329], [330, 331], [389, 390], [389, 393], [394, 395], [394, 397], [398, 399], [398, 401], [402, 403], [402, 409]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 17, 20, 33, 67, 112, 153, 199, 238, 270, 295, 314, 350, 358, 366, 374, 382], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\metrics\\tdd_metrics_collector.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 129, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 129, "excluded_lines": 0, "num_branches": 30, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 30}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 39, 42, 43, 44, 46, 47, 50, 51, 52, 53, 54, 57, 60, 61, 63, 65, 67, 69, 72, 75, 78, 81, 84, 85, 87, 102, 103, 105, 106, 108, 123, 125, 127, 130, 131, 137, 141, 142, 144, 146, 147, 149, 151, 152, 153, 154, 156, 160, 161, 162, 163, 165, 167, 168, 171, 172, 177, 178, 181, 182, 184, 202, 206, 207, 208, 209, 210, 211, 212, 213, 215, 217, 218, 219, 220, 221, 223, 226, 227, 228, 229, 231, 233, 234, 236, 243, 245, 247, 248, 254, 255, 261, 262, 268, 270, 272, 274, 275, 277, 278, 280, 281, 283], "excluded_lines": [], "executed_branches": [], "missing_branches": [[152, -149], [152, 153], [167, 168], [167, 171], [177, 178], [177, 181], [206, 207], [206, 213], [208, 209], [208, 213], [211, 212], [211, 213], [217, 218], [217, 221], [219, 220], [219, 221], [226, 227], [226, 229], [247, 248], [247, 254], [254, 255], [254, 261], [261, 262], [261, 268], [274, 275], [274, 277], [277, 278], [277, 280], [280, 281], [280, 283]], "functions": {"TDDMetricsCollector.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [43, 44, 46, 47, 50, 51, 52, 53, 54, 57, 60, 61], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TDDMetricsCollector.collect_current_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [65, 67, 69, 72, 75, 78, 81, 84, 85, 87, 102, 103, 105, 106, 108], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TDDMetricsCollector.save_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [125, 127, 130, 131, 137, 141, 142, 144, 146, 147], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TDDMetricsCollector.load_metrics_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [151, 152, 153, 154, 156, 160, 161, 162, 163], "excluded_lines": [], "executed_branches": [], "missing_branches": [[152, -149], [152, 153]]}, "TDDMetricsCollector.get_trend_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [167, 168, 171, 172, 177, 178, 181, 182, 184], "excluded_lines": [], "executed_branches": [], "missing_branches": [[167, 168], [167, 171], [177, 178], [177, 181]]}, "TDDMetricsCollector._calculate_development_velocity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [206, 207, 208, 209, 210, 211, 212, 213], "excluded_lines": [], "executed_branches": [], "missing_branches": [[206, 207], [206, 213], [208, 209], [208, 213], [211, 212], [211, 213]]}, "TDDMetricsCollector._calculate_rework_rate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [217, 218, 219, 220, 221], "excluded_lines": [], "executed_branches": [], "missing_branches": [[217, 218], [217, 221], [219, 220], [219, 221]]}, "TDDMetricsCollector._calculate_defect_density": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [226, 227, 228, 229], "excluded_lines": [], "executed_branches": [], "missing_branches": [[226, 227], [226, 229]]}, "TDDMetricsCollector.generate_real_time_dashboard_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [233, 234, 236], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TDDMetricsCollector._check_quality_alerts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [245, 247, 248, 254, 255, 261, 262, 268], "excluded_lines": [], "executed_branches": [], "missing_branches": [[247, 248], [247, 254], [254, 255], [254, 261], [261, 262], [261, 268]]}, "TDDMetricsCollector._generate_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [272, 274, 275, 277, 278, 280, 281, 283], "excluded_lines": [], "executed_branches": [], "missing_branches": [[274, 275], [274, 277], [277, 278], [277, 280], [280, 281], [280, 283]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 38, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 38, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 39, 42, 63, 123, 149, 165, 202, 215, 223, 231, 243, 270], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"TDDMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TDDMetricsCollector": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 91, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 91, "excluded_lines": 0, "num_branches": 30, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 30}, "missing_lines": [43, 44, 46, 47, 50, 51, 52, 53, 54, 57, 60, 61, 65, 67, 69, 72, 75, 78, 81, 84, 85, 87, 102, 103, 105, 106, 108, 125, 127, 130, 131, 137, 141, 142, 144, 146, 147, 151, 152, 153, 154, 156, 160, 161, 162, 163, 167, 168, 171, 172, 177, 178, 181, 182, 184, 206, 207, 208, 209, 210, 211, 212, 213, 217, 218, 219, 220, 221, 226, 227, 228, 229, 233, 234, 236, 245, 247, 248, 254, 255, 261, 262, 268, 272, 274, 275, 277, 278, 280, 281, 283], "excluded_lines": [], "executed_branches": [], "missing_branches": [[152, -149], [152, 153], [167, 168], [167, 171], [177, 178], [177, 181], [206, 207], [206, 213], [208, 209], [208, 213], [211, 212], [211, 213], [217, 218], [217, 221], [219, 220], [219, 221], [226, 227], [226, 229], [247, 248], [247, 254], [254, 255], [254, 261], [261, 262], [261, 268], [274, 275], [274, 277], [277, 278], [277, 280], [280, 281], [280, 283]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 38, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 38, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 39, 42, 63, 123, 149, 165, 202, 215, 223, 231, 243, 270], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\speech_recognition\\__init__.py": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\speech_synthesis\\__init__.py": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\translation\\__init__.py": {"executed_lines": [1, 9], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 9], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 9], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\ui\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 10, 13, 14, 16], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 10, 13, 14, 16], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 10, 13, 14, 16], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\ui\\file_drop_area.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 201, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 201, "excluded_lines": 0, "num_branches": 42, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 42}, "missing_lines": [10, 11, 12, 13, 15, 18, 19, 21, 24, 26, 27, 28, 29, 30, 33, 36, 38, 82, 83, 86, 87, 88, 91, 94, 95, 96, 97, 98, 99, 100, 103, 109, 116, 117, 118, 121, 122, 125, 126, 127, 130, 133, 134, 136, 138, 140, 142, 144, 146, 148, 149, 151, 152, 153, 155, 158, 159, 160, 161, 162, 164, 166, 168, 169, 170, 172, 175, 176, 179, 180, 181, 182, 183, 187, 189, 191, 193, 195, 198, 199, 202, 204, 207, 208, 210, 211, 212, 213, 215, 217, 219, 220, 223, 225, 227, 228, 229, 231, 233, 235, 237, 238, 240, 242, 244, 245, 246, 247, 249, 251, 253, 254, 257, 260, 263, 264, 265, 266, 267, 269, 270, 272, 273, 276, 278, 279, 281, 283, 286, 287, 288, 289, 292, 293, 294, 295, 296, 299, 300, 303, 306, 307, 308, 311, 312, 317, 319, 320, 321, 322, 324, 333, 334, 335, 336, 339, 340, 341, 343, 344, 345, 348, 349, 352, 353, 355, 357, 358, 359, 360, 362, 364, 366, 368, 370, 372, 374, 376, 378, 380, 382, 384, 386, 393, 394, 397, 399, 401, 402, 404, 406, 415, 417, 418, 419, 421, 431, 433, 434, 436, 438], "excluded_lines": [], "executed_branches": [], "missing_branches": [[18, 19], [18, 21], [148, 149], [148, 151], [158, -146], [158, 159], [179, 180], [179, 187], [193, 195], [193, 207], [235, 237], [235, 242], [237, 238], [237, 240], [263, 264], [263, 269], [272, 273], [272, 286], [276, 278], [276, 283], [278, 279], [278, 281], [286, 287], [286, 292], [292, 293], [292, 299], [294, 295], [294, 296], [311, 312], [311, 317], [334, 335], [334, 339], [339, 340], [339, 343], [343, 344], [343, 348], [352, 353], [352, 355], [393, 394], [393, 397], [418, 419], [418, 421]], "functions": {"FileDropAreaConfig.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [38, 82, 83, 86, 87, 88], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DropAreaMetrics.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [95, 96, 97, 98, 99, 100], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [116, 117, 118, 121, 122, 125, 126, 127, 130, 133, 134, 136], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.add_state_change_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [140], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.add_error_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [144], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea._change_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [148, 149, 151, 152, 153, 155, 158, 159, 160, 161, 162], "excluded_lines": [], "executed_branches": [], "missing_branches": [[148, 149], [148, 151], [158, -146], [158, 159]]}, "FileDropArea._handle_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [166, 168, 169, 170, 172, 175, 176, 179, 180, 181, 182, 183, 187], "excluded_lines": [], "executed_branches": [], "missing_branches": [[179, 180], [179, 187]]}, "FileDropArea.dragEnterEvent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [191, 193, 195, 198, 199, 202, 204, 207, 208, 210, 211, 212, 213], "excluded_lines": [], "executed_branches": [], "missing_branches": [[193, 195], [193, 207]]}, "FileDropArea.dragLeaveEvent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [217, 219, 220, 223, 225, 227, 228, 229], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.dragMoveEvent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [233, 235, 237, 238, 240, 242, 244, 245, 246, 247], "excluded_lines": [], "executed_branches": [], "missing_branches": [[235, 237], [235, 242], [237, 238], [237, 240]]}, "FileDropArea.dropEvent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 41, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [251, 253, 254, 257, 260, 263, 264, 265, 266, 267, 269, 270, 272, 273, 276, 278, 279, 281, 283, 286, 287, 288, 289, 292, 293, 294, 295, 296, 299, 300, 303, 306, 307, 308, 311, 312, 317, 319, 320, 321, 322], "excluded_lines": [], "executed_branches": [], "missing_branches": [[263, 264], [263, 269], [272, 273], [272, 286], [276, 278], [276, 283], [278, 279], [278, 281], [286, 287], [286, 292], [292, 293], [292, 299], [294, 295], [294, 296], [311, 312], [311, 317]]}, "FileDropArea.add_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [333, 334, 335, 336, 339, 340, 341, 343, 344, 345, 348, 349, 352, 353, 355, 357, 358, 359, 360], "excluded_lines": [], "executed_branches": [], "missing_branches": [[334, 335], [334, 339], [339, 340], [339, 343], [343, 344], [343, 348], [352, 353], [352, 355]]}, "FileDropArea.get_current_style": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [364], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.get_css_class": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [368], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.get_border_style": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [372], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.get_background_color": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [376], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.get_border_color": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [380], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.get_text_color": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [384], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.animate_highlight": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [393, 394, 397], "excluded_lines": [], "executed_branches": [], "missing_branches": [[393, 394], [393, 397]]}, "FileDropArea.clear_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [401, 402], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.get_state_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [406], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.get_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [417, 418, 419, 421], "excluded_lines": [], "executed_branches": [], "missing_branches": [[418, 419], [418, 421]]}, "FileDropArea.reset_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [433, 434], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea.is_available": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [438], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 41, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [10, 11, 12, 13, 15, 18, 19, 21, 24, 26, 27, 28, 29, 30, 33, 36, 91, 94, 103, 109, 138, 142, 146, 164, 189, 215, 231, 249, 324, 362, 366, 370, 374, 378, 382, 386, 399, 404, 415, 431, 436], "excluded_lines": [], "executed_branches": [], "missing_branches": [[18, 19], [18, 21]]}}, "classes": {"DropAreaState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropAreaConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [38, 82, 83, 86, 87, 88], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DropAreaMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [95, 96, 97, 98, 99, 100], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileDropArea": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 148, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 148, "excluded_lines": 0, "num_branches": 40, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 40}, "missing_lines": [116, 117, 118, 121, 122, 125, 126, 127, 130, 133, 134, 136, 140, 144, 148, 149, 151, 152, 153, 155, 158, 159, 160, 161, 162, 166, 168, 169, 170, 172, 175, 176, 179, 180, 181, 182, 183, 187, 191, 193, 195, 198, 199, 202, 204, 207, 208, 210, 211, 212, 213, 217, 219, 220, 223, 225, 227, 228, 229, 233, 235, 237, 238, 240, 242, 244, 245, 246, 247, 251, 253, 254, 257, 260, 263, 264, 265, 266, 267, 269, 270, 272, 273, 276, 278, 279, 281, 283, 286, 287, 288, 289, 292, 293, 294, 295, 296, 299, 300, 303, 306, 307, 308, 311, 312, 317, 319, 320, 321, 322, 333, 334, 335, 336, 339, 340, 341, 343, 344, 345, 348, 349, 352, 353, 355, 357, 358, 359, 360, 364, 368, 372, 376, 380, 384, 393, 394, 397, 401, 402, 406, 417, 418, 419, 421, 433, 434, 438], "excluded_lines": [], "executed_branches": [], "missing_branches": [[148, 149], [148, 151], [158, -146], [158, 159], [179, 180], [179, 187], [193, 195], [193, 207], [235, 237], [235, 242], [237, 238], [237, 240], [263, 264], [263, 269], [272, 273], [272, 286], [276, 278], [276, 283], [278, 279], [278, 281], [286, 287], [286, 292], [292, 293], [292, 299], [294, 295], [294, 296], [311, 312], [311, 317], [334, 335], [334, 339], [339, 340], [339, 343], [343, 344], [343, 348], [352, 353], [352, 355], [393, 394], [393, 397], [418, 419], [418, 421]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 41, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [10, 11, 12, 13, 15, 18, 19, 21, 24, 26, 27, 28, 29, 30, 33, 36, 91, 94, 103, 109, 138, 142, 146, 164, 189, 215, 231, 249, 324, 362, 366, 370, 374, 378, 382, 386, 399, 404, 415, 431, 436], "excluded_lines": [], "executed_branches": [], "missing_branches": [[18, 19], [18, 21]]}}}, "src\\voice_came\\ui\\upload_widget.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 318, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 318, "excluded_lines": 0, "num_branches": 70, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 70}, "missing_lines": [10, 11, 12, 13, 15, 18, 19, 21, 24, 26, 27, 28, 29, 30, 33, 39, 46, 47, 48, 49, 50, 51, 52, 53, 56, 57, 58, 60, 62, 63, 64, 65, 66, 67, 68, 69, 71, 73, 74, 76, 77, 78, 79, 80, 82, 84, 85, 86, 87, 89, 91, 98, 101, 104, 106, 107, 108, 109, 111, 112, 114, 116, 118, 120, 122, 123, 124, 125, 127, 129, 130, 132, 134, 135, 136, 138, 140, 141, 142, 143, 145, 147, 148, 150, 157, 159, 161, 164, 167, 174, 175, 176, 177, 179, 181, 183, 185, 187, 189, 191, 193, 195, 197, 198, 199, 200, 201, 202, 205, 208, 210, 211, 212, 213, 215, 217, 219, 221, 223, 225, 227, 229, 231, 233, 236, 242, 245, 246, 247, 248, 249, 250, 253, 254, 255, 258, 259, 260, 263, 264, 267, 270, 272, 274, 277, 280, 281, 283, 285, 295, 296, 298, 303, 312, 314, 315, 316, 319, 320, 321, 324, 325, 326, 329, 330, 331, 334, 335, 336, 337, 339, 342, 343, 346, 348, 349, 351, 352, 353, 355, 357, 358, 359, 360, 361, 362, 363, 364, 365, 367, 376, 377, 380, 381, 382, 385, 386, 387, 390, 391, 392, 393, 396, 397, 398, 399, 400, 402, 403, 404, 406, 407, 408, 410, 412, 413, 414, 416, 425, 426, 427, 428, 431, 433, 434, 436, 437, 439, 440, 441, 443, 445, 446, 447, 450, 451, 454, 456, 458, 459, 461, 463, 466, 467, 470, 471, 473, 476, 477, 479, 481, 483, 484, 486, 489, 491, 492, 494, 496, 497, 498, 499, 501, 502, 503, 505, 506, 509, 512, 513, 514, 515, 516, 518, 520, 521, 522, 523, 525, 527, 528, 529, 532, 533, 534, 535, 538, 540, 542, 543, 545, 552, 553, 554, 557, 558, 559, 562, 563, 564, 565, 566, 568, 569, 571, 573, 575, 577, 579, 581, 582, 583, 584, 586, 594, 596, 598, 600], "excluded_lines": [], "executed_branches": [], "missing_branches": [[18, 19], [18, 21], [63, -39], [63, 64], [73, 74], [73, 76], [76, 77], [76, 80], [77, 78], [77, 79], [122, 123], [122, 125], [134, 135], [134, 136], [140, 141], [140, 143], [141, 140], [141, 142], [197, -195], [197, 198], [198, -195], [198, 199], [314, 315], [314, 319], [319, 320], [319, 324], [324, 325], [324, 329], [329, 330], [329, 334], [358, 359], [358, 365], [360, 358], [360, 361], [363, 358], [363, 364], [380, 381], [380, 385], [385, 386], [385, 390], [391, 392], [391, 396], [398, 399], [398, 402], [402, 403], [402, 410], [426, 427], [426, 436], [470, 471], [470, 473], [476, 477], [476, 479], [484, 486], [484, 489], [497, 498], [497, 501], [501, 502], [501, 505], [512, 513], [512, 518], [532, 533], [532, 538], [533, 532], [533, 534], [553, -545], [553, 554], [562, -545], [562, 563], [582, 583], [582, 586]], "functions": {"FileItem.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [46, 47, 48, 49, 50, 51, 52, 53, 56, 57, 58, 60, 62, 63, 64, 65, 66, 67, 68, 69], "excluded_lines": [], "executed_branches": [], "missing_branches": [[63, -39], [63, 64]]}, "FileItem.get_size_formatted": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [73, 74, 76, 77, 78, 79, 80], "excluded_lines": [], "executed_branches": [], "missing_branches": [[73, 74], [73, 76], [76, 77], [76, 80], [77, 78], [77, 79]]}, "FileItem.is_valid": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [84, 85, 86, 87], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileItem.get_status_text": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [91, 98], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileListWidget.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [106, 107, 108, 109], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileListWidget.item_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [114], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileListWidget.add_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [118], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileListWidget.remove_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [122, 123, 124, 125], "excluded_lines": [], "executed_branches": [], "missing_branches": [[122, 123], [122, 125]]}, "FileListWidget.clear_items": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [129, 130], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileListWidget.get_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [134, 135, 136], "excluded_lines": [], "executed_branches": [], "missing_branches": [[134, 135], [134, 136]]}, "FileListWidget.find_item_by_path": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [140, 141, 142, 143], "excluded_lines": [], "executed_branches": [], "missing_branches": [[140, 141], [140, 143], [141, 140], [141, 142]]}, "FileListWidget.sort_items": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [147, 148, 150, 157], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileListWidget.get_selected_items": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [161], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UIButton.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [174, 175, 176, 177], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UIButton.is_enabled": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [181], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UIButton.set_enabled": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [185], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UIButton.set_text": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [189], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UIButton.add_click_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [193], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UIButton.click": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [197, 198, 199, 200, 201, 202], "excluded_lines": [], "executed_branches": [], "missing_branches": [[197, -195], [197, 198], [198, -195], [198, 199]]}, "ProgressBar.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [210, 211, 212, 213], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProgressBar.set_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [217], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProgressBar.set_text": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [221], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProgressBar.show": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [225], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProgressBar.hide": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [229], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProgressBar.is_visible": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [233], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 17, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [245, 246, 247, 248, 249, 250, 253, 254, 255, 258, 259, 260, 263, 264, 267, 270, 272], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget._setup_connections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [277, 280, 281], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget._create_layout": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [285], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget.button_layout": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [298], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget.add_file_from_drop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 26, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [312, 314, 315, 316, 319, 320, 321, 324, 325, 326, 329, 330, 331, 334, 335, 336, 337, 339, 342, 343, 346, 348, 349, 351, 352, 353], "excluded_lines": [], "executed_branches": [], "missing_branches": [[314, 315], [314, 319], [319, 320], [319, 324], [324, 325], [324, 329], [329, 330], [329, 334]]}, "UploadWidget._is_duplicate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [357, 358, 359, 360, 361, 362, 363, 364, 365], "excluded_lines": [], "executed_branches": [], "missing_branches": [[358, 359], [358, 365], [360, 358], [360, 361], [363, 358], [363, 364]]}, "UploadWidget._validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 27, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [376, 377, 380, 381, 382, 385, 386, 387, 390, 391, 392, 393, 396, 397, 398, 399, 400, 402, 403, 404, 406, 407, 408, 410, 412, 413, 414], "excluded_lines": [], "executed_branches": [], "missing_branches": [[380, 381], [380, 385], [385, 386], [385, 390], [391, 392], [391, 396], [398, 399], [398, 402], [402, 403], [402, 410]]}, "UploadWidget.remove_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [425, 426, 427, 428, 431, 433, 434, 436, 437, 439, 440, 441], "excluded_lines": [], "executed_branches": [], "missing_branches": [[426, 427], [426, 436]]}, "UploadWidget.clear_all_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [445, 446, 447, 450, 451, 454, 456, 458, 459], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget._update_ui_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [463, 466, 467, 470, 471, 473, 476, 477, 479], "excluded_lines": [], "executed_branches": [], "missing_branches": [[470, 471], [470, 473], [476, 477], [476, 479]]}, "UploadWidget._handle_upload_click": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [483, 484, 486, 489, 491, 492], "excluded_lines": [], "executed_branches": [], "missing_branches": [[484, 486], [484, 489]]}, "UploadWidget.start_upload": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [496, 497, 498, 499, 501, 502, 503, 505, 506, 509, 512, 513, 514, 515, 516, 518, 520, 521, 522, 523], "excluded_lines": [], "executed_branches": [], "missing_branches": [[497, 498], [497, 501], [501, 502], [501, 505], [512, 513], [512, 518]]}, "UploadWidget.cancel_upload": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [527, 528, 529, 532, 533, 534, 535, 538, 540, 542, 543], "excluded_lines": [], "executed_branches": [], "missing_branches": [[532, 533], [532, 538], [533, 532], [533, 534]]}, "UploadWidget.update_progress": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [552, 553, 554, 557, 558, 559, 562, 563, 564, 565, 566, 568, 569], "excluded_lines": [], "executed_branches": [], "missing_branches": [[553, -545], [553, 554], [562, -545], [562, 563]]}, "UploadWidget.get_file_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [573], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget.get_total_size": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [577], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget.get_status_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [581, 582, 583, 584, 586], "excluded_lines": [], "executed_branches": [], "missing_branches": [[582, 583], [582, 586]]}, "UploadWidget.add_upload_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [596], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget.add_progress_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [600], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 65, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 65, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [10, 11, 12, 13, 15, 18, 19, 21, 24, 26, 27, 28, 29, 30, 33, 39, 71, 82, 89, 101, 104, 111, 112, 116, 120, 127, 132, 138, 145, 159, 164, 167, 179, 183, 187, 191, 195, 205, 208, 215, 219, 223, 227, 231, 236, 242, 274, 283, 295, 296, 303, 355, 367, 416, 443, 461, 481, 494, 525, 545, 571, 575, 579, 594, 598], "excluded_lines": [], "executed_branches": [], "missing_branches": [[18, 19], [18, 21]]}}, "classes": {"FileStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FileItem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 33, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [46, 47, 48, 49, 50, 51, 52, 53, 56, 57, 58, 60, 62, 63, 64, 65, 66, 67, 68, 69, 73, 74, 76, 77, 78, 79, 80, 84, 85, 86, 87, 91, 98], "excluded_lines": [], "executed_branches": [], "missing_branches": [[63, -39], [63, 64], [73, 74], [73, 76], [76, 77], [76, 80], [77, 78], [77, 79]]}, "FileListWidget": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [106, 107, 108, 109, 114, 118, 122, 123, 124, 125, 129, 130, 134, 135, 136, 140, 141, 142, 143, 147, 148, 150, 157, 161], "excluded_lines": [], "executed_branches": [], "missing_branches": [[122, 123], [122, 125], [134, 135], [134, 136], [140, 141], [140, 143], [141, 140], [141, 142]]}, "UIButton": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [174, 175, 176, 177, 181, 185, 189, 193, 197, 198, 199, 200, 201, 202], "excluded_lines": [], "executed_branches": [], "missing_branches": [[197, -195], [197, 198], [198, -195], [198, 199]]}, "ProgressBar": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [210, 211, 212, 213, 217, 221, 225, 229, 233], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UploadWidget": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 173, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 173, "excluded_lines": 0, "num_branches": 48, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 48}, "missing_lines": [245, 246, 247, 248, 249, 250, 253, 254, 255, 258, 259, 260, 263, 264, 267, 270, 272, 277, 280, 281, 285, 298, 312, 314, 315, 316, 319, 320, 321, 324, 325, 326, 329, 330, 331, 334, 335, 336, 337, 339, 342, 343, 346, 348, 349, 351, 352, 353, 357, 358, 359, 360, 361, 362, 363, 364, 365, 376, 377, 380, 381, 382, 385, 386, 387, 390, 391, 392, 393, 396, 397, 398, 399, 400, 402, 403, 404, 406, 407, 408, 410, 412, 413, 414, 425, 426, 427, 428, 431, 433, 434, 436, 437, 439, 440, 441, 445, 446, 447, 450, 451, 454, 456, 458, 459, 463, 466, 467, 470, 471, 473, 476, 477, 479, 483, 484, 486, 489, 491, 492, 496, 497, 498, 499, 501, 502, 503, 505, 506, 509, 512, 513, 514, 515, 516, 518, 520, 521, 522, 523, 527, 528, 529, 532, 533, 534, 535, 538, 540, 542, 543, 552, 553, 554, 557, 558, 559, 562, 563, 564, 565, 566, 568, 569, 573, 577, 581, 582, 583, 584, 586, 596, 600], "excluded_lines": [], "executed_branches": [], "missing_branches": [[314, 315], [314, 319], [319, 320], [319, 324], [324, 325], [324, 329], [329, 330], [329, 334], [358, 359], [358, 365], [360, 358], [360, 361], [363, 358], [363, 364], [380, 381], [380, 385], [385, 386], [385, 390], [391, 392], [391, 396], [398, 399], [398, 402], [402, 403], [402, 410], [426, 427], [426, 436], [470, 471], [470, 473], [476, 477], [476, 479], [484, 486], [484, 489], [497, 498], [497, 501], [501, 502], [501, 505], [512, 513], [512, 518], [532, 533], [532, 538], [533, 532], [533, 534], [553, -545], [553, 554], [562, -545], [562, 563], [582, 583], [582, 586]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 65, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 65, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [10, 11, 12, 13, 15, 18, 19, 21, 24, 26, 27, 28, 29, 30, 33, 39, 71, 82, 89, 101, 104, 111, 112, 116, 120, 127, 132, 138, 145, 159, 164, 167, 179, 183, 187, 191, 195, 205, 208, 215, 219, 223, 227, 231, 236, 242, 274, 283, 295, 296, 303, 355, 367, 416, 443, 461, 481, 494, 525, 545, 571, 575, 579, 594, 598], "excluded_lines": [], "executed_branches": [], "missing_branches": [[18, 19], [18, 21]]}}}, "src\\voice_came\\utils\\__init__.py": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "src\\voice_came\\utils\\logger.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 23, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [9, 10, 11, 12, 15, 25, 27, 29, 32, 33, 36, 39, 42, 44, 47, 53, 54, 57, 58, 61, 64, 67, 68], "excluded_lines": [], "executed_branches": [], "missing_branches": [[27, 29], [27, 44], [53, 54], [53, 57]], "functions": {"get_logger": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [25, 27, 29, 32, 33, 36, 39, 42, 44], "excluded_lines": [], "executed_branches": [], "missing_branches": [[27, 29], [27, 44]]}, "setup_file_logging": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [53, 54, 57, 58, 61, 64, 67, 68], "excluded_lines": [], "executed_branches": [], "missing_branches": [[53, 54], [53, 57]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 10, 11, 12, 15, 47], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 23, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [9, 10, 11, 12, 15, 25, 27, 29, 32, 33, 36, 39, 42, 44, 47, 53, 54, 57, 58, 61, 64, 67, 68], "excluded_lines": [], "executed_branches": [], "missing_branches": [[27, 29], [27, 44], [53, 54], [53, 57]]}}}}, "totals": {"covered_lines": 17, "num_statements": 2522, "percent_covered": 0.5376344086021505, "percent_covered_display": "0.54", "missing_lines": 2505, "excluded_lines": 21, "num_branches": 640, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 640}}