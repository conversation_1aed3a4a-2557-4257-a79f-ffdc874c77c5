#!/usr/bin/env python3
"""
TDD度量实时监控脚本

定期收集和分析TDD度量指标，生成报告和警报
"""

import sys
import time
import logging
import argparse
import schedule
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.voice_came.metrics import (
    TDDMetricsCollector,
    MetricsReporter
)


class TDDMetricsMonitor:
    """TDD度量监控器"""
    
    def __init__(self, metrics_dir: str = "metrics"):
        self.metrics_dir = Path(metrics_dir)
        self.metrics_dir.mkdir(exist_ok=True)
        
        # 初始化组件
        self.collector = TDDMetricsCollector(metrics_dir)
        self.reporter = MetricsReporter(metrics_dir)
        
        # 设置日志
        self.setup_logging()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("TDD度量监控器已初始化")
    
    def setup_logging(self):
        """设置日志配置"""
        log_file = self.metrics_dir / "monitor.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def collect_and_report(self):
        """收集度量并生成报告"""
        try:
            self.logger.info("开始收集TDD度量指标...")
            
            # 收集当前度量
            current_metrics = self.collector.collect_current_metrics()
            self.collector.save_metrics(current_metrics)
            
            # 生成实时仪表盘数据
            dashboard_data = self.collector.generate_real_time_dashboard_data()
            
            # 生成日报
            daily_report = self.reporter.generate_daily_report(dashboard_data)
            
            # 检查警报
            self._check_and_send_alerts(dashboard_data)
            
            self.logger.info("度量收集和报告生成完成")
            
            return {
                'metrics': current_metrics,
                'dashboard': dashboard_data,
                'report': daily_report
            }
            
        except Exception as e:
            self.logger.error(f"收集度量时出错: {e}")
            return None
    
    def generate_weekly_report(self):
        """生成周报"""
        try:
            self.logger.info("生成周度报告...")
            
            weekly_report = self.reporter.generate_weekly_report()
            
            self.logger.info("周度报告生成完成")
            return weekly_report
            
        except Exception as e:
            self.logger.error(f"生成周报时出错: {e}")
            return None
    
    def _check_and_send_alerts(self, dashboard_data: dict):
        """检查并发送警报"""
        alerts = dashboard_data.get('alerts', [])
        
        if alerts:
            self.logger.warning(f"发现 {len(alerts)} 个警报:")
            for alert in alerts:
                self.logger.warning(f"  [{alert['level']}] {alert['message']}")
        
        # 这里可以集成邮件、Slack等通知系统
        self._save_alerts_to_file(alerts)
    
    def _save_alerts_to_file(self, alerts: list):
        """保存警报到文件"""
        if not alerts:
            return
        
        alerts_file = self.metrics_dir / "alerts.json"
        
        try:
            import json
            
            # 加载现有警报
            if alerts_file.exists():
                with open(alerts_file, 'r', encoding='utf-8') as f:
                    existing_alerts = json.load(f)
            else:
                existing_alerts = []
            
            # 添加时间戳
            for alert in alerts:
                alert['timestamp'] = datetime.now().isoformat()
            
            # 合并警报
            existing_alerts.extend(alerts)
            
            # 保留最近100个警报
            if len(existing_alerts) > 100:
                existing_alerts = existing_alerts[-100:]
            
            # 保存到文件
            with open(alerts_file, 'w', encoding='utf-8') as f:
                json.dump(existing_alerts, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"警报已保存到 {alerts_file}")
            
        except Exception as e:
            self.logger.error(f"保存警报时出错: {e}")
    
    def start_continuous_monitoring(self, interval_minutes: int = 30):
        """启动持续监控"""
        self.logger.info(f"启动持续监控，间隔 {interval_minutes} 分钟")
        
        # 安排定期任务
        schedule.every(interval_minutes).minutes.do(self.collect_and_report)
        schedule.every().day.at("09:00").do(self.generate_weekly_report)
        
        # 立即执行一次
        self.collect_and_report()
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次调度
                
        except KeyboardInterrupt:
            self.logger.info("监控已停止")
    
    def run_once(self):
        """执行一次度量收集"""
        self.logger.info("执行一次性度量收集")
        
        result = self.collect_and_report()
        
        if result:
            # 打印摘要
            metrics = result['metrics']
            print("\n=== TDD度量摘要 ===")
            print(f"测试覆盖率: {metrics.test_coverage:.1%}")
            print(f"测试通过率: {metrics.test_pass_rate:.1%}")
            print(f"代码质量评分: {metrics.code_quality_score:.1f}")
            print(f"TDD合规率: {metrics.compliance_rate:.1%}")
            print(f"性能评分: {metrics.performance_score:.1f}")
            
            # 显示警报
            alerts = result['dashboard'].get('alerts', [])
            if alerts:
                print(f"\n⚠️  发现 {len(alerts)} 个警报:")
                for alert in alerts:
                    print(f"  • {alert['message']}")
            
            # 显示建议
            recommendations = result['dashboard'].get('recommendations', [])
            if recommendations:
                print(f"\n💡 改进建议:")
                for rec in recommendations:
                    print(f"  • {rec}")
            
            print(f"\n📊 详细报告已保存到: {self.metrics_dir}/reports/")
        else:
            print("❌ 度量收集失败")
            return 1
        
        return 0
    
    def show_dashboard(self):
        """显示实时仪表盘"""
        try:
            dashboard_data = self.reporter.generate_dashboard_data()
            
            print("\n" + "="*60)
            print("           Voice-came TDD实时仪表盘")
            print("="*60)
            
            overview = dashboard_data.get('overview', {})
            health_score = overview.get('health_score', 0)
            status = overview.get('status', 'unknown')
            
            print(f"📊 整体健康评分: {health_score:.1f}/100 ({status.upper()})")
            
            # 关键指标
            key_metrics = overview.get('key_metrics', {})
            print(f"\n📈 关键指标:")
            print(f"  测试覆盖率: {key_metrics.get('coverage', 0):.1%}")
            print(f"  代码质量: {key_metrics.get('quality', 0):.1f}/100")
            print(f"  TDD合规性: {key_metrics.get('compliance', 0):.1%}")
            print(f"  性能指标: {key_metrics.get('performance', 0):.1f}/100")
            
            # 快速统计
            quick_stats = dashboard_data.get('quick_stats', {})
            print(f"\n⚡ 今日统计:")
            print(f"  运行测试: {quick_stats.get('tests_run_today', 0)} 次")
            print(f"  代码提交: {quick_stats.get('code_commits', 0)} 次")
            print(f"  覆盖率变化: {quick_stats.get('coverage_change', 'N/A')}")
            print(f"  质量趋势: {quick_stats.get('quality_trend', 'N/A')}")
            
            # 活跃警报
            alerts = dashboard_data.get('alerts', [])
            if alerts:
                print(f"\n⚠️  活跃警报 ({len(alerts)} 个):")
                for alert in alerts[:3]:  # 只显示前3个
                    print(f"  • {alert.get('message', 'N/A')}")
            else:
                print(f"\n✅ 无活跃警报")
            
            print("\n" + "="*60)
            print(f"最后更新: {dashboard_data.get('timestamp', 'N/A')}")
            print("="*60)
            
        except Exception as e:
            self.logger.error(f"显示仪表盘时出错: {e}")
            print("❌ 无法显示仪表盘")
            return 1
        
        return 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="TDD度量监控工具")
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 运行一次命令
    subparsers.add_parser('run', help='执行一次度量收集')
    
    # 持续监控命令
    monitor_parser = subparsers.add_parser('monitor', help='启动持续监控')
    monitor_parser.add_argument('--interval', type=int, default=30, 
                               help='监控间隔(分钟，默认30)')
    
    # 仪表盘命令
    subparsers.add_parser('dashboard', help='显示实时仪表盘')
    
    # 报告命令
    report_parser = subparsers.add_parser('report', help='生成报告')
    report_parser.add_argument('--type', choices=['daily', 'weekly'], 
                              default='daily', help='报告类型')
    
    # 配置参数
    parser.add_argument('--metrics-dir', default='metrics', 
                       help='度量数据目录')
    
    args = parser.parse_args()
    
    # 初始化监控器
    monitor = TDDMetricsMonitor(args.metrics_dir)
    
    try:
        if args.command == 'run':
            return monitor.run_once()
            
        elif args.command == 'monitor':
            monitor.start_continuous_monitoring(args.interval)
            return 0
            
        elif args.command == 'dashboard':
            return monitor.show_dashboard()
            
        elif args.command == 'report':
            if args.type == 'daily':
                result = monitor.collect_and_report()
                if result:
                    print("✅ 日报生成完成")
                    return 0
                else:
                    print("❌ 日报生成失败")
                    return 1
            elif args.type == 'weekly':
                result = monitor.generate_weekly_report()
                if result:
                    print("✅ 周报生成完成")
                    return 0
                else:
                    print("❌ 周报生成失败")
                    return 1
        else:
            parser.print_help()
            return 1
            
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 