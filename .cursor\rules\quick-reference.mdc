---
description: 
globs: 
alwaysApply: false
---
# Voice-came Quick Reference

## Project Summary
Voice-came is a sleep content globalization tool that extracts effective speech from 3-12 hour videos and translates to multiple languages using WhisperX + local translation models.

## Key Files to Know
- **Development Standards**: [Voice-came_开发规范_v1.0.md](mdc:Voice-came_开发规范_v1.0.md) - Complete coding standards
- **Product Requirements**: [Voice-came_PRD_v4.1.md](mdc:Voice-came_PRD_v4.1.md) - Feature specs and requirements  
- **Technical Implementation**: [WhisperX集成方案.md](mdc:WhisperX集成方案.md) - WhisperX integration details

## Core Architecture
```
Video → WhisperX (Speech Recognition) → Translation Models (Gemma3/Qwen3) → Export (SRT/TXT)
```

## Essential Commands
```bash
# Setup environment
python -m venv venv && source venv/bin/activate
pip install -r requirements.txt

# Run tests (TDD approach)
pytest tests/unit/ -v --cov=voice_came
pytest tests/integration/ -v

# Code quality
black voice_came/
isort voice_came/
flake8 voice_came/
```

## File Placement Rules
- **Core logic**: `voice_came/core/` (business logic)
- **Engines**: `voice_came/engines/` (WhisperX, translation)
- **Utils**: `voice_came/utils/` (helper functions)
- **Config**: `voice_came/config/` (YAML/JSON configs)
- **Tests**: `tests/` (unit, integration, e2e)

## Naming Conventions
- Files: `snake_case.py`
- Classes: `PascalCase`
- Functions: `snake_case`
- Constants: `UPPER_SNAKE_CASE`

## Key Performance Targets
- **Speed**: 3-hour video ≤ 30 minutes processing
- **Memory**: GPU usage < 8GB
- **Accuracy**: 90% terminology accuracy
- **Scale**: 100+ video batch processing

## MVP Timeline (4 Weeks)
1. **Week 1**: WhisperX integration + batch processing
2. **Week 2**: Translation models (Gemma3/Qwen3)
3. **Week 3**: File management + export system
4. **Week 4**: Optimization + release prep

## Quality Gates
- **Coverage**: ≥80% test coverage
- **Performance**: Meet speed targets
- **Stability**: 100 consecutive video processing
- **User Experience**: ≥4.0/5.0 satisfaction

## Common Patterns
```python
# File header template
"""
Voice-came [Module Name]
Brief description of functionality.
Author: Voice-came Team
"""

# Error handling pattern
try:
    result = process_function(input)
except SpecificError:
    logger.error(f"Specific error: {input}")
    raise
except Exception as e:
    logger.error(f"General error: {e}")
    raise ProcessingError(f"Failed: {e}") from e

# TDD test pattern
def test_function_behavior():
    # Arrange
    input_data = create_test_data()
    # Act
    result = function_to_test(input_data)
    # Assert
    assert result == expected_outcome
```

## Critical Dependencies
- **WhisperX**: Speech recognition (70x real-time speed)
- **Gemma3-12B**: Primary translation model
- **Qwen3**: Fallback translation model
- **PyTorch**: ML framework
- **CUDA**: GPU acceleration

## Emergency Contacts
- Technical issues: Project GitHub Issues
- Specification questions: Reference PRD document
- Standards questions: Reference development standards

