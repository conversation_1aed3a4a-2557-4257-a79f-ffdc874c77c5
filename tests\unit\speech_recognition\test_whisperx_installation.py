#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhisperX安装验证测试

TDD-Red阶段：这些测试应该初始失败，直到实现对应功能
"""

import pytest
import sys
import importlib
import subprocess
from unittest.mock import patch, MagicMock

class TestWhisperXInstallation:
    """WhisperX安装验证测试类"""
    
    @pytest.mark.tdd_red
    def test_whisperx_library_installed(self):
        """测试WhisperX库是否已安装"""
        try:
            import whisperx
            assert whisperx is not None
            assert hasattr(whisperx, 'load_model')
            assert hasattr(whisperx, 'load_audio')
            assert hasattr(whisperx, 'align')
        except ImportError:
            pytest.fail("WhisperX库未安装或导入失败")
    
    @pytest.mark.tdd_red  
    def test_torch_availability(self):
        """测试PyTorch是否可用"""
        try:
            import torch
            assert torch is not None
            # 测试CUDA是否可用
            cuda_available = torch.cuda.is_available()
            assert isinstance(cuda_available, bool)
        except ImportError:
            pytest.fail("PyTorch未安装或导入失败")
    
    @pytest.mark.tdd_red
    def test_whisperx_dependencies(self):
        """测试WhisperX相关依赖是否完整"""
        required_packages = [
            'transformers',
            'librosa', 
            'soundfile',
            'pyannote.audio',
            'faster_whisper'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                importlib.import_module(package)
            except ImportError:
                missing_packages.append(package)
        
        assert len(missing_packages) == 0, f"缺少依赖包: {missing_packages}"
    
    @pytest.mark.tdd_red
    def test_whisperx_version_compatibility(self):
        """测试WhisperX版本兼容性"""
        try:
            import whisperx
            # 检查是否有版本信息
            if hasattr(whisperx, '__version__'):
                version = whisperx.__version__
                # 期望版本 >= 3.1.0
                version_parts = version.split('.')
                major, minor = int(version_parts[0]), int(version_parts[1])
                assert major >= 3 and minor >= 1, f"WhisperX版本过低: {version}"
        except ImportError:
            pytest.fail("无法导入WhisperX检查版本")
    
    @pytest.mark.tdd_red
    def test_audio_processing_libraries(self):
        """测试音频处理库是否可用"""
        audio_libs = ['librosa', 'soundfile', 'audioread']
        
        for lib in audio_libs:
            try:
                importlib.import_module(lib)
            except ImportError:
                pytest.fail(f"音频处理库 {lib} 未安装")
    
    @pytest.mark.tdd_red
    def test_model_directory_creation(self):
        """测试模型缓存目录是否可创建"""
        import tempfile
        import os
        
        # 测试创建模型缓存目录
        with tempfile.TemporaryDirectory() as tmpdir:
            model_cache_dir = os.path.join(tmpdir, '.cache', 'whisperx')
            os.makedirs(model_cache_dir, exist_ok=True)
            assert os.path.exists(model_cache_dir)
            assert os.path.isdir(model_cache_dir)
    
    @pytest.mark.tdd_red
    def test_huggingface_hub_access(self):
        """测试Hugging Face Hub访问权限"""
        try:
            from huggingface_hub import hf_hub_download
            # 这个测试可能需要网络连接，在CI中可能需要Mock
            assert hf_hub_download is not None
        except ImportError:
            pytest.fail("huggingface_hub未安装，无法下载模型")


class TestWhisperXInstallationEnvironment:
    """WhisperX安装环境测试类"""
    
    @pytest.mark.tdd_red
    def test_gpu_memory_check(self):
        """测试GPU内存是否足够运行WhisperX"""
        try:
            import torch
            if torch.cuda.is_available():
                # 获取GPU内存信息
                gpu_memory = torch.cuda.get_device_properties(0).total_memory
                # WhisperX large-v2模型大约需要8GB GPU内存
                min_required_memory = 8 * 1024 * 1024 * 1024  # 8GB in bytes
                assert gpu_memory >= min_required_memory, f"GPU内存不足: {gpu_memory / 1024**3:.1f}GB < 8GB"
            else:
                # CPU模式下跳过此测试
                pytest.skip("CUDA不可用，跳过GPU内存检查")
        except Exception as e:
            pytest.fail(f"GPU内存检查失败: {e}")
    
    @pytest.mark.tdd_red
    def test_system_resources(self):
        """测试系统资源是否足够"""
        import psutil
        
        # 检查系统内存
        memory = psutil.virtual_memory()
        min_ram_gb = 16  # 推荐16GB RAM
        available_ram_gb = memory.available / (1024**3)
        
        assert available_ram_gb >= min_ram_gb * 0.5, f"可用内存不足: {available_ram_gb:.1f}GB"
        
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        min_disk_gb = 20  # 至少20GB用于模型和缓存
        available_disk_gb = disk.free / (1024**3)
        
        assert available_disk_gb >= min_disk_gb, f"可用磁盘空间不足: {available_disk_gb:.1f}GB"
    
    @pytest.mark.tdd_red 
    def test_python_version_compatibility(self):
        """测试Python版本兼容性"""
        python_version = sys.version_info
        
        # WhisperX需要Python 3.8+
        assert python_version.major == 3, "需要Python 3.x版本"
        assert python_version.minor >= 8, f"Python版本过低: {python_version.major}.{python_version.minor}, 需要3.8+"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"]) 